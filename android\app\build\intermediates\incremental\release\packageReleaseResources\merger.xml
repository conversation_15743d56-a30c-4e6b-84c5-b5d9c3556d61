<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\src\main\res"><file name="ic_launcher_background" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="notification_icon" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-hdpi\notification_icon.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-hdpi\splashscreen_logo.png" qualifiers="hdpi-v4" type="drawable"/><file name="notification_icon" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-mdpi\notification_icon.png" qualifiers="mdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-mdpi\splashscreen_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="notification_icon" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-xhdpi\notification_icon.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-xhdpi\splashscreen_logo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="notification_icon" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-xxhdpi\notification_icon.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-xxhdpi\splashscreen_logo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="notification_icon" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-xxxhdpi\notification_icon.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\drawable-xxxhdpi\splashscreen_logo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="splashscreen_background">#ffffff</color><color name="iconBackground">#FFFFFF</color><color name="colorPrimary">#023c69</color><color name="colorPrimaryDark">#ffffff</color><color name="notification_icon_color">#ffffff</color></file><file path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">TecBiz Associado</string><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string></file><file path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:enforceNavigationBarContrast" ns1:targetApi="29">true</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style><style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style></file><file path="D:\Projetos\TecBizExpoApp\android\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\resValues\release"/><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\createBundleReleaseJsAndAssets"/><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\resValues\release"><file path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\resValues\release\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer><string name="react_native_dev_server_ip" translatable="false">*************</string></file></source><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\createBundleReleaseJsAndAssets"/><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\processReleaseGoogleServices"><file path="D:\Projetos\TecBizExpoApp\android\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">608372388905</string><string name="google_api_key" translatable="false">AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk</string><string name="google_app_id" translatable="false">1:608372388905:android:25e23e2d6867cb314ad838</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk</string><string name="google_storage_bucket" translatable="false">tecbizappass.firebasestorage.app</string><string name="project_id" translatable="false">tecbizappass</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-createBundleReleaseJsAndAssets" generated-set="res-createBundleReleaseJsAndAssets$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>