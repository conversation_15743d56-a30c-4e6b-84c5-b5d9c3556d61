{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7da1abaface34e688008971d372cf60e\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "248,612,613,614,615,3411,3413,3414,3419,3421", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "14778,38916,38969,39022,39075,231219,231395,231517,231779,231974", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "14862,38964,39017,39070,39123,231280,231512,231573,231840,232036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\b4f01381b71eb2d1673f09999325396c\\transformed\\activity-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "707,753", "startColumns": "4,4", "startOffsets": "44175,46459", "endColumns": "41,59", "endOffsets": "44212,46514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "766,874", "startColumns": "4,4", "startOffsets": "47246,57458", "endColumns": "67,166", "endOffsets": "47309,57620"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "149,150,151,152,354,355,885,899,900,901", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "8189,8247,8313,8376,21960,22031,58827,60094,60161,60240", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "8242,8308,8371,8433,22026,22098,58890,60156,60235,60304"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,4,5", "startColumns": "4,4,4", "startOffsets": "55,157,213", "endColumns": "55,55,54", "endOffsets": "106,208,263"}, "to": {"startLines": "677,1008,1009", "startColumns": "4,4,4", "startOffsets": "42667,74453,74509", "endColumns": "55,55,54", "endOffsets": "42718,74504,74559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\17a21103ff574ee4212c28297f5d53b4\\transformed\\transition-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "682,683,701,710,711,740,741,742,743,744", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "42929,42969,43882,44294,44349,45835,45889,45941,45990,46051", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "42964,43011,43920,44344,44391,45884,45936,45985,46046,46096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\341d65447503e294f53c11a98be04379\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "755", "startColumns": "4", "startOffsets": "46573", "endColumns": "49", "endOffsets": "46618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8016bd49cd6c51dfc99846b2ac930aa7\\transformed\\material-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,439,443,447,454,461,469,479,488,497,506,507,508,513,514,518,519,525,529,530,531,532,542,543,544,548,549,555,559,560,561,562,563,618,681,726,789,843,906,954,999,1062,1066,1070,1082,1097,1111,1112,1160,1164,1165,1166,1169,1182,1195,1235,1236,1237,1249,1253,1258,1263,1268,1271,1274,1277,1281,1285,1286,1287,1288,1289,1292,1295,1298,1301,1305,1309,1310,1313,1316,1319,1322,1326,1329,1332,1335,1338,1341,1344,1348,1351,1354,1358,1361,1371,1379,1387,1390,1393,1396,1399,1402,1405,1408,1409,1412,1415,1416,1419,1420,1421,1425,1426,1431,1432,1440,1448,1449,1457,1461,1469,1477,1485,1493,1501,1502,1510,1518,1519,1522,1524,1529,1531,1536,1540,1544,1545,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1574,1575,1576,1582,1583,1587,1589,1590,1593,1598,1599,1600,1601,1602,1603,1607,1608,1609,1615,1616,1620,1622,1625,1629,1633,1637,1641,1642,1643,1644,1652,1660,1663,1668,1673,1678,1683,1687,1691,1700,1708,1709,1710,1711,1712,1720,1729,1734,1739,1740,1741,1742,1760,1764,1769,1772,1776,1779,1783,1787,1791,1794,1802,1811,1824,1828,1843,1851,1854,1865,1870,1874,1909,1913,1914,1921,1925,1926,1927,1930,1934,1938,1939,1943,1948,1963,1967,1968,1980,1990,1991,2001,2006,2029,2032,2038,2041,2050,2058,2062,2065,2068,2071,2075,2078,2093,2097,2100,2115,2118,2126,2131,2137,2143,2149,2179,2190,2207,2214,2217,2229,2238,2242,2247,2251,2255,2259,2263,2267,2270,2279,2284,2293,2297,2304,2313,2319,2323,2345,2346,2347,2348,2349,2353,2354,2363,2367,2379,2391,2398,2399,2403,2407,2408,2412,2426,2432,2438,2444,2450,2455,2461,2467,2468,2477,2485,2486,2493,2501,2526,2538,2577,2597,2631,2680,2737,2848,2874,2968,2983,2995,3001,3047,3051,3057,3063,3070,3076,3083,3086,3147,3159,3170,3182,3210,3219,3228,3234,3242,3247,3295,3298,3301,3305,3343,3355,3400,3408,3431,3438,3446,3531,3536,3776,3792", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,206,255,311,371,432,487,537,587,640,698,746,783,830,879,935,986,1035,1089,1143,1192,1248,1295,1351,1411,1472,1544,1608,1661,1713,1771,1836,1884,1950,2008,2077,2135,2205,2279,2347,2414,2484,2550,2623,2683,2743,2803,2862,2919,2977,3035,3081,3141,3195,3263,3332,3400,3453,3505,3555,3601,3651,3698,3756,3814,3873,3933,3995,4057,4119,4181,4243,4305,4373,4434,4496,4558,4611,4665,4716,4761,4825,4884,4946,5020,5091,5157,5231,5300,5371,5444,5515,5583,5656,5732,5802,5880,5948,6014,6075,6144,6208,6274,6342,6408,6471,6539,6610,6675,6748,6811,6892,6956,7022,7092,7162,7232,7302,7369,7434,7500,7553,7629,7695,7782,7858,7934,7981,8046,8096,8150,8229,8307,8380,8445,8508,8574,8645,8716,8778,8847,8913,8973,9040,9107,9163,9214,9267,9319,9373,9444,9507,9566,9628,9687,9760,9827,9887,9950,10025,10097,10193,10264,10320,10391,10448,10505,10571,10635,10706,10763,10816,10879,10931,10989,11056,11140,11225,11306,11374,11451,11524,11594,11666,11736,11809,11873,11943,11991,12060,12112,12170,12236,12303,12369,12450,12525,12581,12634,12695,12753,12803,12852,12901,12950,13012,13064,13109,13190,13244,13297,13351,13402,13451,13502,13563,13624,13686,13736,13777,13836,13895,13954,14015,14071,14127,14194,14255,14320,14375,14440,14509,14577,14655,14724,14784,14855,14929,14994,15066,15136,15203,15287,15356,15423,15493,15556,15623,15691,15774,15853,15943,16020,16088,16155,16233,16290,16347,16415,16481,16537,16597,16656,16710,16760,16810,16858,16920,16971,17037,17110,17190,17270,17334,17397,17464,17535,17593,17654,17720,17779,17846,17906,17966,18029,18097,18158,18225,18303,18373,18422,18479,18548,18609,18697,18785,18873,18961,19017,19104,19191,19278,19365,19423,19479,19550,19615,19677,19752,19825,19894,19964,20033,20088,20144,20200,20261,20319,20375,20430,20492,20545,20602,20696,20765,20866,20917,20975,21045,21114,21184,21254,21324,21391,21458,21533,21600,21659,21713,21767,21821,21874,21926,22000,22065,22121,22179,22241,22296,22339,22384,22427,22474,22519,22570,22621,22672,22723,22771,22837,22899,22962,23034,23091,23154,23211,23271,23336,23403,23468,23525,23586,23644,23714,23771,24091,24241,24372,24470,24585,24670,24718,24797,24862,24951,25108,25265,25418,25572,25631,25805,25983,26161,26343,26660,26842,27024,27214,27404,27603,27776,27886,28071,28208,28428,28612,28772,28930,29114,29317,29488,29708,29930,30085,30292,30476,30579,30720,30885,31056,31256,31460,31662,31867,32068,32267,32471,32549,32850,33016,33171,33273,33407,33684,33969,34359,34815,35324,35866,36331,36793,37264,37357,37464,37807,37914,38159,38280,38689,38937,39037,39142,39261,39795,39942,40061,40312,40445,40860,41114,41226,41347,41480,41627,45714,49885,53249,57434,61428,65562,68582,71862,76010,76262,76527,77587,78434,79354,79445,81633,81843,81952,82071,82255,83185,83931,86428,86523,86554,87424,87710,88113,88515,88858,89070,89271,89484,89773,90058,90131,90218,90303,90402,90522,90683,90846,91007,91172,91339,91392,91525,91645,91743,91856,92049,92175,92327,92469,92639,92795,92967,93258,93370,93499,93728,93946,94801,95388,96002,96170,96312,96473,96616,96784,96941,97136,97228,97401,97563,97658,97827,97921,98010,98253,98342,98635,98749,99158,99572,99688,100106,100347,100777,101212,101622,102044,102454,102576,102985,103401,103523,103707,103775,104119,104199,104555,104705,104849,104933,105322,105420,105528,105622,105752,105860,105982,106118,106226,106346,106480,106602,106730,106872,106998,107138,107264,107382,107514,107612,107722,108022,108134,108252,108716,108832,109135,109261,109357,109487,109888,109998,110122,110260,110370,110492,110804,110928,111058,111534,111662,111977,112115,112261,112423,112639,112795,112999,113067,113151,113255,113817,114448,114606,114825,115056,115279,115514,115736,116002,116594,117193,117307,117451,117563,117687,118258,118856,119351,119897,120042,120135,120227,121588,121976,122274,122463,122669,122862,123072,123289,123550,123681,124113,124637,125281,125478,126426,126983,127106,127879,128100,128300,130277,130512,130636,131144,131358,131461,131591,131766,132013,132204,132344,132538,132808,133689,133977,134107,134872,135517,135663,136224,136462,137935,138085,138502,138667,139353,139823,140019,140181,140336,140480,140714,140881,141655,141941,142101,142716,142875,143203,143430,143795,144166,144527,146281,146910,147986,148506,148658,149648,150385,150588,150834,151081,151323,151644,151949,152172,152344,152885,153154,153648,153909,154349,155094,155459,155764,157464,157570,157700,157838,157962,158208,158308,158944,159245,160106,160861,161300,161424,161665,161853,161987,162178,162957,163226,163517,163796,164113,164335,164630,164913,165017,165664,166229,166349,166846,167380,169004,169715,171763,172605,174529,177450,181203,186428,187706,193536,194244,194931,195315,197485,197716,198020,198337,198794,199101,199531,199690,202899,203517,204046,204541,205841,206332,206826,207199,207541,207749,210970,211081,211218,211453,213307,213855,216074,216515,217745,218166,218482,223124,223389,236918,237987", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,438,442,446,453,460,468,478,487,496,505,506,507,512,513,517,518,524,528,529,530,531,541,542,543,547,548,554,558,559,560,561,562,617,680,725,788,842,905,953,998,1061,1065,1069,1081,1096,1110,1111,1159,1163,1164,1165,1168,1181,1194,1234,1235,1236,1248,1252,1257,1262,1267,1270,1273,1276,1280,1284,1285,1286,1287,1288,1291,1294,1297,1300,1304,1308,1309,1312,1315,1318,1321,1325,1328,1331,1334,1337,1340,1343,1347,1350,1353,1357,1360,1370,1378,1386,1389,1392,1395,1398,1401,1404,1407,1408,1411,1414,1415,1418,1419,1420,1424,1425,1430,1431,1439,1447,1448,1456,1460,1468,1476,1484,1492,1500,1501,1509,1517,1518,1521,1523,1528,1530,1535,1539,1543,1544,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1573,1574,1575,1581,1582,1586,1588,1589,1592,1597,1598,1599,1600,1601,1602,1606,1607,1608,1614,1615,1619,1621,1624,1628,1632,1636,1640,1641,1642,1643,1651,1659,1662,1667,1672,1677,1682,1686,1690,1699,1707,1708,1709,1710,1711,1719,1728,1733,1738,1739,1740,1741,1759,1763,1768,1771,1775,1778,1782,1786,1790,1793,1801,1810,1823,1827,1842,1850,1853,1864,1869,1873,1908,1912,1913,1920,1924,1925,1926,1929,1933,1937,1938,1942,1947,1962,1966,1967,1979,1989,1990,2000,2005,2028,2031,2037,2040,2049,2057,2061,2064,2067,2070,2074,2077,2092,2096,2099,2114,2117,2125,2130,2136,2142,2148,2178,2189,2206,2213,2216,2228,2237,2241,2246,2250,2254,2258,2262,2266,2269,2278,2283,2292,2296,2303,2312,2318,2322,2344,2345,2346,2347,2348,2352,2353,2362,2366,2378,2390,2397,2398,2402,2406,2407,2411,2425,2431,2437,2443,2449,2454,2460,2466,2467,2476,2484,2485,2492,2500,2525,2537,2576,2596,2630,2679,2736,2847,2873,2967,2982,2994,3000,3046,3050,3056,3062,3069,3075,3082,3085,3146,3158,3169,3181,3209,3218,3227,3233,3241,3246,3294,3297,3300,3304,3342,3354,3399,3407,3430,3437,3445,3530,3535,3775,3791,3800", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,36,46,48,55,50,48,53,53,48,55,46,55,59,60,71,63,52,51,57,64,47,65,57,68,57,69,73,67,66,69,65,72,59,59,59,58,56,57,57,45,59,53,67,68,67,52,51,49,45,49,46,57,57,58,59,61,61,61,61,61,61,67,60,61,61,52,53,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,64,65,52,75,65,86,75,75,46,64,49,53,78,77,72,64,62,65,70,70,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,83,84,80,67,76,72,69,71,69,72,63,69,47,68,51,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,65,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,55,70,64,61,74,72,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,64,55,57,61,54,42,44,42,46,44,50,50,50,50,47,65,61,62,71,56,62,56,59,64,66,64,56,60,57,69,56,12,149,130,97,114,84,47,78,64,88,156,156,152,153,58,173,177,177,181,316,181,181,189,189,198,172,109,184,136,219,183,159,157,183,202,170,219,221,154,206,183,102,140,164,170,199,203,201,204,200,198,203,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,108,118,10,10,10,10,94,30,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,10,10,10,113,143,111,123,10,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,129,137,123,10,99,10,10,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,119,10,10,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "201,250,306,366,427,482,532,582,635,693,741,778,825,874,930,981,1030,1084,1138,1187,1243,1290,1346,1406,1467,1539,1603,1656,1708,1766,1831,1879,1945,2003,2072,2130,2200,2274,2342,2409,2479,2545,2618,2678,2738,2798,2857,2914,2972,3030,3076,3136,3190,3258,3327,3395,3448,3500,3550,3596,3646,3693,3751,3809,3868,3928,3990,4052,4114,4176,4238,4300,4368,4429,4491,4553,4606,4660,4711,4756,4820,4879,4941,5015,5086,5152,5226,5295,5366,5439,5510,5578,5651,5727,5797,5875,5943,6009,6070,6139,6203,6269,6337,6403,6466,6534,6605,6670,6743,6806,6887,6951,7017,7087,7157,7227,7297,7364,7429,7495,7548,7624,7690,7777,7853,7929,7976,8041,8091,8145,8224,8302,8375,8440,8503,8569,8640,8711,8773,8842,8908,8968,9035,9102,9158,9209,9262,9314,9368,9439,9502,9561,9623,9682,9755,9822,9882,9945,10020,10092,10188,10259,10315,10386,10443,10500,10566,10630,10701,10758,10811,10874,10926,10984,11051,11135,11220,11301,11369,11446,11519,11589,11661,11731,11804,11868,11938,11986,12055,12107,12165,12231,12298,12364,12445,12520,12576,12629,12690,12748,12798,12847,12896,12945,13007,13059,13104,13185,13239,13292,13346,13397,13446,13497,13558,13619,13681,13731,13772,13831,13890,13949,14010,14066,14122,14189,14250,14315,14370,14435,14504,14572,14650,14719,14779,14850,14924,14989,15061,15131,15198,15282,15351,15418,15488,15551,15618,15686,15769,15848,15938,16015,16083,16150,16228,16285,16342,16410,16476,16532,16592,16651,16705,16755,16805,16853,16915,16966,17032,17105,17185,17265,17329,17392,17459,17530,17588,17649,17715,17774,17841,17901,17961,18024,18092,18153,18220,18298,18368,18417,18474,18543,18604,18692,18780,18868,18956,19012,19099,19186,19273,19360,19418,19474,19545,19610,19672,19747,19820,19889,19959,20028,20083,20139,20195,20256,20314,20370,20425,20487,20540,20597,20691,20760,20861,20912,20970,21040,21109,21179,21249,21319,21386,21453,21528,21595,21654,21708,21762,21816,21869,21921,21995,22060,22116,22174,22236,22291,22334,22379,22422,22469,22514,22565,22616,22667,22718,22766,22832,22894,22957,23029,23086,23149,23206,23266,23331,23398,23463,23520,23581,23639,23709,23766,24086,24236,24367,24465,24580,24665,24713,24792,24857,24946,25103,25260,25413,25567,25626,25800,25978,26156,26338,26655,26837,27019,27209,27399,27598,27771,27881,28066,28203,28423,28607,28767,28925,29109,29312,29483,29703,29925,30080,30287,30471,30574,30715,30880,31051,31251,31455,31657,31862,32063,32262,32466,32544,32845,33011,33166,33268,33402,33679,33964,34354,34810,35319,35861,36326,36788,37259,37352,37459,37802,37909,38154,38275,38684,38932,39032,39137,39256,39790,39937,40056,40307,40440,40855,41109,41221,41342,41475,41622,45709,49880,53244,57429,61423,65557,68577,71857,76005,76257,76522,77582,78429,79349,79440,81628,81838,81947,82066,82250,83180,83926,86423,86518,86549,87419,87705,88108,88510,88853,89065,89266,89479,89768,90053,90126,90213,90298,90397,90517,90678,90841,91002,91167,91334,91387,91520,91640,91738,91851,92044,92170,92322,92464,92634,92790,92962,93253,93365,93494,93723,93941,94796,95383,95997,96165,96307,96468,96611,96779,96936,97131,97223,97396,97558,97653,97822,97916,98005,98248,98337,98630,98744,99153,99567,99683,100101,100342,100772,101207,101617,102039,102449,102571,102980,103396,103518,103702,103770,104114,104194,104550,104700,104844,104928,105317,105415,105523,105617,105747,105855,105977,106113,106221,106341,106475,106597,106725,106867,106993,107133,107259,107377,107509,107607,107717,108017,108129,108247,108711,108827,109130,109256,109352,109482,109883,109993,110117,110255,110365,110487,110799,110923,111053,111529,111657,111972,112110,112256,112418,112634,112790,112994,113062,113146,113250,113812,114443,114601,114820,115051,115274,115509,115731,115997,116589,117188,117302,117446,117558,117682,118253,118851,119346,119892,120037,120130,120222,121583,121971,122269,122458,122664,122857,123067,123284,123545,123676,124108,124632,125276,125473,126421,126978,127101,127874,128095,128295,130272,130507,130631,131139,131353,131456,131586,131761,132008,132199,132339,132533,132803,133684,133972,134102,134867,135512,135658,136219,136457,137930,138080,138497,138662,139348,139818,140014,140176,140331,140475,140709,140876,141650,141936,142096,142711,142870,143198,143425,143790,144161,144522,146276,146905,147981,148501,148653,149643,150380,150583,150829,151076,151318,151639,151944,152167,152339,152880,153149,153643,153904,154344,155089,155454,155759,157459,157565,157695,157833,157957,158203,158303,158939,159240,160101,160856,161295,161419,161660,161848,161982,162173,162952,163221,163512,163791,164108,164330,164625,164908,165012,165659,166224,166344,166841,167375,168999,169710,171758,172600,174524,177445,181198,186423,187701,193531,194239,194926,195310,197480,197711,198015,198332,198789,199096,199526,199685,202894,203512,204041,204536,205836,206327,206821,207194,207536,207744,210965,211076,211213,211448,213302,213850,216069,216510,217740,218161,218477,223119,223384,236913,237982,238316"}, "to": {"startLines": "36,37,38,39,40,41,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,62,63,64,65,67,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,122,123,124,125,128,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,237,238,239,240,241,242,243,244,351,352,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,616,695,696,697,698,709,712,713,731,732,733,734,735,736,748,760,761,764,765,767,768,769,770,771,772,773,774,775,776,777,779,781,815,819,858,859,860,861,862,890,894,895,896,922,923,928,930,931,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,972,973,974,975,976,1019,1033,1037,1086,1093,1100,1270,1280,1289,1298,1367,1368,1369,1374,1375,1379,1380,1386,1390,1391,1392,1393,1403,1404,1405,1409,1410,1416,1420,1490,1491,1492,1493,1548,1611,1656,1719,1773,1836,1884,1929,1992,1996,2000,2772,2787,2801,2802,2850,2854,2855,2856,2859,2872,2885,2925,2952,2957,2969,2973,2978,2983,2988,2991,2994,2997,3001,3148,3149,3150,3151,3228,3231,3234,3237,3240,3244,3248,3249,3252,3255,3258,3261,3265,3268,3271,3274,3277,3280,3283,3287,3290,3299,3303,3306,3316,3324,3332,3335,3338,3341,3344,3347,3422,3425,3426,3429,3432,3433,3436,3437,3438,3442,3443,3448,3449,3457,3465,3466,3474,3478,3486,3494,3502,3510,3518,3519,3527,3535,3536,3612,3614,3619,3621,3626,3630,3659,3660,3665,3666,3667,3668,3669,3670,3671,3672,3673,3674,3675,3676,3677,3678,3679,3680,3681,3682,3683,3684,3685,3689,3690,3691,3697,3698,3702,3704,3705,3708,3713,3714,3715,3716,3717,3718,3722,3723,3724,3730,3731,3735,3737,3740,3744,3748,3752,3780,3781,3782,3783,3791,3799,3802,3807,3812,3817,3822,3826,3830,3839,3847,3848,3849,3850,3851,3859,3868,3873,3878,3879,3880,3881,3899,3903,3908,3911,3915,3918,3922,3926,3930,4081,4089,4098,4111,4115,4130,4138,4141,4152,4157,4161,4196,4200,4201,4208,4212,4213,4214,4217,4221,4225,4226,4230,4235,4250,4254,4255,4267,4277,4278,4288,4293,4316,4319,4325,4328,4337,4345,4349,4352,4355,4358,4362,4365,4380,4384,4387,4402,4405,4413,4418,4424,4430,4436,4466,4477,4494,4501,4504,4516,4525,4529,4534,4538,4542,4546,4550,4554,4557,4566,4571,4580,4584,4591,4600,4606,4610,4628,4629,4630,4631,4632,4636,4637,4645,4649,4661,4673,4680,4681,4685,4689,4690,4694,4708,4714,4720,4726,4732,4737,4743,4749,4750,4759,4767,4768,4775,4949,4964,4974,5521,5541,5575,5610,5722,5829,5849,5930,6090,6102,6108,6152,6156,6218,6406,6461,6467,6474,6477,6526,6535,6546,6558,6581,6587,6593,6599,6604,6808,6860,6890,6893,6932,6970,7081,7124,7132,7254,7258,7266,7362,7366,7580,7643", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1295,1351,1400,1456,1516,1577,1684,1734,1784,1837,1895,1943,1980,2027,2076,2132,2183,2232,2286,2340,2389,2445,2608,2664,2724,2785,2857,2966,3068,3120,3178,3243,3291,3357,3415,3484,3542,3612,3686,3754,3821,3891,3957,4030,4090,4150,4210,4269,4326,4444,4558,4604,4664,4718,4786,4855,4923,4976,5028,5078,5124,5174,5221,5279,5337,5396,5456,5518,5580,5642,5704,5766,5828,5896,5957,6019,6081,6134,6229,6280,6325,6389,6565,9828,9902,9973,10039,10113,10182,10253,10326,10397,10465,10538,10614,10684,10762,10830,10896,10957,11026,11090,11156,11224,11290,11353,11421,11492,11557,11630,11693,11774,11838,11904,11974,12044,12114,12184,14023,14088,14154,14207,14283,14349,14436,14512,21780,21827,22761,22811,22865,22944,23022,23095,23160,23223,23289,23360,23431,23493,23562,23628,23688,23755,23822,23878,23929,23982,24034,24088,24159,24222,24281,24343,24402,24475,24542,24602,24665,24740,24812,24908,24979,25035,25106,25163,25220,25286,25350,25421,25478,25531,25594,25646,25704,26994,27078,27163,27244,27312,27389,27462,27532,27604,27674,27747,27811,27881,27929,27998,28050,28108,28174,28241,28307,28388,28463,28519,28572,28633,28691,28741,28790,28839,28888,28950,29002,29047,29128,29182,29235,29289,29340,29389,29440,29501,29562,29624,29674,29715,29774,29833,29892,29953,30009,30065,30132,30193,30258,30313,30378,30447,30515,30593,30662,30722,30793,30867,30932,31004,31074,31141,31225,31294,31361,31431,31494,31561,31629,31712,31791,31881,31958,32026,32093,32171,32228,32285,32353,32419,32475,32535,32594,32648,32698,32748,32796,32858,32909,32975,33048,33128,33208,33272,33335,33402,33473,33531,33592,33658,33717,33784,33844,33904,33967,34035,34096,34163,34241,34311,34360,34417,34486,34547,34635,34723,34811,34899,34955,35042,35129,35216,35303,35361,35417,35488,35553,35615,35690,35763,35832,35902,35971,36026,36082,36138,36199,36257,36313,36368,36430,36483,36540,36634,36703,36804,36855,36913,36983,37052,37122,37192,37262,37329,37396,37471,37538,37597,37651,37705,37759,37812,37864,39128,43547,43603,43661,43723,44251,44396,44441,45419,45466,45511,45562,45613,45664,46223,46866,46932,47111,47174,47314,47371,47434,47491,47551,47616,47683,47748,47805,47866,47924,48058,48185,50625,51019,55704,55802,55917,56002,56050,59180,59513,59602,59759,62242,62395,63017,63188,63362,63934,64112,64294,64611,64793,64975,65165,65355,65554,65727,65837,66022,66159,66379,66563,66723,66881,67065,67268,67439,67659,67881,68036,68243,68427,68530,68671,68836,69007,69207,69411,69613,69818,70019,70218,70538,70616,70917,71083,71238,75250,76154,76431,79624,80014,80470,91607,92121,92558,92992,97332,97425,97532,97875,97982,98227,98348,98757,99005,99105,99210,99329,99838,99985,100104,100355,100488,100903,101157,106372,106493,106626,106773,110696,114758,118029,122105,125935,129960,132912,136084,140123,140353,140596,188875,189722,190642,190733,192921,193131,193240,193359,193543,194473,195219,197542,198878,199091,199961,200247,200650,201052,201395,201607,201808,202021,202310,213394,213467,213554,213639,218308,218428,218589,218752,218913,219078,219245,219298,219431,219551,219649,219762,219955,220081,220233,220375,220545,220701,220873,221164,221276,221745,221974,222192,223047,223634,224248,224416,224558,224719,224862,225030,232041,232236,232328,232501,232663,232758,232927,233021,233110,233353,233442,233735,233849,234258,234672,234788,235206,235447,235877,236312,236722,237144,237554,237676,238085,238501,238623,243424,243492,243836,243916,244272,244422,246085,246169,246558,246656,246764,246858,246988,247096,247218,247354,247462,247582,247716,247838,247966,248108,248234,248374,248500,248618,248750,248848,248958,249258,249370,249488,249952,250068,250371,250497,250593,250723,251124,251234,251358,251496,251606,251728,252040,252164,252294,252770,252898,253213,253351,253497,253659,253875,254031,255983,256051,256135,256239,256726,257282,257440,257659,257890,258113,258348,258570,258836,259428,260027,260141,260285,260397,260521,261092,261690,262185,262731,262876,262969,263061,264358,264746,265044,265233,265439,265632,265842,266059,266320,276970,277402,277926,278570,278767,279715,280272,280395,281168,281389,281589,283566,283801,283925,284337,284551,284654,284784,284959,285206,285397,285537,285731,286001,286882,287170,287300,288065,288710,288856,289417,289655,291128,291278,291695,291860,292546,293016,293212,293303,293387,293531,293765,293932,294706,294992,295152,295767,295926,296254,296481,296846,297217,297578,299332,299961,301037,301461,301613,302603,303340,303543,303789,304036,304278,304599,304904,305127,305299,305840,306109,306603,306864,307304,308049,308414,308719,310009,310115,310245,310383,310507,310753,310853,311315,311616,312477,313232,313671,313795,314036,314224,314358,314549,315328,315597,315888,316167,316484,316706,317001,317284,317388,317939,318409,318529,318931,325282,325755,326054,344646,345423,346527,347637,352159,354413,354853,358606,366146,366461,366663,367798,367948,370610,375854,377649,377956,378386,378545,380025,380245,380774,381269,381768,381921,382077,382270,382444,389408,391130,392296,392433,393662,394800,396885,398195,398443,403173,403289,403464,406581,406728,414443,416817", "endLines": "36,37,38,39,40,41,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,62,63,64,65,67,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,122,123,124,125,128,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,237,238,239,240,241,242,243,244,351,352,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,596,616,695,696,697,698,709,712,713,731,732,733,734,735,736,748,760,761,764,765,767,768,769,770,771,772,773,774,775,776,777,779,784,815,819,858,859,860,861,862,890,894,895,896,922,923,928,930,931,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,972,973,974,975,976,1021,1036,1040,1092,1099,1107,1279,1288,1297,1306,1367,1368,1373,1374,1378,1379,1385,1389,1390,1391,1392,1402,1403,1404,1408,1409,1415,1419,1420,1490,1491,1492,1547,1610,1655,1718,1772,1835,1883,1928,1991,1995,1999,2011,2786,2800,2801,2849,2853,2854,2855,2858,2871,2884,2924,2925,2952,2968,2972,2977,2982,2987,2990,2993,2996,3000,3004,3148,3149,3150,3151,3230,3233,3236,3239,3243,3247,3248,3251,3254,3257,3260,3264,3267,3270,3273,3276,3279,3282,3286,3289,3292,3302,3305,3315,3323,3331,3334,3337,3340,3343,3346,3349,3424,3425,3428,3431,3432,3435,3436,3437,3441,3442,3447,3448,3456,3464,3465,3473,3477,3485,3493,3501,3509,3517,3518,3526,3534,3535,3538,3613,3618,3620,3625,3629,3633,3659,3664,3665,3666,3667,3668,3669,3670,3671,3672,3673,3674,3675,3676,3677,3678,3679,3680,3681,3682,3683,3684,3688,3689,3690,3696,3697,3701,3703,3704,3707,3712,3713,3714,3715,3716,3717,3721,3722,3723,3729,3730,3734,3736,3739,3743,3747,3751,3755,3780,3781,3782,3790,3798,3801,3806,3811,3816,3821,3825,3829,3838,3846,3847,3848,3849,3850,3858,3867,3872,3877,3878,3879,3880,3898,3902,3907,3910,3914,3917,3921,3925,3929,3932,4088,4097,4110,4114,4129,4137,4140,4151,4156,4160,4195,4199,4200,4207,4211,4212,4213,4216,4220,4224,4225,4229,4234,4249,4253,4254,4266,4276,4277,4287,4292,4315,4318,4324,4327,4336,4344,4348,4351,4354,4357,4361,4364,4379,4383,4386,4401,4404,4412,4417,4423,4429,4435,4465,4476,4493,4500,4503,4515,4524,4528,4533,4537,4541,4545,4549,4553,4556,4565,4570,4579,4583,4590,4599,4605,4609,4627,4628,4629,4630,4631,4635,4636,4644,4648,4660,4672,4679,4680,4684,4688,4689,4693,4707,4713,4719,4725,4731,4736,4742,4748,4749,4758,4766,4767,4774,4782,4963,4973,5011,5540,5574,5609,5655,5828,5848,5929,5943,6101,6107,6151,6155,6161,6223,6411,6466,6473,6476,6525,6534,6545,6557,6580,6586,6592,6598,6603,6608,6844,6862,6892,6896,6969,6978,7123,7131,7151,7257,7265,7341,7365,7579,7587,7651", "endColumns": "55,48,55,59,60,54,49,49,52,57,47,36,46,48,55,50,48,53,53,48,55,46,55,59,60,71,63,52,51,57,64,47,65,57,68,57,69,73,67,66,69,65,72,59,59,59,58,56,57,57,45,59,53,67,68,67,52,51,49,45,49,46,57,57,58,59,61,61,61,61,61,61,67,60,61,61,52,53,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,64,65,52,75,65,86,75,75,46,64,49,53,78,77,72,64,62,65,70,70,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,83,84,80,67,76,72,69,71,69,72,63,69,47,68,51,57,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,53,52,53,50,48,50,60,60,61,49,40,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,65,72,79,79,63,62,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,55,86,86,86,86,57,55,70,64,61,74,72,68,69,68,54,55,55,60,57,55,54,61,52,56,93,68,100,50,57,69,68,69,69,69,66,66,74,66,58,53,53,53,52,51,73,64,55,57,61,54,42,44,42,46,44,50,50,50,50,47,65,61,62,71,56,62,56,59,64,66,64,56,60,57,69,56,12,149,130,97,114,84,47,78,64,88,156,156,152,153,58,173,177,177,181,316,181,181,189,189,198,172,109,184,136,219,183,159,157,183,202,170,219,221,154,206,183,102,140,164,170,199,203,201,204,200,198,203,77,300,165,154,101,10,10,10,10,10,10,10,10,10,10,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,90,10,10,108,118,10,10,10,10,94,30,10,10,10,10,10,10,10,10,10,10,72,86,84,98,10,10,10,10,10,10,52,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,10,10,10,113,143,111,123,10,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,129,137,123,10,99,10,10,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,119,10,10,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22", "endOffsets": "1346,1395,1451,1511,1572,1627,1729,1779,1832,1890,1938,1975,2022,2071,2127,2178,2227,2281,2335,2384,2440,2487,2659,2719,2780,2852,2916,3014,3115,3173,3238,3286,3352,3410,3479,3537,3607,3681,3749,3816,3886,3952,4025,4085,4145,4205,4264,4321,4379,4497,4599,4659,4713,4781,4850,4918,4971,5023,5073,5119,5169,5216,5274,5332,5391,5451,5513,5575,5637,5699,5761,5823,5891,5952,6014,6076,6129,6183,6275,6320,6384,6443,6622,9897,9968,10034,10108,10177,10248,10321,10392,10460,10533,10609,10679,10757,10825,10891,10952,11021,11085,11151,11219,11285,11348,11416,11487,11552,11625,11688,11769,11833,11899,11969,12039,12109,12179,12246,14083,14149,14202,14278,14344,14431,14507,14583,21822,21887,22806,22860,22939,23017,23090,23155,23218,23284,23355,23426,23488,23557,23623,23683,23750,23817,23873,23924,23977,24029,24083,24154,24217,24276,24338,24397,24470,24537,24597,24660,24735,24807,24903,24974,25030,25101,25158,25215,25281,25345,25416,25473,25526,25589,25641,25699,25766,27073,27158,27239,27307,27384,27457,27527,27599,27669,27742,27806,27876,27924,27993,28045,28103,28169,28236,28302,28383,28458,28514,28567,28628,28686,28736,28785,28834,28883,28945,28997,29042,29123,29177,29230,29284,29335,29384,29435,29496,29557,29619,29669,29710,29769,29828,29887,29948,30004,30060,30127,30188,30253,30308,30373,30442,30510,30588,30657,30717,30788,30862,30927,30999,31069,31136,31220,31289,31356,31426,31489,31556,31624,31707,31786,31876,31953,32021,32088,32166,32223,32280,32348,32414,32470,32530,32589,32643,32693,32743,32791,32853,32904,32970,33043,33123,33203,33267,33330,33397,33468,33526,33587,33653,33712,33779,33839,33899,33962,34030,34091,34158,34236,34306,34355,34412,34481,34542,34630,34718,34806,34894,34950,35037,35124,35211,35298,35356,35412,35483,35548,35610,35685,35758,35827,35897,35966,36021,36077,36133,36194,36252,36308,36363,36425,36478,36535,36629,36698,36799,36850,36908,36978,37047,37117,37187,37257,37324,37391,37466,37533,37592,37646,37700,37754,37807,37859,37933,39188,43598,43656,43718,43773,44289,44436,44479,45461,45506,45557,45608,45659,45710,46266,46927,46989,47169,47241,47366,47429,47486,47546,47611,47678,47743,47800,47861,47919,47989,48110,48500,50770,51145,55797,55912,55997,56045,56124,59240,59597,59754,59911,62390,62544,63071,63357,63535,64107,64289,64606,64788,64970,65160,65350,65549,65722,65832,66017,66154,66374,66558,66718,66876,67060,67263,67434,67654,67876,68031,68238,68422,68525,68666,68831,69002,69202,69406,69608,69813,70014,70213,70417,70611,70912,71078,71233,71335,75379,76426,76711,80009,80465,80974,92116,92553,92987,93430,97420,97527,97870,97977,98222,98343,98752,99000,99100,99205,99324,99833,99980,100099,100350,100483,100898,101152,101264,106488,106621,106768,110691,114753,118024,122100,125930,129955,132907,136079,140118,140348,140591,141629,189717,190637,190728,192916,193126,193235,193354,193538,194468,195214,197537,197632,198904,199956,200242,200645,201047,201390,201602,201803,202016,202305,202590,213462,213549,213634,213733,218423,218584,218747,218908,219073,219240,219293,219426,219546,219644,219757,219950,220076,220228,220370,220540,220696,220868,221159,221271,221400,221969,222187,223042,223629,224243,224411,224553,224714,224857,225025,225182,232231,232323,232496,232658,232753,232922,233016,233105,233348,233437,233730,233844,234253,234667,234783,235201,235442,235872,236307,236717,237139,237549,237671,238080,238496,238618,238802,243487,243831,243911,244267,244417,244561,246164,246553,246651,246759,246853,246983,247091,247213,247349,247457,247577,247711,247833,247961,248103,248229,248369,248495,248613,248745,248843,248953,249253,249365,249483,249947,250063,250366,250492,250588,250718,251119,251229,251353,251491,251601,251723,252035,252159,252289,252765,252893,253208,253346,253492,253654,253870,254026,254230,256046,256130,256234,256721,257277,257435,257654,257885,258108,258343,258565,258831,259423,260022,260136,260280,260392,260516,261087,261685,262180,262726,262871,262964,263056,264353,264741,265039,265228,265434,265627,265837,266054,266315,266446,277397,277921,278565,278762,279710,280267,280390,281163,281384,281584,283561,283796,283920,284332,284546,284649,284779,284954,285201,285392,285532,285726,285996,286877,287165,287295,288060,288705,288851,289412,289650,291123,291273,291690,291855,292541,293011,293207,293298,293382,293526,293760,293927,294701,294987,295147,295762,295921,296249,296476,296841,297212,297573,299327,299956,301032,301456,301608,302598,303335,303538,303784,304031,304273,304594,304899,305122,305294,305835,306104,306598,306859,307299,308044,308409,308714,310004,310110,310240,310378,310502,310748,310848,311310,311611,312472,313227,313666,313790,314031,314219,314353,314544,315323,315592,315883,316162,316479,316701,316996,317279,317383,317934,318404,318524,318926,319460,325750,326049,327988,345418,346522,347632,349522,354408,354848,358601,359184,366456,366658,367793,367943,368118,370830,376116,377951,378381,378540,380020,380240,380769,381264,381763,381916,382072,382265,382439,382647,390563,391236,392428,392574,394795,395018,398190,398438,399121,403284,403459,405832,406723,414438,414681,417146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cd54390103da05e9f0b933f516c6d0cd\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "751", "startColumns": "4", "startOffsets": "46373", "endColumns": "42", "endOffsets": "46411"}}, {"source": "D:\\Projetos\\TecBizExpoApp\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2,3", "startColumns": "2,2,2", "startOffsets": "14,66,152", "endColumns": "51,85,94", "endOffsets": "63,149,244"}, "to": {"startLines": "814,892,893", "startColumns": "4,4,4", "startOffsets": "50571,59328,59416", "endColumns": "53,87,96", "endOffsets": "50620,59411,59508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\2617a59ab69d3cbb4d67d12e7172f9c8\\transformed\\drawee-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,136", "startColumns": "4,4", "startOffsets": "55,3906", "endLines": "135,218", "endColumns": "22,22", "endOffsets": "3901,5346"}, "to": {"startLines": "6233,6998", "startColumns": "4,4", "startOffsets": "371154,395440", "endLines": "6366,7080", "endColumns": "22,22", "endOffsets": "375000,396880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\8e0aac242641ffe02139e17b1b8d81fe\\transformed\\expo.modules.notifications-0.32.11\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "891", "startColumns": "4", "startOffsets": "59245", "endColumns": "82", "endOffsets": "59323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "60,66,68,121,126,127,129,130,131,132,133,134,135,138,139,140,141,143,144,145,146,147,148,153,154,214,215,216,217,218,219,220,221,222,223,225,226,227,228,229,230,231,232,233,234,235,236,249,250,251,252,253,254,255,256,257,258,259,260,261,262,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,413,414,419,420,421,422,423,424,425,617,618,619,620,621,622,623,624,672,673,674,675,686,704,705,715,745,758,759,762,763,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,986,1017,1018,1022,1023,1024,1047,1055,1056,1060,1064,1075,1080,1108,1115,1119,1123,1128,1132,1136,1140,1144,1148,1152,1158,1162,1168,1172,1178,1182,1187,1191,1194,1198,1204,1208,1214,1218,1224,1227,1231,1235,1239,1243,1247,1248,1249,1250,1253,1256,1259,1262,1266,1267,1268,1269,1307,1310,1312,1314,1316,1321,1322,1326,1332,1336,1337,1339,1351,1352,1356,1362,1366,1421,1422,1426,1453,1457,1458,1462,2012,2184,2210,2381,2407,2438,2446,2452,2468,2490,2495,2500,2510,2519,2528,2532,2539,2558,2565,2566,2575,2578,2581,2585,2589,2593,2596,2597,2602,2607,2617,2622,2629,2635,2636,2639,2643,2648,2650,2652,2655,2658,2660,2664,2667,2674,2677,2680,2684,2686,2690,2692,2694,2696,2700,2708,2716,2728,2734,2743,2746,2757,2760,2761,2766,2767,3009,3078,3152,3153,3163,3172,3173,3175,3179,3182,3185,3188,3191,3194,3197,3200,3204,3207,3210,3213,3217,3220,3224,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3360,3361,3362,3363,3364,3365,3366,3367,3368,3369,3370,3372,3374,3375,3376,3377,3378,3379,3380,3381,3383,3384,3386,3387,3389,3391,3392,3394,3395,3396,3397,3398,3399,3401,3402,3403,3404,3405,3539,3541,3543,3549,3550,3551,3552,3553,3554,3555,3556,3557,3558,3559,3560,3561,3563,3564,3565,3566,3567,3568,3569,3571,3575,3768,3769,3770,3771,3772,3773,3777,3778,3779,3950,3952,3954,3956,3958,3960,3961,3962,3963,3965,3967,3969,3970,3971,3972,3973,3974,3975,3976,3977,3978,3979,3980,3983,3984,3985,3986,3988,3990,3991,3993,3994,3996,3998,4000,4001,4002,4003,4004,4005,4006,4007,4008,4009,4010,4011,4013,4014,4015,4016,4018,4019,4020,4021,4022,4024,4026,4028,4030,4031,4032,4033,4034,4035,4036,4037,4038,4039,4040,4041,4042,4043,4044,4796,4871,4874,4877,4880,4894,4907,5012,5015,5044,5071,5080,5144,5656,5694,5954,6072,6412,6436,6442,6609,6630,6754,6850,6856,6863,6897,7152,7188,7342,7588,7652,7664,7693", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2553,2921,3019,6188,6448,6503,6627,6691,6761,6822,6897,6973,7050,7288,7373,7455,7531,7663,7740,7818,7924,8030,8109,8438,8495,12621,12695,12770,12835,12901,12961,13022,13094,13167,13234,13351,13410,13469,13528,13587,13646,13700,13754,13807,13861,13915,13969,14867,14941,15020,15093,15167,15238,15310,15382,15455,15512,15570,15643,15717,15791,15924,15996,16069,16139,16210,16270,16331,16400,16469,16539,16613,16689,16753,16830,16906,16983,17048,17117,17194,17269,17338,17406,17483,17549,17610,17707,17772,17841,17940,18011,18070,18128,18185,18244,18308,18379,18451,18523,18595,18667,18734,18802,18870,18929,18992,19056,19146,19237,19297,19363,19430,19496,19566,19630,19683,19750,19811,19878,19991,20049,20112,20177,20242,20317,20390,20462,20506,20553,20599,20648,20709,20770,20831,20893,20957,21021,21085,21150,21213,21273,21334,21400,21459,21519,21581,21652,21712,25771,25857,26160,26250,26337,26425,26507,26590,26680,39193,39245,39303,39348,39414,39478,39535,39592,42388,42445,42493,42542,43133,44036,44083,44549,46101,46740,46804,46994,47054,48505,48579,48649,48727,48781,48851,48936,48984,49030,49091,49154,49220,49284,49355,49418,49483,49547,49608,49669,49721,49794,49868,49937,50012,50086,50160,50301,72175,75082,75160,75384,75472,75568,77081,77663,77752,77999,78280,78946,79231,80979,81456,81678,81900,82176,82403,82633,82863,83093,83323,83550,83969,84195,84620,84850,85278,85497,85780,85988,86119,86346,86772,86997,87424,87645,88070,88190,88466,88767,89091,89382,89696,89833,89964,90069,90311,90478,90682,90890,91161,91273,91385,91490,93435,93649,93795,93935,94021,94369,94457,94703,95121,95370,95452,95550,96207,96307,96559,96983,97238,101269,101358,101595,103619,103861,103963,104216,141634,152315,153831,164526,166054,167811,168437,168857,170118,171383,171639,171875,172422,172916,173521,173719,174299,175667,176042,176160,176698,176855,177051,177324,177580,177750,177891,177955,178320,178687,179363,179627,179965,180318,180412,180598,180904,181166,181291,181418,181657,181868,181987,182180,182357,182812,182993,183115,183374,183487,183674,183776,183883,184012,184287,184795,185291,186168,186462,187032,187181,187913,188085,188169,188505,188597,202792,208023,213738,213800,214378,214962,215053,215166,215395,215555,215707,215878,216044,216213,216380,216543,216786,216956,217129,217300,217574,217773,217978,225187,225271,225367,225463,225561,225661,225763,225865,225967,226069,226171,226271,226367,226479,226608,226731,226862,226993,227091,227205,227299,227439,227573,227669,227781,227881,227997,228093,228205,228305,228445,228581,228745,228875,229033,229183,229324,229468,229603,229715,229865,229993,230121,230257,230389,230519,230649,230761,238807,238953,239097,239414,239480,239570,239646,239750,239840,239942,240050,240158,240258,240338,240430,240528,240638,240690,240768,240874,240966,241070,241180,241302,241465,254980,255060,255160,255250,255360,255450,255691,255785,255891,267289,267389,267501,267615,267731,267847,267941,268055,268167,268269,268389,268511,268593,268697,268817,268943,269041,269135,269223,269335,269451,269573,269685,269860,269976,270062,270154,270266,270390,270457,270583,270651,270779,270923,271051,271120,271215,271330,271443,271542,271651,271762,271873,271974,272079,272179,272309,272400,272523,272617,272729,272815,272919,273015,273103,273221,273325,273429,273555,273643,273751,273851,273941,274051,274135,274237,274321,274375,274439,274545,274631,274741,274825,320051,322667,322785,322900,322980,323341,323878,327993,328071,329415,330776,331164,334007,349527,350789,359490,365574,376121,376872,377134,382652,383031,387309,390750,390979,391241,392579,399126,400147,405837,414686,417151,417491,418912", "endLines": "60,66,68,121,126,127,129,130,131,132,133,134,135,138,139,140,141,143,144,145,146,147,148,153,154,214,215,216,217,218,219,220,221,222,223,225,226,227,228,229,230,231,232,233,234,235,236,249,250,251,252,253,254,255,256,257,258,259,260,261,262,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,413,414,419,420,421,422,423,424,425,617,618,619,620,621,622,623,624,672,673,674,675,686,704,705,715,745,758,759,762,763,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,986,1017,1018,1022,1023,1024,1054,1055,1059,1063,1067,1079,1085,1114,1118,1122,1127,1131,1135,1139,1143,1147,1151,1157,1161,1167,1171,1177,1181,1186,1190,1193,1197,1203,1207,1213,1217,1223,1226,1230,1234,1238,1242,1246,1247,1248,1249,1252,1255,1258,1261,1265,1266,1267,1268,1269,1309,1311,1313,1315,1320,1321,1325,1331,1335,1336,1338,1350,1351,1355,1361,1365,1366,1421,1425,1452,1456,1457,1461,1489,2183,2209,2380,2406,2437,2445,2451,2467,2489,2494,2499,2509,2518,2527,2531,2538,2557,2564,2565,2574,2577,2580,2584,2588,2592,2595,2596,2601,2606,2616,2621,2628,2634,2635,2638,2642,2647,2649,2651,2654,2657,2659,2663,2666,2673,2676,2679,2683,2685,2689,2691,2693,2695,2699,2707,2715,2727,2733,2742,2745,2756,2759,2760,2765,2766,2771,3077,3147,3152,3162,3171,3172,3174,3178,3181,3184,3187,3190,3193,3196,3199,3203,3206,3209,3212,3216,3219,3223,3227,3350,3351,3352,3353,3354,3355,3356,3357,3358,3359,3360,3361,3362,3363,3364,3365,3366,3367,3368,3369,3371,3373,3374,3375,3376,3377,3378,3379,3380,3382,3383,3385,3386,3388,3390,3391,3393,3394,3395,3396,3397,3398,3400,3401,3402,3403,3404,3405,3540,3542,3544,3549,3550,3551,3552,3553,3554,3555,3556,3557,3558,3559,3560,3562,3563,3564,3565,3566,3567,3568,3570,3574,3578,3768,3769,3770,3771,3772,3776,3777,3778,3779,3951,3953,3955,3957,3959,3960,3961,3962,3964,3966,3968,3969,3970,3971,3972,3973,3974,3975,3976,3977,3978,3979,3982,3983,3984,3985,3987,3989,3990,3992,3993,3995,3997,3999,4000,4001,4002,4003,4004,4005,4006,4007,4008,4009,4010,4012,4013,4014,4015,4017,4018,4019,4020,4021,4023,4025,4027,4029,4030,4031,4032,4033,4034,4035,4036,4037,4038,4039,4040,4041,4042,4043,4044,4870,4873,4876,4879,4893,4899,4916,5014,5043,5070,5079,5143,5506,5659,5721,5981,6089,6435,6441,6447,6629,6753,6773,6855,6859,6868,6931,7163,7253,7361,7642,7663,7689,7699", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2603,2961,3063,6224,6498,6560,6686,6756,6817,6892,6968,7045,7123,7368,7450,7526,7602,7735,7813,7919,8025,8104,8184,8490,8548,12690,12765,12830,12896,12956,13017,13089,13162,13229,13297,13405,13464,13523,13582,13641,13695,13749,13802,13856,13910,13964,14018,14936,15015,15088,15162,15233,15305,15377,15450,15507,15565,15638,15712,15786,15861,15991,16064,16134,16205,16265,16326,16395,16464,16534,16608,16684,16748,16825,16901,16978,17043,17112,17189,17264,17333,17401,17478,17544,17605,17702,17767,17836,17935,18006,18065,18123,18180,18239,18303,18374,18446,18518,18590,18662,18729,18797,18865,18924,18987,19051,19141,19232,19292,19358,19425,19491,19561,19625,19678,19745,19806,19873,19986,20044,20107,20172,20237,20312,20385,20457,20501,20548,20594,20643,20704,20765,20826,20888,20952,21016,21080,21145,21208,21268,21329,21395,21454,21514,21576,21647,21707,21775,25852,25939,26245,26332,26420,26502,26585,26675,26766,39240,39298,39343,39409,39473,39530,39587,39641,42440,42488,42537,42588,43162,44078,44127,44590,46128,46799,46861,47049,47106,48574,48644,48722,48776,48846,48931,48979,49025,49086,49149,49215,49279,49350,49413,49478,49542,49603,49664,49716,49789,49863,49932,50007,50081,50155,50296,50366,72223,75155,75245,75467,75563,75653,77658,77747,77994,78275,78527,79226,79619,81451,81673,81895,82171,82398,82628,82858,83088,83318,83545,83964,84190,84615,84845,85273,85492,85775,85983,86114,86341,86767,86992,87419,87640,88065,88185,88461,88762,89086,89377,89691,89828,89959,90064,90306,90473,90677,90885,91156,91268,91380,91485,91602,93644,93790,93930,94016,94364,94452,94698,95116,95365,95447,95545,96202,96302,96554,96978,97233,97327,101353,101590,103614,103856,103958,104211,106367,152310,153826,164521,166049,167806,168432,168852,170113,171378,171634,171870,172417,172911,173516,173714,174294,175662,176037,176155,176693,176850,177046,177319,177575,177745,177886,177950,178315,178682,179358,179622,179960,180313,180407,180593,180899,181161,181286,181413,181652,181863,181982,182175,182352,182807,182988,183110,183369,183482,183669,183771,183878,184007,184282,184790,185286,186163,186457,187027,187176,187908,188080,188164,188500,188592,188870,208018,213389,213795,214373,214957,215048,215161,215390,215550,215702,215873,216039,216208,216375,216538,216781,216951,217124,217295,217569,217768,217973,218303,225266,225362,225458,225556,225656,225758,225860,225962,226064,226166,226266,226362,226474,226603,226726,226857,226988,227086,227200,227294,227434,227568,227664,227776,227876,227992,228088,228200,228300,228440,228576,228740,228870,229028,229178,229319,229463,229598,229710,229860,229988,230116,230252,230384,230514,230644,230756,230896,238948,239092,239230,239475,239565,239641,239745,239835,239937,240045,240153,240253,240333,240425,240523,240633,240685,240763,240869,240961,241065,241175,241297,241460,241617,255055,255155,255245,255355,255445,255686,255780,255886,255978,267384,267496,267610,267726,267842,267936,268050,268162,268264,268384,268506,268588,268692,268812,268938,269036,269130,269218,269330,269446,269568,269680,269855,269971,270057,270149,270261,270385,270452,270578,270646,270774,270918,271046,271115,271210,271325,271438,271537,271646,271757,271868,271969,272074,272174,272304,272395,272518,272612,272724,272810,272914,273010,273098,273216,273320,273424,273550,273638,273746,273846,273936,274046,274130,274232,274316,274370,274434,274540,274626,274736,274820,274940,322662,322780,322895,322975,323336,323569,324390,328066,329410,330771,331159,334002,344055,349657,352154,360842,366141,376867,377129,377329,383026,387304,387910,390974,391125,391451,393657,399433,403168,406576,416812,417486,418797,419110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "165,166,167,168,169,170,171,172,866,867,868,869,870,871,872,873,875,876,877,878,879,880,881,882,883,6448,6979", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9157,9247,9327,9417,9507,9587,9668,9748,56418,56523,56704,56829,56936,57116,57239,57355,57625,57813,57918,58099,58224,58399,58547,58610,58672,377334,395023", "endLines": "165,166,167,168,169,170,171,172,866,867,868,869,870,871,872,873,875,876,877,878,879,880,881,882,883,6460,6997", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "9242,9322,9412,9502,9582,9663,9743,9823,56518,56699,56824,56931,57111,57234,57350,57453,57808,57913,58094,58219,58394,58542,58605,58667,58746,377644,395435"}}, {"source": "D:\\Projetos\\TecBizExpoApp\\android\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,574", "endColumns": "81,103,108,119,103,72", "endOffsets": "132,236,345,465,569,642"}, "to": {"startLines": "913,917,918,919,920,978", "startColumns": "4,4,4,4,4,4", "startOffsets": "61329,61707,61811,61920,62040,71480", "endColumns": "81,103,108,119,103,72", "endOffsets": "61406,61806,61915,62035,62139,71548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bcb549dbc855619e30027cf9153600f3\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "6774,6787,6793,6799,6845", "startColumns": "4,4,4,4,4", "startOffsets": "387915,388554,388798,389045,390568", "endLines": "6786,6792,6798,6801,6849", "endColumns": "24,24,24,24,24", "endOffsets": "388549,388793,389040,389173,390745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\18cf84c98fd82f7dbabfb0f27f981a70\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "813", "startColumns": "4", "startOffsets": "50488", "endColumns": "82", "endOffsets": "50566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\a02035e21744ea944cae4c099374c9d0\\transformed\\recyclerview-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "92,415,416,417,426,427,428,690,6869", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4502,25944,26003,26051,26771,26846,26922,43326,391456", "endLines": "92,415,416,417,426,427,428,690,6889", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "4553,25998,26046,26102,26841,26917,26989,43387,392291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\762f4dc9aebf37bfc66de375dc543aba\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "681,714,756,6224,6229", "startColumns": "4,4,4,4,4", "startOffsets": "42872,44484,46623,370835,371005", "endLines": "681,714,756,6228,6232", "endColumns": "56,64,63,24,24", "endOffsets": "42924,44544,46682,371000,371149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\cf671caf8aa903c4b3c265cc86a280a8\\transformed\\firebase-messaging-24.0.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "902", "startColumns": "4", "startOffsets": "60309", "endColumns": "81", "endOffsets": "60386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "90,136,137,155,156,245,246,359,360,361,362,363,364,365,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,625,626,627,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,692,693,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,737,780,820,821,822,823,824,825,826,1000,3406,3407,3412,3415,3420,4079,4080,5660,5944,6162,6197,6367,6400", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4384,7128,7200,8553,8618,14588,14657,22273,22343,22411,22483,22553,22614,22688,37938,37999,38060,38122,38186,38248,38309,38377,38477,38537,38603,38676,38745,38802,38854,39646,39718,39794,40023,40082,40141,40201,40261,40321,40381,40441,40501,40561,40621,40681,40741,40800,40860,40920,40980,41040,41100,41160,41220,41280,41340,41400,41459,41519,41579,41638,41697,41756,41815,41874,43433,43468,44595,44650,44713,44768,44826,44884,44945,45008,45065,45116,45166,45227,45284,45350,45384,45715,48115,51150,51217,51289,51358,51427,51501,51573,73576,230901,231018,231285,231578,231845,276831,276903,349662,359189,368123,369929,375005,375687", "endLines": "90,136,137,155,156,245,246,359,360,361,362,363,364,365,597,598,599,600,601,602,603,604,605,606,607,608,609,610,611,625,626,627,631,632,633,634,635,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,651,652,653,654,655,656,657,658,659,660,661,662,692,693,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,737,780,820,821,822,823,824,825,826,1000,3406,3410,3412,3418,3420,4079,4080,5665,5953,6196,6217,6399,6405", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "4439,7195,7283,8613,8679,14652,14715,22338,22406,22478,22548,22609,22683,22756,37994,38055,38117,38181,38243,38304,38372,38472,38532,38598,38671,38740,38797,38849,38911,39713,39789,39854,40077,40136,40196,40256,40316,40376,40436,40496,40556,40616,40676,40736,40795,40855,40915,40975,41035,41095,41155,41215,41275,41335,41395,41454,41514,41574,41633,41692,41751,41810,41869,41928,43463,43498,44645,44708,44763,44821,44879,44940,45003,45060,45111,45161,45222,45279,45345,45379,45414,45745,48180,51212,51284,51353,51422,51496,51568,51656,73642,231013,231214,231390,231774,231969,276898,276965,349860,359485,369924,370605,375682,375849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\11fd102c6c432d0a7e1f8a37852e02b8\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "684", "startColumns": "4", "startOffsets": "43016", "endColumns": "65", "endOffsets": "43077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\aab617897d5efee02f2a2b67997cb35e\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "689,703", "startColumns": "4,4", "startOffsets": "43272,43969", "endColumns": "53,66", "endOffsets": "43321,44031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bba1d21875594204cebffa52ee9faa9a\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "752", "startColumns": "4", "startOffsets": "46416", "endColumns": "42", "endOffsets": "46454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\df8bcecc49fccce54e9e67dd4295a710\\transformed\\core-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "18", "endColumns": "12", "endOffsets": "933"}, "to": {"startLines": "3933", "startColumns": "4", "startOffsets": "266451", "endLines": "3949", "endColumns": "12", "endOffsets": "267284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,92,96,100,103,107,111,115,118,121,122,123,132,139,146,149,152,155,161,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,181,232,286,345,393,442,491,540,596,644,693,751,800,836,886,927,971,1022,1066,1109,1143,1182,1228,1276,1318,1372,1484,1601,1724,1844,1958,2084,2239,2624,2720,2840,2960,3062,3202,3324,3434,3541,3644,3755,3924,4092,4209,4328,4441,4627,4735,4848,4939,5050,5219,5317,5442,5537,5644,5814,5912,6095,6268,6380,6481,6640,6774,6914,7102,7207,7338,7507,7624,7772,7917,8067,8166,8262,8458,8641,8740,8924,9091,9339,9587,9830,9990,10192,10398,10595,10771,10935,10961,10996,11534,11952,12330,12507,12686,12869,13234,13431", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,91,95,99,102,106,110,114,117,120,121,122,131,138,145,148,151,154,160,163,173", "endColumns": "62,62,50,53,58,47,48,48,48,55,47,48,57,48,35,49,40,43,50,43,42,33,38,45,47,41,53,47,116,122,119,113,125,154,384,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "113,176,227,281,340,388,437,486,535,591,639,688,746,795,831,881,922,966,1017,1061,1104,1138,1177,1223,1271,1313,1367,1415,1596,1719,1839,1953,2079,2234,2619,2715,2835,2955,3057,3197,3319,3429,3536,3639,3750,3919,4087,4204,4323,4436,4622,4730,4843,4934,5045,5214,5312,5437,5532,5639,5809,5907,6090,6263,6375,6476,6635,6769,6909,7011,7202,7333,7502,7619,7767,7912,8062,8161,8257,8453,8636,8735,8919,9086,9334,9582,9825,9985,10187,10393,10590,10766,10930,10956,10991,11529,11947,12325,12502,12681,12864,13229,13426,13867"}, "to": {"startLines": "161,162,628,629,630,663,664,665,666,667,668,669,670,671,680,688,691,694,700,702,706,708,738,739,746,747,749,750,812,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,865,921,924,925,929,932,933,934,977,979,983,985,989,990,991,993,994,996,998,999,1001,1004,1006,1007,1025,1029,2926,2930,2941,2945,3005,3293,3296,3545,3595,3596,3605,3646,3653,3656,3756,3759,3765,4786", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8933,8996,39859,39910,39964,41933,41981,42030,42079,42128,42184,42232,42281,42339,42836,43222,43392,43503,43831,43925,44132,44217,45750,45789,46133,46181,46271,46325,50371,51661,51784,51904,52018,52144,52299,52684,52780,52900,53020,53122,53262,53384,53494,53601,53704,53815,53984,54152,54269,54388,54501,54687,54795,54908,54999,55110,55279,55377,55502,55597,56248,62144,62549,62732,63076,63540,63641,63800,71340,71553,71849,72044,72328,72497,72614,72808,72953,73145,73284,73380,73647,73913,74102,74286,75658,75906,197637,197880,198358,198560,202595,221405,221581,239235,242433,242468,243006,245351,245729,245906,254235,254418,254783,319610", "endLines": "161,162,628,629,630,663,664,665,666,667,668,669,670,671,680,688,691,694,700,702,706,708,738,739,746,747,749,750,812,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,865,921,924,925,929,932,933,934,977,979,983,985,989,990,991,993,994,996,998,999,1001,1004,1006,1007,1028,1032,2929,2932,2944,2948,3008,3295,3298,3545,3595,3604,3611,3652,3655,3658,3758,3764,3767,4795", "endColumns": "62,62,50,53,58,47,48,48,48,55,47,48,57,48,35,49,40,43,50,43,42,33,38,45,47,41,53,47,116,122,119,113,125,154,384,95,119,119,101,139,121,109,106,102,110,168,167,116,118,112,185,107,112,90,110,168,97,124,94,106,169,97,182,172,111,100,158,133,139,101,104,130,168,116,147,144,149,98,95,195,182,98,183,166,10,10,12,12,10,10,10,12,12,25,34,10,10,10,10,10,12,12,12,10", "endOffsets": "8991,9054,39905,39959,40018,41976,42025,42074,42123,42179,42227,42276,42334,42383,42867,43267,43428,43542,43877,43964,44170,44246,45784,45830,46176,46218,46320,46368,50483,51779,51899,52013,52139,52294,52679,52775,52895,53015,53117,53257,53379,53489,53596,53699,53810,53979,54147,54264,54383,54496,54682,54790,54903,54994,55105,55274,55372,55497,55592,55699,56413,62237,62727,62900,63183,63636,63795,63929,71475,71650,71949,72170,72492,72609,72757,72948,73098,73239,73375,73571,73825,74007,74281,74448,75901,76149,197875,198035,198555,198761,202787,221576,221740,239256,242463,243001,243419,245724,245901,246080,254413,254778,254975,320046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\497b53a2103d9dab55d7751334c2a0b7\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "699,4900,6802,6805", "startColumns": "4,4,4,4", "startOffsets": "43778,323574,389178,389293", "endLines": "699,4906,6804,6807", "endColumns": "52,24,24,24", "endOffsets": "43826,323873,389288,389403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\eb6f6e5d4b0be6ab8ca06969f4ad2e47\\transformed\\autofill-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,19,20,27,32,37,44,53", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,934,994,1376,1656,1938,2322,2820", "endLines": "2,18,19,26,31,36,43,52,66", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "118,929,989,1371,1651,1933,2317,2815,3867"}, "to": {"startLines": "353,3579,4045,4046,4053,4058,4063,4070,5507", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "21892,241622,274945,275005,275387,275667,275949,276333,344060", "endLines": "353,3594,4045,4052,4057,4062,4069,4078,5520", "endColumns": "67,12,59,12,12,12,12,12,24", "endOffsets": "21955,242428,275000,275382,275662,275944,276328,276826,344641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f33a32db8d06dc21097da2718df72823\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "42,157,158,159,160,356,357,358,1068,2933,2935,2938,5666", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1632,8684,8745,8807,8869,22103,22162,22219,78532,198040,198104,198230,349865", "endLines": "42,157,158,159,160,356,357,358,1074,2934,2937,2940,5693", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "1679,8740,8802,8864,8928,22157,22214,22268,78941,198099,198225,198353,350784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1797,1853,1906,2020,2104,2180,2240,2344,2418,2499,2588,2687,2795,2892,2980,3080,3150,3268,3356,3453,3563,3652,3742,3805,3898,3950,4032,4098,4194", "endLines": "5,7,10,14,33,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "19,19,19,19,19,19,55,52,113,83,75,59,103,73,80,88,98,107,96,87,99,69,117,87,96,109,88,89,62,92,51,81,65,95,65", "endOffsets": "189,272,383,518,1696,1792,1848,1901,2015,2099,2175,2235,2339,2413,2494,2583,2682,2790,2887,2975,3075,3145,3263,3351,3448,3558,3647,3737,3800,3893,3945,4027,4093,4189,4255"}, "to": {"startLines": "2,6,8,11,15,34,142,418,817,818,884,887,897,898,903,904,905,906,907,908,909,910,911,912,914,915,916,984,1010,1011,1012,1013,1014,1015,1016", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,1199,7607,26107,50821,50935,58751,58961,59916,60020,60391,60472,60561,60660,60768,60865,60953,61053,61123,61241,61411,61508,61618,71954,74564,74627,74720,74772,74854,74920,75016", "endLines": "5,7,10,14,33,35,142,418,817,818,884,887,897,898,903,904,905,906,907,908,909,910,911,912,914,915,916,984,1010,1011,1012,1013,1014,1015,1016", "endColumns": "19,19,19,19,19,19,55,52,113,83,75,59,103,73,80,88,98,107,96,87,99,69,117,87,96,109,88,89,62,92,51,81,65,95,65", "endOffsets": "284,367,478,613,1194,1290,7658,26155,50930,51014,58822,59016,60015,60089,60467,60556,60655,60763,60860,60948,61048,61118,61236,61324,61503,61613,61702,72039,74622,74715,74767,74849,74915,75011,75077"}}, {"source": "D:\\Projetos\\TecBizExpoApp\\android\\app\\build\\generated\\res\\resValues\\release\\values\\gradleResValues.xml", "from": {"startLines": "6,8", "startColumns": "4,4", "startOffsets": "159,265", "endColumns": "63,89", "endOffsets": "218,350"}, "to": {"startLines": "778,982", "startColumns": "4,4", "startOffsets": "47994,71759", "endColumns": "63,89", "endOffsets": "48053,71844"}}, {"source": "D:\\Projetos\\TecBizExpoApp\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "1,7", "startColumns": "2,2", "startOffsets": "61,426", "endLines": "6,9", "endColumns": "10,10", "endOffsets": "423,574"}, "to": {"startLines": "1041,3546", "startColumns": "4,4", "startOffsets": "76716,239261", "endLines": "1046,3548", "endColumns": "10,10", "endOffsets": "77076,239409"}}, {"source": "D:\\Projetos\\TecBizExpoApp\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "3,4,2,5,1", "startColumns": "2,2,2,2,2", "startOffsets": "117,162,70,211,14", "endColumns": "44,48,46,55,55", "endOffsets": "159,208,114,264,67"}, "to": {"startLines": "163,164,224,247,263", "startColumns": "4,4,4,4,4", "startOffsets": "9059,9106,13302,14720,15866", "endColumns": "46,50,48,57,57", "endOffsets": "9101,9152,13346,14773,15919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\dfd1ac0f786923d01f25f12719618681\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "7184", "startColumns": "4", "startOffsets": "399976", "endLines": "7187", "endColumns": "24", "endOffsets": "400142"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\1c8b3ac0d0a1c4de61c4a8165bf1b104\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "59,4783,5982,5988", "startColumns": "4,4,4,4", "startOffsets": "2492,319465,360847,361058", "endLines": "59,4785,5987,6071", "endColumns": "60,12,24,24", "endOffsets": "2548,319605,361053,365569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\ab4e70f01f1243dec7d1ff49d1e2575b\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "754", "startColumns": "4", "startOffsets": "46519", "endColumns": "53", "endOffsets": "46568"}}, {"source": "D:\\Projetos\\TecBizExpoApp\\node_modules\\expo-dev-launcher\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "100,163,226,290,345,417,470", "endLines": "2,3,4,5,6,7,19", "endColumns": "62,62,63,54,71,52,10", "endOffsets": "158,221,285,340,412,465,1250"}, "to": {"startLines": "208,209,210,211,212,213,3634", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12251,12314,12377,12441,12496,12568,244566", "endLines": "208,209,210,211,212,213,3645", "endColumns": "62,62,63,54,71,52,10", "endOffsets": "12309,12372,12436,12491,12563,12616,245346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,66", "endColumns": "73,57,54,50,54,52,45,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2447,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3818"}, "to": {"startLines": "676,678,679,685,687,757,816,863,864,886,888,889,926,927,970,971,980,981,987,988,992,995,997,1002,1003,1005,2949,2953", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "42593,42723,42781,43082,43167,46687,50775,56129,56194,58895,59021,59122,62905,62957,70422,70484,71655,71705,72228,72274,72762,73103,73244,73830,73877,74012,198766,198909", "endLines": "676,678,679,685,687,757,816,863,864,886,888,889,926,927,970,971,980,981,987,988,992,995,997,1002,1003,1005,2951,2956", "endColumns": "73,57,54,50,54,52,45,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12", "endOffsets": "42662,42776,42831,43128,43217,46735,50816,56189,56243,58956,59117,59175,62952,63012,70479,70533,71700,71754,72269,72323,72803,73140,73279,73872,73908,74097,198873,199086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\93b576e42733cef862a954c928112e8c\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "4917,4933,4939,7164,7180", "startColumns": "4,4,4,4,4", "startOffsets": "324395,324820,324998,399438,399849", "endLines": "4932,4938,4948,7179,7183", "endColumns": "24,24,24,24,24", "endOffsets": "324815,324993,325277,399844,399971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\35c10ae8011164342dcf8561fd64efea\\transformed\\viewpager2-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "24", "endOffsets": "160"}, "to": {"startLines": "7690", "startColumns": "4", "startOffsets": "418802", "endLines": "7692", "endColumns": "24", "endOffsets": "418907"}}]}]}