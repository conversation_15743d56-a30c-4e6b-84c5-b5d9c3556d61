{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5350", "endColumns": "153", "endOffsets": "5499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3319,3422,3526,3629,3731,3836,3942,12241", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3417,3521,3624,3726,3831,3937,4056,12337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,11115", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,11196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,354,463,559,727,856,965,1085,1206,1340,1477,1607,1760,1849,2005,2122,2272,2415,2552,2675,2786,2939,3049,3198,3293,3430", "endColumns": "168,129,108,95,167,128,108,119,120,133,136,129,152,88,155,116,149,142,136,122,110,152,109,148,94,136,107", "endOffsets": "219,349,458,554,722,851,960,1080,1201,1335,1472,1602,1755,1844,2000,2117,2267,2410,2547,2670,2781,2934,3044,3193,3288,3425,3533"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3020,3189,6676,6998,7281,7449,7906,8015,8135,8256,8390,8527,8657,8810,8899,9055,9172,9322,9465,10900,13144,13255,13408,13518,13667,13762,13899", "endColumns": "168,129,108,95,167,128,108,119,120,133,136,129,152,88,155,116,149,142,136,122,110,152,109,148,94,136,107", "endOffsets": "3184,3314,6780,7089,7444,7573,8010,8130,8251,8385,8522,8652,8805,8894,9050,9167,9317,9460,9597,11018,13250,13403,13513,13662,13757,13894,14002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-my\\values-my.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "134,243", "endColumns": "108,107", "endOffsets": "238,346"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "12927,13036", "endColumns": "108,107", "endOffsets": "13031,13139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,106", "endOffsets": "159,265,380,487"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6785,7578,7684,7799", "endColumns": "108,105,114,106", "endOffsets": "6889,7679,7794,7901"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,282,353,437,506,574,653,737,827,909,979,1071,1154,1236,1328,1412,1495,1567,1639,1724,1800,1877,1956", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "124,203,277,348,432,501,569,648,732,822,904,974,1066,1149,1231,1323,1407,1490,1562,1634,1719,1795,1872,1951,2031"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,4243,9602,9676,9747,10014,10083,10151,10230,10485,10575,10830,11023,11365,11448,11530,11699,11783,11939,12084,12156,12342,12565,12768,12847", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "2935,4317,9671,9742,9826,10078,10146,10225,10309,10570,10652,10895,11110,11443,11525,11617,11778,11861,12006,12151,12236,12413,12637,12842,12922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,185,279,367,471,575,658,742,841,930,1012,1099,1185,1260,1349,1426,1499,1572,1653,1719", "endColumns": "79,93,87,103,103,82,83,98,88,81,86,85,74,88,76,72,72,80,65,125", "endOffsets": "180,274,362,466,570,653,737,836,925,1007,1094,1180,1255,1344,1421,1494,1567,1648,1714,1840"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2940,4061,4155,6894,7094,7198,9831,9915,10314,10403,10657,10744,11201,11276,11622,11866,12011,12418,12499,12642", "endColumns": "79,93,87,103,103,82,83,98,88,81,86,85,74,88,76,72,72,80,65,125", "endOffsets": "3015,4150,4238,6993,7193,7276,9910,10009,10398,10480,10739,10825,11271,11360,11694,11934,12079,12494,12560,12763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4322,4429,4593,4727,4838,4985,5117,5240,5504,5680,5786,5956,6099,6257,6444,6514,6587", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "4424,4588,4722,4833,4980,5112,5235,5345,5675,5781,5951,6094,6252,6439,6509,6582,6671"}}]}]}