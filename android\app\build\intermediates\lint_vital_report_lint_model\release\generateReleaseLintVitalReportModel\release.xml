<variant
    name="release"
    package="com.tecbiz.tecbizassociadospush"
    minSdkVersion="24"
    targetSdkVersion="36.0"
    shrinking="true"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    manifestMergeReport="build\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.11.0;proguard-rules.pro;build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.11.0;proguard-rules.pro"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;build\generated\source\codegen\java;src\release\java;build\generated\autolinking\src\main\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <resValues>
    <resValue
        type="string"
        name="react_native_dev_server_ip"
        value="*************" />
    <resValue
        type="integer"
        name="react_native_dev_server_port"
        value="8081" />
  </resValues>
  <manifestPlaceholders>
    <placeholder
        name="usesCleartextTraffic"
        value="false" />
  </manifestPlaceholders>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\tmp\kotlin-classes\release;build\kotlinToolingMetadata;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.tecbiz.tecbizassociadospush"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\source\buildConfig\release"
      generatedResourceFolders="build\generated\res\createBundleReleaseJsAndAssets;build\generated\res\processReleaseGoogleServices;build\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4af04fcba058659bc7831c5a14254dac\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
