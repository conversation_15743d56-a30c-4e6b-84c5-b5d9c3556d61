{\rtf1\ansi\ansicpg1252\cocoartf2822
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\pard\tx720\tx1440\tx2160\tx2880\tx3600\tx4320\tx5040\tx5760\tx6480\tx7200\tx7920\tx8640\pardirnatural\partightenfactor0

\f0\fs24 \cf0 padr\'e3o	14:07:35.786288-0300	appstored	[AF8CF733] getLaunchInfoForBundleID: com.tecbiz.tecbizassociadospush for client: com.apple.springboard\
padr\'e3o	14:07:22.525713-0300	audiomxd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.657571-0300	audiomxd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.756298-0300	audiomxd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.565215-0300	audiomxd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.068679-0300	audiomxd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.109333-0300	audiomxd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:21.550339-0300	backboardd	canceling paths contacts: [pathIndex: 3; touchIdentifier: 26; touching; locked; destinations: ((touchStream|filterDetachedTouches); contextID: 0xB830D45E; clientPort: 0x39403, (hitTest); contextID: 0x3B2DD7D9; clientPort: 0x4BE6F; inheritedSceneHostSettings: <identifier: sceneID:com.tecbiz.tecbizassociadospush-default; touchBehavior: foreground>)] -- Soft (ts:<BKDigitizerTouchStreamClient: 0x8d334abc0; pid: 219(v263); SpringBoard; system-gestures; creationTime: 32.8760; touchDestination: <BKTouchDestination: 0x8d2bcb5d0; (touchStream|filterDetachedTouches); contextID: 0xB830D45E; clientPort: 0x39403; externalReferences: 1>; valid: YES; dispatchMode: stealing; ambiguityRecommendation: defer>)\
padr\'e3o	14:07:21.551605-0300	backboardd	not removing destination (external): <BKTouchDestination: 0x8d2ace580; (hitTest); contextID: 0x3B2DD7D9; clientPort: 0x4BE6F; inheritedSceneHostSettings: <identifier: sceneID:com.tecbiz.tecbizassociadospush-default; touchBehavior: foreground>; externalReferences: 1>\
padr\'e3o	14:07:21.600182-0300	backboardd	new scene host settings: contextID:3B2DD7D9 <sceneID:com.tecbiz.tecbizassociadospush-default> foreground -> inactive\
padr\'e3o	14:07:22.463635-0300	backboardd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.661058-0300	backboardd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.748994-0300	backboardd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.563771-0300	backboardd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.017777-0300	backboardd	new deferring rules for pid:541: [[541-2]; <keyboardFocus; builtin; \'85eneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default> -> <token: 0xA5C09968; pid: 541>; reason: \'85gin event deferring in keyboardFocus for window: 0x102056c60]\
padr\'e3o	14:07:35.035900-0300	backboardd	new scene host settings: contextID:A5C09968 <sceneID:com.tecbiz.tecbizassociadospush-default> unspecified -> foreground\
padr\'e3o	14:07:35.041520-0300	backboardd	new deferring rules for pid:219: [\
    [219-17]; <keyboardFocus; builtin; \'85board.systemappservices/FBSceneManager:com.apple.springboard> -> <token: 0xA00EAB43; pid: 219>; reason: \'85gin event deferring in keyboardFocus for window: 0xb75178700,\
    [219-2]; <system; builtin; SBMainSystemGestures> -> <token: 0xB830D45E; pid: 219>; reason: systemGestureSymbol-Main,\
    [219-1]; <system; builtin> -> <token: 0xB830D45E; pid: 219>; reason: systemGestures-Main,\
    [219-19]; <keyboardFocus; \'85board.systemappservices/FBSceneManager:com.apple.springboard> -> <token: \'85eneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; pid: 541>; reason: SpringBoard<com.apple.springboard>: enforcing outbound,\
    [219-4]; <keyboardFocus; SBKeyboardFocus> -> <token: \'85board.systemappservices/FBSceneManager:com.apple.springboard; pid: 219>; reason: SB incoming to root scene (symbol),\
    [219-5]; <systemKeyCommandOverlay> -> <token: 0xE87FA617; pid: 219>; reason: systemKeyCommandOverlayEnvironment to root scene,\
    [219-3]; <keyboardFocus\
padr\'e3o	14:07:35.044756-0300	backboardd	chain did update (setDeferringRules) <keyboardFocus; display: null; compatibilityDisplay: builtin> [\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard; pid: 219>,\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; pid: 541>,\
    <token: 0xA5C09968; pid: 541>\
]\
padr\'e3o	14:07:35.045357-0300	backboardd	chain did update (setDeferringRules) <keyboardFocus; display: builtin> [\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard; pid: 219>,\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; pid: 541>,\
    <token: 0xA5C09968; pid: 541>\
]\
padr\'e3o	14:07:35.074850-0300	backboardd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.108509-0300	backboardd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.231904-0300	backboardd	new deferring rules for pid:541: [[541-3]; <keyboardFocus; builtin; \'85eneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default> -> <token: 0xC49EDDF1; pid: 541>; reason: \'85gin event deferring in keyboardFocus for window: 0x10650fb80]\
padr\'e3o	14:07:35.234399-0300	backboardd	chain did update (setDeferringRules) <keyboardFocus; display: null; compatibilityDisplay: builtin> [\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard; pid: 219>,\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; pid: 541>,\
    <token: 0xC49EDDF1; pid: 541>\
]\
padr\'e3o	14:07:35.234570-0300	backboardd	chain did update (setDeferringRules) <keyboardFocus; display: builtin> [\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard; pid: 219>,\
    <token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; pid: 541>,\
    <token: 0xC49EDDF1; pid: 541>\
]\
padr\'e3o	14:07:35.253456-0300	backboardd	new scene host settings: contextID:C49EDDF1 <sceneID:com.tecbiz.tecbizassociadospush-default> unspecified -> foreground\
padr\'e3o	14:07:35.254009-0300	backboardd	new scene host settings: contextID:7E76E820 <sceneID:com.tecbiz.tecbizassociadospush-default> unspecified -> foreground\
padr\'e3o	14:07:43.368447-0300	backboardd	touchstats 100000963-<main> events starting 21.8891s ago: [\
    down: 11,\
    up: 11,\
    rangeIn: 11,\
    rangeOut: 11,\
    soft cancel: 4,\
    fromEdge: 6,\
    pathsSeen: 1 2 3 4 5 6,\
    destinations: \{\
        MobileSafari.465 contextID 1D2EEC9C: \{\
            touchIDs: 22-24;\
        \};\
        SpringBoard.219 contextID CAB91AE8: \{\
            touchIDs: 1B-1D 1F-21;\
        \};\
        WhatsApp.470 contextID A8F96724: \{\
            touchIDs: 1E;\
        \};\
        TecBizAssociado.541 contextID 3B2DD7D9: \{\
            touchIDs: 1A;\
        \};\
    \},\
    averageProcessingTime (\'b5s): 859.79\
]\
padr\'e3o	14:07:21.622221-0300	bluetoothd	SystemUI unknown identifier: 'sceneID:com.tecbiz.tecbizassociadospush-default' / 'com.tecbiz.tecbizassociadospush'\
padr\'e3o	14:07:35.092725-0300	bluetoothd	SystemUI unknown identifier: 'sceneID:com.tecbiz.tecbizassociadospush-default' / 'com.tecbiz.tecbizassociadospush'\
padr\'e3o	14:07:35.814901-0300	bluetoothd	SystemUI unknown identifier: 'sceneID:com.tecbiz.tecbizassociadospush-default' / 'com.tecbiz.tecbizassociadospush'\
padr\'e3o	14:07:21.612375-0300	cameracaptured	<<<< FigCaptureDisplayLayoutMonitor >>>> -[FigCaptureDisplayLayoutMonitor _parseFBSDisplayLayout:]: <FigCaptureDisplayLayoutMonitor: 0x49e1955c0 Main Display> <FBSDisplayLayout: 0x49e6e8700; displayIdentity: Main> \{    bounds = \{\{0, 0\}, \{390, 844\}\};    interfaceOrientation = "portrait (1)";    backlightLevel = 100;    backlightState = 2;    elements = \{        <SBSDisplayLayoutElement: 0x49ecf20d0; com.apple.springboard.home-screen; frame: \{\{0, 0\}, \{390, 844\}\}; level: 0; role: primary>;        <SBSDisplayLayoutElement: 0x49ecf1340; sceneID:com.tecbiz.tecbizassociadospush-default; bundleID: com.tecbiz.tecbizassociadospush; frame: \{\{0, 0\}, \{390, 844\}\}; level: 1; role: primary>;    \}    timestamp = 16 September 2025 at 14:07:21 GMT-3;\}\
padr\'e3o	14:07:21.612451-0300	cameracaptured	<<<< FigCaptureDisplayLayoutMonitor >>>> -[FigCaptureDisplayLayoutMonitor _updateObserversWithLayout:]: <FigCaptureDisplayLayoutMonitor: 0x49e1955c0 Main Display> <FigCaptureDisplayLayout: 0x49e6e8680 09-16-2025 14:07:21, Main Display, foreground:[com.tecbiz.tecbizassociadospush]>\
padr\'e3o	14:07:35.014700-0300	cameracaptured	<<<< FigCaptureDisplayLayoutMonitor >>>> -[FigCaptureDisplayLayoutMonitor _parseFBSDisplayLayout:]: <FigCaptureDisplayLayoutMonitor: 0x49e1955c0 Main Display> <FBSDisplayLayout: 0x49e6e8140; displayIdentity: Main> \{    bounds = \{\{0, 0\}, \{390, 844\}\};    interfaceOrientation = "portrait (1)";    backlightLevel = 100;    backlightState = 2;    elements = \{        <SBSDisplayLayoutElement: 0x49ecf11f0; com.apple.springboard.home-screen-open-folder; frame: \{\{0, 0\}, \{390, 844\}\}; level: 0; role: embedded>;        <SBSDisplayLayoutElement: 0x49ecf1110; sceneID:com.tecbiz.tecbizassociadospush-default; bundleID: com.tecbiz.tecbizassociadospush; frame: \{\{0, 0\}, \{390, 844\}\}; level: 1; role: primary>;    \}    timestamp = 16 September 2025 at 14:07:34 GMT-3;\}\
padr\'e3o	14:07:35.014760-0300	cameracaptured	<<<< FigCaptureDisplayLayoutMonitor >>>> -[FigCaptureDisplayLayoutMonitor _updateObserversWithLayout:]: <FigCaptureDisplayLayoutMonitor: 0x49e1955c0 Main Display> <FigCaptureDisplayLayout: 0x49e6e8680 09-16-2025 14:07:34, Main Display, foreground:[com.tecbiz.tecbizassociadospush]>\
padr\'e3o	14:07:35.800630-0300	cameracaptured	<<<< FigCaptureDisplayLayoutMonitor >>>> -[FigCaptureDisplayLayoutMonitor _parseFBSDisplayLayout:]: <FigCaptureDisplayLayoutMonitor: 0x49e1955c0 Main Display> <FBSDisplayLayout: 0x49e6e8400; displayIdentity: Main> \{    bounds = \{\{0, 0\}, \{390, 844\}\};    interfaceOrientation = "portrait (1)";    backlightLevel = 100;    backlightState = 2;    elements = \{        <SBSDisplayLayoutElement: 0x49ecf20d0; com.apple.springboard.home-screen-open-folder; frame: \{\{0, 0\}, \{390, 844\}\}; level: 0; role: embedded>;        <SBSDisplayLayoutElement: 0x49ecf1260; sceneID:com.tecbiz.tecbizassociadospush-default; bundleID: com.tecbiz.tecbizassociadospush; frame: \{\{0, 0\}, \{390, 844\}\}; level: 1; role: primary>;    \}    timestamp = 16 September 2025 at 14:07:35 GMT-3;\}\
padr\'e3o	14:07:35.800713-0300	cameracaptured	<<<< FigCaptureDisplayLayoutMonitor >>>> -[FigCaptureDisplayLayoutMonitor _updateObserversWithLayout:]: <FigCaptureDisplayLayoutMonitor: 0x49e1955c0 Main Display> <FigCaptureDisplayLayout: 0x49e6e8680 09-16-2025 14:07:35, Main Display, foreground:[com.tecbiz.tecbizassociadospush]>\
padr\'e3o	14:07:22.445719-0300	CommCenter	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.612232-0300	CommCenter	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.741374-0300	CommCenter	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.559748-0300	CommCenter	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.012295-0300	CommCenter	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.104161-0300	CommCenter	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.523650-0300	dasd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.658303-0300	dasd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.752649-0300	dasd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.565015-0300	dasd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.074140-0300	dasd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.109308-0300	dasd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:24.046576-0300	gamepolicyd	Hit the server for a process handle 16615bfe0000021d that resolved to: [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]\
padr\'e3o	14:07:24.055341-0300	gamepolicyd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:24.276468-0300	gamepolicyd	Identified game com.tecbiz.tecbizassociadospush GM:false DPS:false SEM:false MMA:true\
padr\'e3o	14:07:25.563848-0300	gamepolicyd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.072324-0300	gamepolicyd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-NotVisible\
padr\'e3o	14:07:35.108681-0300	gamepolicyd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-NotVisible\
padr\'e3o	14:07:22.501652-0300	locationd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.657113-0300	locationd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.749346-0300	locationd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:22.749519-0300	locationd	\{"msg":"invoking applicationStateChange handler", "StateChangeData":"\{\\n    BKSApplicationStateAppIsFrontmost = 0;\\n    BKSApplicationStateExtensionKey = 0;\\n    SBApplicationStateDisplayIDKey = \\"com.tecbiz.tecbizassociadospush\\";\\n    SBApplicationStateKey = 2;\\n    SBApplicationStateProcessIDKey = 541;\\n    SBMostElevatedStateForProcessID = 2;\\n\}"\}\
padr\'e3o	14:07:22.749620-0300	locationd	\{"msg":"Not Posting Application State Change Notification via legacy path", "notification":"BackgroundTaskSuspended", "pid":541, "bundleId":"com.tecbiz.tecbizassociadospush"\}\
padr\'e3o	14:07:22.749780-0300	locationd	\{"msg":"RBS #AppMonitor process monitor update handler invoked", "pid":541, "bundleID":"com.tecbiz.tecbizassociadospush", "state":"RunningSuspended"\}\
padr\'e3o	14:07:22.749845-0300	locationd	\{"msg":"RBS #AppMonitor Post Application State Change Notification", "notification":"BackgroundTaskSuspended", "pid":541, "bundleId":"com.tecbiz.tecbizassociadospush"\}\
padr\'e3o	14:07:22.750163-0300	locationd	\{"msg":"#Warning #ClientResolution the passed keyPath is not registered. Resolving to #nullCKP", "InputCKP":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:22.751682-0300	locationd	\{"msg":"#CLIUA AppMonitor notification", "notification":"BackgroundTaskSuspended", "pid":541, "bundleId":"com.tecbiz.tecbizassociadospush", "ClientKey":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:22.751756-0300	locationd	\{"msg":"#CLIUA Marking change", "clientKey":"icom.tecbiz.tecbizassociadospush:", "reason":"Decaying in-use status from process state", "AssertionLevel":"kCLClientInUseLevelDecayingUserEngagement", "coming":1\}\
padr\'e3o	14:07:22.751827-0300	locationd	\{"msg":"#CLIUA updating AssertionRecord", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelDirectUserEngagement"\}\
padr\'e3o	14:07:22.751930-0300	locationd	\{"msg":"#CLIUA AssertionRecord updated", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelDirectUserEngagement"\}\
padr\'e3o	14:07:22.751977-0300	locationd	\{"msg":"#CLIUA Marking change", "clientKey":"icom.tecbiz.tecbizassociadospush:", "reason":"Process state from RunningBoard", "AssertionLevel":"kCLClientInUseLevelDirectUserEngagement", "coming":0\}\
padr\'e3o	14:07:22.752098-0300	locationd	\{"msg":"#CLIUA updating AssertionRecord", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelDirectUserEngagement"\}\
padr\'e3o	14:07:22.752141-0300	locationd	\{"msg":"#CLIUA AssertionRecord updated", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelDecayingUserEngagement"\}\
padr\'e3o	14:07:22.752175-0300	locationd	\{"msg":"#CLIUA in-use level changed for client", "ClientKey":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:22.752232-0300	locationd	\{"msg":"#Warning #ClientResolution the passed keyPath is not registered. Resolving to #nullCKP", "InputCKP":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:25.564106-0300	locationd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:32.751230-0300	locationd	\{"msg":"#CLIUA Marking change", "clientKey":"icom.tecbiz.tecbizassociadospush:", "reason":"Decaying in-use status from process state", "AssertionLevel":"kCLClientInUseLevelDecayingUserEngagement", "coming":0\}\
padr\'e3o	14:07:32.751271-0300	locationd	\{"msg":"#CLIUA updating AssertionRecord", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelDecayingUserEngagement"\}\
padr\'e3o	14:07:32.751312-0300	locationd	\{"msg":"#CLIUA AssertionRecord updated", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelNotInUse"\}\
padr\'e3o	14:07:32.751557-0300	locationd	\{"msg":"#CLIUA in-use level changed for client", "ClientKey":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:32.751591-0300	locationd	\{"msg":"#Warning #ClientResolution the passed keyPath is not registered. Resolving to #nullCKP", "InputCKP":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:35.078138-0300	locationd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.078212-0300	locationd	\{"msg":"invoking applicationStateChange handler", "StateChangeData":"\{\\n    BKSApplicationStateAppIsFrontmost = 1;\\n    BKSApplicationStateExtensionKey = 0;\\n    SBApplicationStateDisplayIDKey = \\"com.tecbiz.tecbizassociadospush\\";\\n    SBApplicationStateKey = 8;\\n    SBApplicationStateProcessIDKey = 541;\\n    SBMostElevatedStateForProcessID = 8;\\n\}"\}\
padr\'e3o	14:07:35.078299-0300	locationd	\{"msg":"RBS #AppMonitor process monitor update handler invoked", "pid":541, "bundleID":"com.tecbiz.tecbizassociadospush", "state":"RunningScheduled"\}\
padr\'e3o	14:07:35.078342-0300	locationd	\{"msg":"Not Posting Application State Change Notification via legacy path", "notification":"ForegroundRunning", "pid":541, "bundleId":"com.tecbiz.tecbizassociadospush"\}\
padr\'e3o	14:07:35.078407-0300	locationd	\{"msg":"RBS #AppMonitor Post Application State Change Notification", "notification":"ForegroundRunning", "pid":541, "bundleId":"com.tecbiz.tecbizassociadospush"\}\
padr\'e3o	14:07:35.078933-0300	locationd	\{"msg":"#Warning #ClientResolution the passed keyPath is not registered. Resolving to #nullCKP", "InputCKP":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:35.079704-0300	locationd	\{"msg":"#CLIUA AppMonitor notification", "notification":"ForegroundRunning", "pid":541, "bundleId":"com.tecbiz.tecbizassociadospush", "ClientKey":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:35.079762-0300	locationd	\{"msg":"#CLIUA Marking change", "clientKey":"icom.tecbiz.tecbizassociadospush:", "reason":"Process state from RunningBoard", "AssertionLevel":"kCLClientInUseLevelDirectUserEngagement", "coming":1\}\
padr\'e3o	14:07:35.079824-0300	locationd	\{"msg":"#CLIUA updating AssertionRecord", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelNotInUse"\}\
padr\'e3o	14:07:35.079863-0300	locationd	\{"msg":"#CLIUA AssertionRecord updated", "ClientKey":"icom.tecbiz.tecbizassociadospush:", "AssertionLevel":"kCLClientInUseLevelDirectUserEngagement"\}\
padr\'e3o	14:07:35.079901-0300	locationd	\{"msg":"#CLIUA in-use level changed for client", "ClientKey":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:35.079935-0300	locationd	\{"msg":"#Warning #ClientResolution the passed keyPath is not registered. Resolving to #nullCKP", "InputCKP":"icom.tecbiz.tecbizassociadospush:"\}\
padr\'e3o	14:07:35.108706-0300	locationd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:34.900213-0300	lsd	[FBSSystemService][0x48ce] Sending request to open "com.tecbiz.tecbizassociadospush"\
padr\'e3o	14:07:35.004705-0300	lsd	[FBSSystemService][0x48ce] Request successful: <BSProcessHandle: 0xb2681b750; TecBizAssociado:541; valid: YES>\
padr\'e3o	14:07:35.056921-0300	mDNSResponder	[R413] getaddrinfo start -- flags: 0xC000D000, ifindex: 0, protocols: 0, hostname: <mask.hash: 'O9AQHfqSWsL6SUwYfUTYTQ=='>, options: 0x0 \{\}, client pid: 541 (TecBizAssociado)\
padr\'e3o	14:07:35.105124-0300	mDNSResponder	[R413] getaddrinfo stop -- hostname: <mask.hash: 'O9AQHfqSWsL6SUwYfUTYTQ=='>, client pid: 541 (TecBizAssociado)\
padr\'e3o	14:07:22.501625-0300	PerfPowerServices	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.656709-0300	PerfPowerServices	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.748744-0300	PerfPowerServices	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.563462-0300	PerfPowerServices	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.071618-0300	PerfPowerServices	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.109570-0300	PerfPowerServices	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.444272-0300	powerd	Process runningboardd.33 Created SystemIsActive "app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>33-219-648:FBSceneSnapshotAction:sceneID:com.tecbiz.tecbizassociadospush-default" age:00:00:00  id:51539640699 [System: PrevIdle SysAct]\
padr\'e3o	14:07:22.740673-0300	powerd	Process runningboardd.33 Released SystemIsActive "app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>33-219-648:FBSceneSnapshotAction:sceneID:com.tecbiz.tecbizassociadospush-default" age:00:00:00  id:51539640699 [System: PrevIdle SysAct]\
padr\'e3o	14:07:22.440944-0300	runningboardd	Acquiring assertion targeting [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] from originator [osservice<com.apple.SpringBoard>:219] with description <RBSAssertionDescriptor| "FBSceneSnapshotAction:sceneID:com.tecbiz.tecbizassociadospush-default" ID:33-219-648 target:541 attributes:[\
	<RBSDomainAttribute| domain:"com.apple.frontboard" name:"SceneSnapshotAction" sourceEnvironment:"(null)">,\
	<RBSDurationAttribute| invalidationDuration:5.00 warningDuration:0.00 startPolicy:Fixed endPolicy:Invalidate>\
	]>\
padr\'e3o	14:07:22.440982-0300	runningboardd	Assertion 33-219-648 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) will be created as active\
padr\'e3o	14:07:22.441833-0300	runningboardd	Invalidating assertion 33-219-644 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541](UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default)) from originator [osservice<com.apple.SpringBoard>:219]\
padr\'e3o	14:07:22.441953-0300	runningboardd	Acquiring assertion targeting [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] from originator [osservice<com.apple.SpringBoard>:219] with description <RBSAssertionDescriptor| "FBWorkspace (ForegroundNonFocal)" ID:33-219-649 target:541 attributes:[\
	<RBSDomainAttribute| domain:"com.apple.frontboard" name:"Workspace-ForegroundActive" sourceEnvironment:"(null)">,\
	<RBSAcquisitionCompletionAttribute| policy:AfterApplication>,\
	<RBSDomainAttribute| domain:"com.apple.frontboard" name:"Visibility" sourceEnvironment:"(null)">\
	]>\
padr\'e3o	14:07:22.441987-0300	runningboardd	Assertion 33-219-649 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) will be created as active\
padr\'e3o	14:07:22.442352-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-active (role: UserInteractiveFocal) (endowments: <private>)\
padr\'e3o	14:07:22.442786-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-active (role: UserInteractiveFocal) (endowments: <private>)\
padr\'e3o	14:07:22.444034-0300	runningboardd	Invalidating assertion 33-219-647 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) from originator [osservice<com.apple.SpringBoard>:219]\
padr\'e3o	14:07:22.478900-0300	runningboardd	Acquiring assertion targeting [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] from originator [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] with description <RBSAssertionDescriptor| "Shared Background Assertion 1 for com.tecbiz.tecbizassociadospush" ID:33-541-650 target:541 attributes:[\
	<RBSLegacyAttribute| requestedReason:FinishTask reason:FinishTask flags:( PreventTaskSuspend )>,\
	<RBSAcquisitionCompletionAttribute| policy:AfterValidation>\
	]>\
padr\'e3o	14:07:22.478935-0300	runningboardd	Assertion 33-541-650 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) will be created as inactive as start-time-defining assertions exist\
padr\'e3o	14:07:22.604496-0300	runningboardd	Inheritance changeset: <RBSInheritanceChangeSet| gained:\{(\
)\} lost:\{(\
    <RBSInheritance| environment:(none) name:com.apple.frontboard.visibility origID:33-219-647 0>,\
    <RBSInheritance| environment:UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default name:com.apple.frontboard.visibility origID:33-219-644 payload 331416388402482309>\
)\}>\
padr\'e3o	14:07:22.604562-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-active (role: UserInteractiveNonFocal) (endowments: <private>)\
padr\'e3o	14:07:22.604878-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Set darwin role to: UserInteractiveNonFocal\
padr\'e3o	14:07:22.670629-0300	runningboardd	Invalidating assertion 33-219-649 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) from originator [osservice<com.apple.SpringBoard>:219]\
padr\'e3o	14:07:22.671343-0300	runningboardd	Invalidating assertion 33-219-648 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) from originator [osservice<com.apple.SpringBoard>:219]\
padr\'e3o	14:07:22.678189-0300	runningboardd	Invalidating assertion 33-541-650 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) from originator [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]\
padr\'e3o	14:07:22.737950-0300	runningboardd	Removed last relative-start-date-defining assertion for process app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>\
padr\'e3o	14:07:22.740598-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-suspended (role: None) (endowments: (null))\
padr\'e3o	14:07:22.740773-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Set jetsam priority to 0 [0] flag[1]\
padr\'e3o	14:07:22.740925-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Suspending task.\
padr\'e3o	14:07:22.742156-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Shutdown sockets (SVC)\
padr\'e3o	14:07:22.742185-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Set darwin role to: None\
padr\'e3o	14:07:22.742342-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] visiblity is no\
padr\'e3o	14:07:22.747343-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] check if suspended process is holding locks\
padr\'e3o	14:07:25.557754-0300	runningboardd	Acquiring assertion targeting [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] from originator [osservice<com.apple.dasd>:80] with description <RBSAssertionDescriptor| "DAS: Application is docked." ID:33-80-664 target:541 attributes:[\
	<RBSDomainAttribute| domain:"com.apple.dasd" name:"DockApp" sourceEnvironment:"(null)">\
	]>\
padr\'e3o	14:07:25.557859-0300	runningboardd	Assertion 33-80-664 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) will be created as active\
padr\'e3o	14:07:25.558552-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Set jetsam priority to 30 [0] flag[1]\
padr\'e3o	14:07:25.559156-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-suspended (role: None) (endowments: (null))\
padr\'e3o	14:07:35.008400-0300	runningboardd	Acquiring assertion targeting [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] from originator [osservice<com.apple.SpringBoard>:219] with description <RBSAssertionDescriptor| "FBWorkspace (ForegroundFocal)" ID:33-219-804 target:541 attributes:[\
	<RBSDomainAttribute| domain:"com.apple.frontboard" name:"Workspace-ForegroundFocal" sourceEnvironment:"(null)">,\
	<RBSAcquisitionCompletionAttribute| policy:AfterApplication>,\
	<RBSDomainAttribute| domain:"com.apple.frontboard" name:"Visibility" sourceEnvironment:"(null)">\
	]>\
padr\'e3o	14:07:35.008435-0300	runningboardd	Assertion 33-219-804 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541]) will be created as active\
padr\'e3o	14:07:35.010055-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-active (role: UserInteractiveFocal) (endowments: <private>)\
padr\'e3o	14:07:35.010104-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Set jetsam priority to 100 [0] flag[1]\
padr\'e3o	14:07:35.010152-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Resuming task.\
padr\'e3o	14:07:35.010198-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] Set darwin role to: UserInteractiveFocal\
padr\'e3o	14:07:35.010438-0300	runningboardd	[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541] visiblity is yes\
padr\'e3o	14:07:35.031919-0300	runningboardd	Acquiring assertion targeting [app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541](UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default) from originator [osservice<com.apple.SpringBoard>:219] with description <RBSAssertionDescriptor| "injecting inherited from "UIScene:com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard" to 541<UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default>" ID:33-219-806 target:541<UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default> attributes:[\
	<RBSHereditaryGrant| endowmentNamespace:com.apple.boardservices.endpoint-injection UIScene:com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard>,\
	<RBSHereditaryGrant| endowmentNamespace:com.apple.frontboard.visibility UIScene:com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard>\
	]>\
padr\'e3o	14:07:35.031971-0300	runningboardd	Assertion 33-219-806 (target:[app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>:541](UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default)) will be created as active\
padr\'e3o	14:07:35.033240-0300	runningboardd	Calculated state for app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>: running-active (role: UserInteractiveFocal) (endowments: <private>)\
padr\'e3o	14:07:35.035012-0300	runningboardd	Inheritance changeset: <RBSInheritanceChangeSet| gained:\{(\
    <RBSInheritance| environment:UIScene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default name:com.apple.frontboard.visibility origID:33-219-806 payload 331416388402482309>\
)\} lost:\{(\
)\}>\
padr\'e3o	14:07:21.491599-0300	SpringBoard	RX com.tecbiz.tecbizassociadospush(541) focusApplicationWithPID:541 stealKeyboard:Y\
    context:<contextID:992860121 sceneID:com.tecbiz.tecbizassociadospush-default>\
padr\'e3o	14:07:21.521279-0300	SpringBoard	[SwitcherOrientation] outSwitcherOrientation: portrait (1), outElementsOrientations: \{\
    "sceneID:com.tecbiz.tecbizassociadospush-default" = 1;\
\}\
padr\'e3o	14:07:21.525949-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBGestureSwitcherModifierEvent: 0xb74057980; type: PanGesture; gestureID: A71637B6-1EA4-4ED4-97F9-FE0FDAA35EB2; phase: Begin; gestureType: DeckSwipeUp; selectedAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>\
padr\'e3o	14:07:21.528104-0300	SpringBoard	[Main (EmbeddedDisplay)] handle response:\
<SBSwitcherModifierEventResponse: 0xb764cd1d0> \{\
    <SBSwitcherModifierEventResponse: 0xb764cc8a0> \{\
	    <SBSwitcherModifierEventResponse: 0xb764ce2b0> \{\
		    <SBUpdateLayoutSwitcherEventResponse: 0xb763913c0; updateVisibleItems; mode: None>;\
		    <SBUpdateLayoutSwitcherEventResponse: 0xb75939880; style; mode: None>;\
		    <SBUpdateLayoutSwitcherEventResponse: 0xb783c1500; layout; mode: RetargetingAnimations>;\
		\};\
	    <SBIconViewVisibilitySwitcherEventResponse: 0xb73b16ad0; visible: NO; animationSettings: 0x0; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
	    <SBEmitSBEventSwitcherEventResponse: 0xb76d5e0c0; eventType: SwipeUpGestureBegan; payloadCount: 4; eventType: SwipeUpGestureBegan> payload = \{\
    kSBSAnalyticsSwipeUpYCoord = 798.6666666666666;\
    kSBSAnalyticsSwipeUpTimestampDelta = 0.03813408333343205;\
    kSBSAnalyticsSwipeUpOrientation = 1;\
    kSBSAnalyticsSwipeUpXCoord = 238.3333333333333;\
padr\'e3o	14:07:21.547753-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: 'systemGesture' for reason: updateAllScenesForBand - Assertion added.\
padr\'e3o	14:07:21.587678-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBGestureSwitcherModifierEvent: 0xb74057f20; type: PanGesture; gestureID: A71637B6-1EA4-4ED4-97F9-FE0FDAA35EB2; phase: End; gestureType: DeckSwipeUp; selectedAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>\
padr\'e3o	14:07:21.588810-0300	SpringBoard	[Main (EmbeddedDisplay)] handle response:\
<SBSwitcherModifierEventResponse: 0xb759b5290> \{\
    <SBSwitcherModifierEventResponse: 0xb759b55c0> \{\
	    <SBUpdateLayoutSwitcherEventResponse: 0xb78401300; setNeedsLayout; mode: None>;\
	    <SBSwitcherModifierEventResponse: 0xb7653ad60> \{\
		    <SBIconViewVisibilitySwitcherEventResponse: 0xb7830ba70; visible: YES; animationSettings: <SBFFluidBehaviorSettings: 0xb72fe8780; Name: iconFadeInSettings> animationSettings = <BSSpringAnimationSettings:0xb784ed6c0 mass=1.000000 stiffness=157.913670 damping=25.132741 epsilon=0.001000 initialVelocity=0.000000 delay=0.000000 interval=0.000000 range=\{0.000000,0.000000,0.000000\} reason=0 timing=linear speed=1.000000>;\
preferredFrameRateRange = <PTFrameRateRangeSettings: 0xb73692cb0> frameRateRange = (CAFrameRateRangeDefault);\
highFrameRateReason = 0;;; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		    <SBPerformTransitionSwitcherEventResponse: 0xb78403100; gestureInitia\
padr\'e3o	14:07:21.592623-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBTransitionSwitcherModifierEvent: 0xb784d1380; type: MainTransition; transitionID: 9A1934E2-F179-466E-90D5-B7659D04D4BE; phase: Prepare; animated: YES; fromAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>; toAppLayout: 0x0; fromEnvironmentMode: application; toEnvironmentMode: home-screen; fromSpaceConfiguration: full; toSpaceConfiguration: undefined; pendingTermination: \{(\
)\}; isGestureInitiated: YES; morphingPIPLayoutRole: undefined>\
padr\'e3o	14:07:21.592729-0300	SpringBoard	[Main (EmbeddedDisplay)] handle response:\
<SBSwitcherModifierEventResponse: 0xb764cd1d0> \{\
    <SBInvalidateAdjustedAppLayoutsSwitcherEventResponse: 0xb764ccae0>;\
    <SBSwitcherModifierEventResponse: 0xb764ccb70> \{\
	    <SBInvalidateAdjustedAppLayoutsSwitcherEventResponse: 0xb764cc000>;\
	    <SBSwitcherModifierEventResponse: 0xb764cd7d0> \{\
		    <SBIconOverlayVisibilitySwitcherEventResponse: 0xb78402c40; visible: YES; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		    <SBIconViewVisibilitySwitcherEventResponse: 0xb78309630; visible: NO; animationSettings: 0x0; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		    <SBNotifyIconWillZoomDownSwitcherEventResponse: 0xb764ce0a0; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		    <SBMatchMoveToIconViewSwitcherEventResponse: 0xb76166880; active: YES; appLayout: <SBAppLayout: 0xb72ce3b\
padr\'e3o	14:07:21.597759-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: 'systemGesture, systemAnimation' for reason: updateAllScenesForBand - Assertion added.\
padr\'e3o	14:07:21.597918-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: 'systemAnimation' for reason: updateAllScenesForBand - Assertion removed.\
padr\'e3o	14:07:21.598186-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBTransitionSwitcherModifierEvent: 0xb784d1380; type: MainTransition; transitionID: 9A1934E2-F179-466E-90D5-B7659D04D4BE; phase: Animate; animated: YES; fromAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>; toAppLayout: 0x0; fromEnvironmentMode: application; toEnvironmentMode: home-screen; fromSpaceConfiguration: full; toSpaceConfiguration: undefined; pendingTermination: \{(\
)\}; isGestureInitiated: YES; morphingPIPLayoutRole: undefined>\
padr\'e3o	14:07:21.601161-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: 'systemGesture, systemAnimation' for reason: updateAllScenesForBand - Assertion added.\
padr\'e3o	14:07:21.605226-0300	SpringBoard	[coordinator] _removeSceneFromRecents didRemoveExternalForegroundApplicationSceneHandle pid:541 scene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default now:<empty>\
padr\'e3o	14:07:21.605251-0300	SpringBoard	[com.apple.springboard] removing scene: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid: 541 for reason: didRemoveExternalForegroundApplicationSceneHandle\
padr\'e3o	14:07:21.618216-0300	SpringBoard	rules: (scene setting) REMOVED keyboardFocus environment from scene: sceneID:com.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:22.411041-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBTransitionSwitcherModifierEvent: 0xb784d1800; type: MainTransition; transitionID: 9A1934E2-F179-466E-90D5-B7659D04D4BE; phase: Complete; animated: YES; fromAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>; toAppLayout: 0x0; fromEnvironmentMode: application; toEnvironmentMode: home-screen; fromSpaceConfiguration: full; toSpaceConfiguration: undefined; pendingTermination: \{(\
)\}; isGestureInitiated: YES; morphingPIPLayoutRole: undefined>\
padr\'e3o	14:07:22.412614-0300	SpringBoard	[Main (EmbeddedDisplay)] handle response:\
<SBSwitcherModifierEventResponse: 0xb7463e250> \{\
    <SBInvalidateAdjustedAppLayoutsSwitcherEventResponse: 0xb7463d830>;\
    <SBSwitcherModifierEventResponse: 0xb7463e610> \{\
	    <SBInvalidateAdjustedAppLayoutsSwitcherEventResponse: 0xb7463dcb0>;\
	    <SBSwitcherModifierEventResponse: 0xb7463dd40> \{\
		    <SBIconOverlayVisibilitySwitcherEventResponse: 0xb78513400; visible: NO; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		    <SBIconViewVisibilitySwitcherEventResponse: 0xb76b3d680; visible: YES; animationSettings: 0x0; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		    <SBMatchMoveToIconViewSwitcherEventResponse: 0xb7597bc60; active: NO; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>>;\
		\};\
	\};\
\}\
padr\'e3o	14:07:22.418787-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: 'systemGesture' for reason: updateAllScenesForBand - Assertion removed.\
padr\'e3o	14:07:22.431615-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Removing parent scene.\
padr\'e3o	14:07:22.432895-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: '(none)' for reason: scene settings update - settings are NOT eligible for deactivation reasons.\
padr\'e3o	14:07:22.433449-0300	SpringBoard	No longer tracking: <FBScene: 0xb7752ab00; FBSceneManager:sceneID:com.tecbiz.tecbizassociadospush-default>\
padr\'e3o	14:07:22.435206-0300	SpringBoard	<SBSceneSnapshotRequestor: 0xb729f4420; debugName: LCD> [sceneID:com.tecbiz.tecbizassociadospush-default] Requesting 2 snapshot(s) because the scene actually moved to the background\
padr\'e3o	14:07:22.435394-0300	SpringBoard	Created: <FBSceneSnapshotAction: 0xb77287480; sceneID:com.tecbiz.tecbizassociadospush-default>\
padr\'e3o	14:07:22.441169-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Sending action(s) in update: <FBSceneSnapshotAction: 0x00db0012>\
padr\'e3o	14:07:22.441203-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] scene content state changed: notReady\
padr\'e3o	14:07:22.441231-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene lifecycle state did change: Background\
padr\'e3o	14:07:22.441286-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene activity mode did change: support (transient).\
padr\'e3o	14:07:22.441310-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene assertion state did change: ForegroundNonFocal.\
padr\'e3o	14:07:22.441338-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Workspace assertion state did change: ForegroundNonFocal (acquireAssertion = YES).\
padr\'e3o	14:07:22.442914-0300	SpringBoard	<BSCompoundAssertion:0xb7369a780> (SBApplicationAppProtectionAssistant: 0xb7369a7c0 - com.tecbiz.tecbizassociadospush) invalidate acq:0xb75d70a20 count:1\
padr\'e3o	14:07:22.442962-0300	SpringBoard	All scenes dismissed for <APApplication: com.tecbiz.tecbizassociadospush>\
padr\'e3o	14:07:22.443672-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Setting process visibility to: Background\
padr\'e3o	14:07:22.443795-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Sending scene action [Logical Deactivate][0xc562] to process 0xb78380000 (watchdog: 10.00s)\
padr\'e3o	14:07:22.445005-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.475028-0300	SpringBoard	<SBFullScreenSwitcherLiveContentOverlayCoordinator: 0xb754d55e0> Removing SwitcherScene overlay for: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>, animated: NO\
padr\'e3o	14:07:22.479769-0300	SpringBoard	com.tecbiz.tecbizassociadospush(541) lostConnection (invalidation)\
padr\'e3o	14:07:22.482783-0300	SpringBoard	RX com.tecbiz.tecbizassociadospush(541) setWindowContextID:0 windowState:Disabled level:0.0\
    focusContext:(null)\
padr\'e3o	14:07:22.504389-0300	SpringBoard	Application process state changed for com.tecbiz.tecbizassociadospush: <SBApplicationProcessState: 0xb75d08b40; pid: 541; taskState: Running; visibility: Background>\
padr\'e3o	14:07:22.529407-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Received action(s): <FBSSceneSnapshotRequestAction: 0x021d0000>\
padr\'e3o	14:07:22.529551-0300	SpringBoard	<SBSceneSnapshotRequestor: 0xb729f4420; debugName: LCD> [sceneID:com.tecbiz.tecbizassociadospush-default] Got FBSSceneSnapshotRequestTypePerform (0xb75d70700)\
padr\'e3o	14:07:22.576801-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] START liquidating the old, as snapshot generation succeeded\
padr\'e3o	14:07:22.581476-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] deleting old snapshots: (\
    "<XBApplicationSnapshot: 0xb738c3480; identifier: CCAAA212-2F16-44A9-9EB0-0A9C0AA6566E; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Light> \{\\n    creationDate = 16 de setembro de 2025 \\U00e0s 14:02:26 BRT;\\n    keepsImageAccessUntilExpiration = NO;\\n    hasGenerationContext = NO;\\n    purge = high;\\n    context = \{\\n        contentType = SceneContent;\\n        fullScreen = YES;\\n        referenceSize = \{390, 844\};\\n        contentFrame = \{\{0, 0\}, \{390, 844\}\};\\n        interfaceOrientation = Portrait;\\n        userInterfaceStyle = Light;\\n        customSafeAreaInsets = <XBDisplayEdgeInsetsWrapper: 0xb73a1d650; topInset: 47.0; leftInset: 0.0; bottomInset: 34.0; rightInset: 0.0>;\\n        additionalContext = \{\\n            statusBarSettings = <XBStatusBarSettings: 0xb73971170; hidden: NO; style: UIStatusBarStyleDarkContent; backgroundActivityEnabled: NO>;\\n        \}\\n    \}\\n    imageContext = \{\\n        scale = 3.0;\\n        opaque = YES;\\n        fileRelativeLocation = default;\\n        path = /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/<EMAIL>;\\n        fileFormat = ktx;\\n    \}\\n    variants = \{\\n        downscaled = <XBApplicationSnapshot: 0xb738c3640; identifier: 923CFA3B-2119-4F7B-B9D7-58E9F6D7454D; variantID: downscaled; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Light> \{\\n            creationDate = 16 de setembro de 2025 \\U00e0s 14:02:26 BRT;\\n            lastUsedDate = 16 de setembro de 2025 \\U00e0s 14:07:21 BRT;\\n            keepsImageAccessUntilExpiration = NO;\\n            hasGenerationContext = NO;\\n            purge = high;\\n            context = \{\\n                contentType = SceneContent;\\n                fullScreen = YES;\\n                referenceSize = \{390, 844\};\\n                contentFrame = \{\{0, 0\}, \{390, 844\}\};\\n                interfaceOrientation = Portrait;\\n                userInterfaceStyle = Light;\\n                customSafeAreaInsets = <XBDisplayEdgeInsetsWrapper: 0xb73a1d650; topInset: 47.0; leftInset: 0.0; bottomInset: 34.0; rightInset: 0.0>;\\n                additionalContext = \{\\n                    statusBarSettings = <XBStatusBarSettings: 0xb73971170; hidden: NO; style: UIStatusBarStyleDarkContent; backgroundActivityEnabled: NO>;\\n                \}\\n            \}\\n            imageContext = \{\\n                scale = 3.0;\\n                opaque = YES;\\n                fileRelativeLocation = default;\\n                path = /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/downscaled/<EMAIL>;\\n                fileFormat = ktx;\\n            \}\\n        \};\\n    \}\\n\}",\
    "<XBApplicationSnapshot: 0xb738c3640; identifier: 923CFA3B-2119-4F7B-B9D7-58E9F6D7454D; variantID: downscaled; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Light> \{\\n    creationDate = 16 de setembro de 2025 \\U00e0s 14:02:26 BRT;\\n    lastUsedDate = 16 de setembro de 2025 \\U00e0s 14:07:21 BRT;\\n    keepsImageAccessUntilExpiration = NO;\\n    hasGenerationContext = NO;\\n    purge = high;\\n    context = \{\\n        contentType = SceneContent;\\n        fullScreen = YES;\\n        referenceSize = \{390, 844\};\\n        contentFrame = \{\{0, 0\}, \{390, 844\}\};\\n        interfaceOrientation = Portrait;\\n        userInterfaceStyle = Light;\\n        customSafeAreaInsets = <XBDisplayEdgeInsetsWrapper: 0xb73a1d650; topInset: 47.0; leftInset: 0.0; bottomInset: 34.0; rightInset: 0.0>;\\n        additionalContext = \{\\n            statusBarSettings = <XBStatusBarSettings: 0xb73971170; hidden: NO; style: UIStatusBarStyleDarkContent; backgroundActivityEnabled: NO>;\\n        \}\\n    \}\\n    imageContext = \{\\n        scale = 3.0;\\n        opaque = YES;\\n        fileRelativeLocation = default;\\n        path = /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/downscaled/<EMAIL>;\\n        fileFormat = ktx;\\n    \}\\n\}"\
)\
padr\'e3o	14:07:22.582870-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Snapshot data for <XBApplicationSnapshot: 0xb783d72c0; \'85D5BBA93777B2> [com.tecbiz.tecbizassociadospush] written to file: /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/<EMAIL>\
padr\'e3o	14:07:22.583066-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshots: (\
    "<XBApplicationSnapshot: 0xb738c3480; identifier: CCAAA212-2F16-44A9-9EB0-0A9C0AA6566E; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Light>",\
    "<XBApplicationSnapshot: 0xb738c3640; identifier: 923CFA3B-2119-4F7B-B9D7-58E9F6D7454D; variantID: downscaled; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Light>"\
)\
padr\'e3o	14:07:22.583493-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshot <XBApplicationSnapshot: 0xb738c3480; \'850A9C0AA6566E> [com.tecbiz.tecbizassociadospush] for reason: _contentType: SceneContent(0)\
padr\'e3o	14:07:22.583578-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshot <XBApplicationSnapshot: 0xb738c3640; \'8558E9F6D7454D> [com.tecbiz.tecbizassociadospush] for reason: _contentType: SceneContent(0)\
padr\'e3o	14:07:22.583780-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting paths: <private>\
padr\'e3o	14:07:22.584767-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] deleting old suspend snapshots using predicate: <XBApplicationSnapshotPredicate: 0xb73972f80> \{\
    name = SBSuspendSnapshot;\
    contentTypeMask = SceneContent;\
\}\
padr\'e3o	14:07:22.585014-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshot <XBApplicationSnapshot: 0xb783d79c0; \'85C3B8BDB84AEB> [com.tecbiz.tecbizassociadospush] for reason: _contentType: SceneContent(0)\
padr\'e3o	14:07:22.585283-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] STOP liquidating the old\
padr\'e3o	14:07:22.585314-0300	SpringBoard	<SBSceneSnapshotRequestor: 0xb729f4420; debugName: LCD> [sceneID:com.tecbiz.tecbizassociadospush-default] Snapshot request (0xb75d70700) complete with error: 0\
padr\'e3o	14:07:22.620557-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.643789-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Snapshot data for <XBApplicationSnapshot: 0xb783d48c0; \'854B4848B2939E> [com.tecbiz.tecbizassociadospush] written to file: /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/downscaled/<EMAIL>\
padr\'e3o	14:07:22.648324-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Received action(s): <FBSSceneSnapshotRequestAction: 0x021d0001>\
padr\'e3o	14:07:22.648438-0300	SpringBoard	<SBSceneSnapshotRequestor: 0xb729f4420; debugName: LCD> [sceneID:com.tecbiz.tecbizassociadospush-default] Got FBSSceneSnapshotRequestTypePerform (0xb75d71d40)\
padr\'e3o	14:07:22.665076-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] START liquidating the old, as snapshot generation succeeded\
padr\'e3o	14:07:22.665288-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] deleting old snapshots: (\
    "<XBApplicationSnapshot: 0xb738c3100; identifier: 0C639E65-34D3-460F-B3FF-4C32D85143C3; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Dark> \{\\n    creationDate = 16 de setembro de 2025 \\U00e0s 14:02:26 BRT;\\n    keepsImageAccessUntilExpiration = NO;\\n    hasGenerationContext = NO;\\n    purge = high;\\n    context = \{\\n        contentType = SceneContent;\\n        fullScreen = YES;\\n        referenceSize = \{390, 844\};\\n        contentFrame = \{\{0, 0\}, \{390, 844\}\};\\n        interfaceOrientation = Portrait;\\n        userInterfaceStyle = Dark;\\n        customSafeAreaInsets = <XBDisplayEdgeInsetsWrapper: 0xb73a1d530; topInset: 47.0; leftInset: 0.0; bottomInset: 34.0; rightInset: 0.0>;\\n        additionalContext = \{\\n            statusBarSettings = <XBStatusBarSettings: 0xb73971150; hidden: NO; style: UIStatusBarStyleDarkContent; backgroundActivityEnabled: NO>;\\n        \}\\n    \}\\n    imageContext = \{\\n        scale = 3.0;\\n        opaque = YES;\\n        fileRelativeLocation = default;\\n        path = /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/<EMAIL>;\\n        fileFormat = ktx;\\n    \}\\n    variants = \{\\n        downscaled = <XBApplicationSnapshot: 0xb738c32c0; identifier: C4E1229F-D861-4162-9B5A-541721109727; variantID: downscaled; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Dark> \{\\n            creationDate = 16 de setembro de 2025 \\U00e0s 14:02:26 BRT;\\n            keepsImageAccessUntilExpiration = NO;\\n            hasGenerationContext = NO;\\n            purge = high;\\n            context = \{\\n                contentType = SceneContent;\\n                fullScreen = YES;\\n                referenceSize = \{390, 844\};\\n                contentFrame = \{\{0, 0\}, \{390, 844\}\};\\n                interfaceOrientation = Portrait;\\n                userInterfaceStyle = Dark;\\n                customSafeAreaInsets = <XBDisplayEdgeInsetsWrapper: 0xb73a1d530; topInset: 47.0; leftInset: 0.0; bottomInset: 34.0; rightInset: 0.0>;\\n                additionalContext = \{\\n                    statusBarSettings = <XBStatusBarSettings: 0xb73971150; hidden: NO; style: UIStatusBarStyleDarkContent; backgroundActivityEnabled: NO>;\\n                \}\\n            \}\\n            imageContext = \{\\n                scale = 3.0;\\n                opaque = YES;\\n                fileRelativeLocation = default;\\n                path = /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/downscaled/<EMAIL>;\\n                fileFormat = ktx;\\n            \}\\n        \};\\n    \}\\n\}",\
    "<XBApplicationSnapshot: 0xb738c32c0; identifier: C4E1229F-D861-4162-9B5A-541721109727; variantID: downscaled; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Dark> \{\\n    creationDate = 16 de setembro de 2025 \\U00e0s 14:02:26 BRT;\\n    keepsImageAccessUntilExpiration = NO;\\n    hasGenerationContext = NO;\\n    purge = high;\\n    context = \{\\n        contentType = SceneContent;\\n        fullScreen = YES;\\n        referenceSize = \{390, 844\};\\n        contentFrame = \{\{0, 0\}, \{390, 844\}\};\\n        interfaceOrientation = Portrait;\\n        userInterfaceStyle = Dark;\\n        customSafeAreaInsets = <XBDisplayEdgeInsetsWrapper: 0xb73a1d530; topInset: 47.0; leftInset: 0.0; bottomInset: 34.0; rightInset: 0.0>;\\n        additionalContext = \{\\n            statusBarSettings = <XBStatusBarSettings: 0xb73971150; hidden: NO; style: UIStatusBarStyleDarkContent; backgroundActivityEnabled: NO>;\\n        \}\\n    \}\\n    imageContext = \{\\n        scale = 3.0;\\n        opaque = YES;\\n        fileRelativeLocation = default;\\n        path = /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/downscaled/<EMAIL>;\\n        fileFormat = ktx;\\n    \}\\n\}"\
)\
padr\'e3o	14:07:22.665620-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Snapshot data for <XBApplicationSnapshot: 0xb783d7d40; \'85460038DBB455> [com.tecbiz.tecbizassociadospush] written to file: /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/<EMAIL>\
padr\'e3o	14:07:22.665709-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshots: (\
    "<XBApplicationSnapshot: 0xb738c3100; identifier: 0C639E65-34D3-460F-B3FF-4C32D85143C3; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Dark>",\
    "<XBApplicationSnapshot: 0xb738c32c0; identifier: C4E1229F-D861-4162-9B5A-541721109727; variantID: downscaled; contentType: SceneContent; referenceSize: \{390, 844\}; interfaceOrientation: Portrait; userInterfaceStyle: Dark>"\
)\
padr\'e3o	14:07:22.665762-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshot <XBApplicationSnapshot: 0xb738c3100; \'854C32D85143C3> [com.tecbiz.tecbizassociadospush] for reason: _contentType: SceneContent(0)\
padr\'e3o	14:07:22.665810-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting snapshot <XBApplicationSnapshot: 0xb738c32c0; \'85541721109727> [com.tecbiz.tecbizassociadospush] for reason: _contentType: SceneContent(0)\
padr\'e3o	14:07:22.665855-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Deleting paths: <private>\
padr\'e3o	14:07:22.665926-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] deleting old suspend snapshots using predicate: <XBApplicationSnapshotPredicate: 0xb73973160> \{\
    name = SBSuspendSnapshot;\
    contentTypeMask = SceneContent;\
\}\
padr\'e3o	14:07:22.665988-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] STOP liquidating the old\
padr\'e3o	14:07:22.666024-0300	SpringBoard	<SBSceneSnapshotRequestor: 0xb729f4420; debugName: LCD> [sceneID:com.tecbiz.tecbizassociadospush-default] Snapshot request (0xb75d71d40) complete with error: 0\
padr\'e3o	14:07:22.670108-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene action [Logical Deactivate][0xc562] completed with success: 1\
padr\'e3o	14:07:22.670270-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene activity mode did change: suspended.\
padr\'e3o	14:07:22.670295-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene assertion state did change: None.\
padr\'e3o	14:07:22.670320-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Workspace assertion state did change: None (acquireAssertion = NO).\
padr\'e3o	14:07:22.670720-0300	SpringBoard	Got response for <FBSceneSnapshotAction: 0xb77287480; sceneID:com.tecbiz.tecbizassociadospush-default>: success\
padr\'e3o	14:07:22.675229-0300	SpringBoard	<XBApplicationSnapshotManifestImpl: 0xb739c8780> [com.tecbiz.tecbizassociadospush] Snapshot data for <XBApplicationSnapshot: 0xb783d4540; \'8527F65F6659B6> [com.tecbiz.tecbizassociadospush] written to file: /private/var/mobile/Containers/Data/Application/8EBA1D99-D848-4E32-B297-BC4997BD2D03/Library/SplashBoard/Snapshots/sceneID:com.tecbiz.tecbizassociadospush-default/downscaled/<EMAIL>\
padr\'e3o	14:07:22.741256-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:22.741801-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Setting process task state to: Suspended\
padr\'e3o	14:07:22.741852-0300	SpringBoard	Application process state changed for com.tecbiz.tecbizassociadospush: <SBApplicationProcessState: 0xb75f01520; pid: 541; taskState: Suspended; visibility: Background>\
padr\'e3o	14:07:22.741931-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] com.tecbiz.tecbizassociadospush application state changed to <RBSProcessState| task:running-suspended debug:none>\
padr\'e3o	14:07:22.741981-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] Leaving topic enabled for application becoming background\
padr\'e3o	14:07:23.179975-0300	SpringBoard	Verify background audio activity for com.tecbiz.tecbizassociadospush, Recording: 0\
padr\'e3o	14:07:25.559772-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:34.902686-0300	SpringBoard	[FBSystemService][0x48ce] Received request to open "com.tecbiz.tecbizassociadospush" with url "tecbizapp:<private>" from lsd:120 on behalf of MobileSafari:465.\
padr\'e3o	14:07:34.903017-0300	SpringBoard	Received trusted open application request for "com.tecbiz.tecbizassociadospush" from <FBApplicationProcess: 0xb74573000; app<com.apple.mobilesafari>:465(v4FF)>.\
padr\'e3o	14:07:34.927396-0300	SpringBoard	Executing request: <SBMainWorkspaceTransitionRequest: 0xb77b8fc00; eventLabel: OpenApplication(sceneID:com.tecbiz.tecbizassociadospush-default)ForRequester(MobileSafari.465); display: Main; source: FBSystemService>\
padr\'e3o	14:07:34.940351-0300	SpringBoard	[FBWorkspaceEvent] Executing: <FBWorkspaceEvent: 0xb759f6240; OpenApplication(sceneID:com.tecbiz.tecbizassociadospush-default)ForRequester(MobileSafari.465)>\
padr\'e3o	14:07:34.946646-0300	SpringBoard	[SwitcherOrientation] outSwitcherOrientation: portrait (1), outElementsOrientations: \{\
    "sceneID:com.tecbiz.tecbizassociadospush-default" = 1;\
\}\
padr\'e3o	14:07:34.962589-0300	SpringBoard	<SBFullScreenSwitcherLiveContentOverlayCoordinator: 0xb754d55e0> Adding SwitcherScene overlay for: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>, animated: YES\
padr\'e3o	14:07:34.971975-0300	SpringBoard	_updatePreferences <Switcher>: \{\
    associatedSceneIdentifiersToSuppressInSystemAperture: (\
        sceneID:com.tecbiz.tecbizassociadospush-default\
    );\
    associatedBundleIdentifiersToSuppressInSystemAperture: (\
        com.tecbiz.tecbizassociadospush\
    );\
\}\
padr\'e3o	14:07:34.974042-0300	SpringBoard	modifying scene setting userInterfaceStyle to Light displayIdentity: Main forSceneManagers: Main <SBDeviceApplicationSceneHandle: 0xb75ebf640; sceneID: sceneID:com.tecbiz.tecbizassociadospush-default; scenePointer: 0xb7752ab00>\
padr\'e3o	14:07:35.003475-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBSceneReadySwitcherModifierEvent: 0xb72c6fa20; type: SceneReady; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main> \{\
    configuration = full;\
    itemsToLayoutAttributes = \{\
        sceneID:com.tecbiz.tecbizassociadospush-default = <SBDisplayItemLayoutAttributes: 0xb77524000; contentOrientation: "portrait (1)"; lastInteractionTime: 147462; sizingPolicy: maximized; size: unspecified; center: unspecified; occlusionState: unknown; userConfiguredSizeBeforeOverlapping: unspecified; unoccludedPeekingCenter: unspecified>;\
    \}\
    environment = main;\
    centerConfiguration = undefined;\
    preferredDisplayOrdinal = 0;\
    continuousExposeIdentifier = com.tecbiz.tecbizassociadospush;\
    layoutItems = \{\
        primary = <SBDisplayItem: 0xb73a1dfe0; type: App; bundleIdentifier: com.tecbiz.tecbizassociadospush; uniqueIdentifier: sceneID:com.tecbiz.tecbizassociadospush-default>;\
    \}\
\}>\
padr\'e3o	14:07:35.005105-0300	SpringBoard	Returning cached initialization context for com.tecbiz.tecbizassociadospush\
padr\'e3o	14:07:35.005273-0300	SpringBoard	Now tracking: <FBScene: 0xb7752ab00; FBSceneManager:sceneID:com.tecbiz.tecbizassociadospush-default>\
padr\'e3o	14:07:35.005300-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: 'systemAnimation' for reason: scene settings update - settings are eligible for deactivation reasons.\
padr\'e3o	14:07:35.005635-0300	SpringBoard	[coordinator] didAddExternalForegroundApplicationSceneHandle pid:541 scene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default now:<recentPIDs: [541]; recentSceneIdentityTokensByPID: \{541: [com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default]\}>\
padr\'e3o	14:07:35.005692-0300	SpringBoard	[com.apple.springboard] didAddExternalForegroundApplicationSceneHandle pid:541 scene:com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default now:<recentPIDs: [541]; recentSceneIdentityTokensByPID: \{541: [com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default]\}>\
padr\'e3o	14:07:35.007097-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Sending action(s) in update: UIOpenURLAction\
padr\'e3o	14:07:35.007121-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] scene content state changed: preparing\
padr\'e3o	14:07:35.007294-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene lifecycle state did change: Foreground\
padr\'e3o	14:07:35.007411-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene activity mode did change: default.\
padr\'e3o	14:07:35.007518-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene assertion state did change: ForegroundFocal.\
padr\'e3o	14:07:35.007569-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Workspace assertion state did change: ForegroundFocal (acquireAssertion = YES).\
padr\'e3o	14:07:35.007668-0300	SpringBoard	<BSCompoundAssertion:0xb7369a780> (SBApplicationAppProtectionAssistant: 0xb7369a7c0 - com.tecbiz.tecbizassociadospush) acquire for reason:NULL scene acq:0xb748971a0 count:1\
padr\'e3o	14:07:35.007817-0300	SpringBoard	scene will become FG visible for <APApplication: com.tecbiz.tecbizassociadospush>\
padr\'e3o	14:07:35.007966-0300	SpringBoard	auth result for <APApplication: com.tecbiz.tecbizassociadospush>: true (null)\
padr\'e3o	14:07:35.010818-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Setting process visibility to: Foreground\
padr\'e3o	14:07:35.011023-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Sending scene action [Logical Activate][0x1497] to process 0xb78380000 (watchdog: 10.00s)\
padr\'e3o	14:07:35.011534-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.012207-0300	SpringBoard	[app<com.tecbiz.tecbizassociadospush>:541] Setting process task state to: Running\
padr\'e3o	14:07:35.015397-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBTransitionSwitcherModifierEvent: 0xb785a5f80; type: MainTransition; transitionID: A9F3CE31-5E5E-4C1C-9102-44360C4EC97C; phase: Prepare; animated: YES; fromAppLayout: <SBAppLayout: 0xb72ce3a00; primary: com.apple.mobilesafari:17E04A246B3A; environment: main>; toAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>; pendingTermination: \{(\
)\}; morphingPIPLayoutRole: undefined>\
padr\'e3o	14:07:35.018604-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBTransitionSwitcherModifierEvent: 0xb78435e00; type: MainTransition; transitionID: A9F3CE31-5E5E-4C1C-9102-44360C4EC97C; phase: Animate; animated: YES; fromAppLayout: <SBAppLayout: 0xb72ce3a00; primary: com.apple.mobilesafari:17E04A246B3A; environment: main>; toAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>; pendingTermination: \{(\
)\}; morphingPIPLayoutRole: undefined>\
padr\'e3o	14:07:35.025368-0300	SpringBoard	[0xb7697a280:(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Scene action [Logical Activate][0x1497] completed with success: 1\
padr\'e3o	14:07:35.026567-0300	SpringBoard	RX com.tecbiz.tecbizassociadospush(541) startArbitration\
    expectedState:(null)\
    focusContext:<private>\
    hostingPIDs:<private> usingFence:Y withSuppression:0\
padr\'e3o	14:07:35.026624-0300	SpringBoard	Application process state changed for com.tecbiz.tecbizassociadospush: <SBApplicationProcessState: 0xb7609f8c0; pid: 541; taskState: Suspended; visibility: Foreground>\
padr\'e3o	14:07:35.027048-0300	SpringBoard	Application process state changed for com.tecbiz.tecbizassociadospush: <SBApplicationProcessState: 0xb72c6d2a0; pid: 541; taskState: Running; visibility: Foreground>\
padr\'e3o	14:07:35.028208-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] scene content state changed: ready\
padr\'e3o	14:07:35.028532-0300	SpringBoard	set focusRequestedHandle:<com.tecbiz.tecbizassociadospush focus:<contextID:2780862824 sceneID:com.tecbiz.tecbizassociadospush-default> run:Y hosting:() level:0 active:N wantedState:Disabled #suppr:0 iavHeight:0 onScreen:N>\
padr\'e3o	14:07:35.029220-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Setting parent scene: (FBSceneManager):com.apple.springboard\
padr\'e3o	14:07:35.029267-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] propagating 4 settings from (FBSceneManager):com.apple.springboard\
padr\'e3o	14:07:35.029800-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.031001-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Removing parent scene.\
padr\'e3o	14:07:35.031287-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Setting parent scene: (FBSceneManager):com.apple.springboard\
padr\'e3o	14:07:35.031312-0300	SpringBoard	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] propagating 4 settings from (FBSceneManager):com.apple.springboard\
padr\'e3o	14:07:35.032428-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBSceneReadySwitcherModifierEvent: 0xb759f7020; type: SceneReady; appLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main> \{\
    configuration = full;\
    itemsToLayoutAttributes = \{\
        sceneID:com.tecbiz.tecbizassociadospush-default = <SBDisplayItemLayoutAttributes: 0xb77524000; contentOrientation: "portrait (1)"; lastInteractionTime: 147462; sizingPolicy: maximized; size: unspecified; center: unspecified; occlusionState: unknown; userConfiguredSizeBeforeOverlapping: unspecified; unoccludedPeekingCenter: unspecified>;\
    \}\
    environment = main;\
    centerConfiguration = undefined;\
    preferredDisplayOrdinal = 0;\
    continuousExposeIdentifier = com.tecbiz.tecbizassociadospush;\
    layoutItems = \{\
        primary = <SBDisplayItem: 0xb73a1dfe0; type: App; bundleIdentifier: com.tecbiz.tecbizassociadospush; uniqueIdentifier: sceneID:com.tecbiz.tecbizassociadospush-default>;\
    \}\
\}>\
padr\'e3o	14:07:35.040035-0300	SpringBoard	[coordinator] handling new keyboard arbiter request pid: 541 sceneIdentity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.040266-0300	SpringBoard	arbiter: arbiter requested pid 541 / com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.040291-0300	SpringBoard	[coordinator] using arbiter suggested pid 541 + scene: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.040370-0300	SpringBoard	[coordinator] informing scene controller 'com.apple.springboard' of focusTarget: <com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid:541>\
padr\'e3o	14:07:35.040519-0300	SpringBoard	[com.apple.springboard] coalition says I have focus; enforcing policy: \{\
    keyboardFocusTarget: <com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid:541>;\
    selectionPolicy: KeyboardArbiter;\
    shouldSuppressRemoteDeferring: 0;\
\}\
padr\'e3o	14:07:35.040545-0300	SpringBoard	rules: (keyboardFocus) outbound target changed from:(null) to <com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid:541>\
padr\'e3o	14:07:35.040569-0300	SpringBoard	rules: (keyboardFocus) defer (<com.apple.frontboard.systemappservices/FBSceneManager:com.apple.springboard pid:219>) -> <com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid:541>\
padr\'e3o	14:07:35.040743-0300	SpringBoard	[coordinator] new enforced policy: \{\
    keyboardFocusTarget: <com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid:541>;\
    selectionPolicy: KeyboardArbiter;\
    shouldSuppressRemoteDeferring: 0;\
\}\
padr\'e3o	14:07:35.040821-0300	SpringBoard	[coordinator] keyboard arbiter suggested <pid: 541; sceneIdentity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default> and we replied <com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default pid:541>\
padr\'e3o	14:07:35.041269-0300	SpringBoard	set currentFocus PID:541 sceneIdentity:com.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.042663-0300	SpringBoard	rules: (scene setting) ADDED keyboardFocus environment to scene: sceneID:com.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.088973-0300	SpringBoard	MRNowPlayingAudioFormatController foreground bundle id changed: com.tecbiz.tecbizassociadospush\
padr\'e3o	14:07:35.092803-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] Foreground app will not request ephemeral notifications isAppClip: NO wantsEphemeral notifications: NO\
padr\'e3o	14:07:35.093724-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] com.tecbiz.tecbizassociadospush application state changed to <RBSProcessState| task:running-active debug:none endowmentNamespace:[\
	com.apple.frontboard.visibility\
	]>\
padr\'e3o	14:07:35.093831-0300	SpringBoard	Move topics from Opportunistic to Enabled: (\
    "com.tecbiz.tecbizassociadospush"\
)\
padr\'e3o	14:07:35.103578-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.232440-0300	SpringBoard	RX com.tecbiz.tecbizassociadospush(541) setClientFocusContext\
    focusContext:<contextID:3298745841 sceneID:com.tecbiz.tecbizassociadospush-default>\
padr\'e3o	14:07:35.423250-0300	SpringBoard	[sceneID:com.tecbiz.tecbizassociadospush-default] Setting deactivation reasons to: '(none)' for reason: updateAllScenesForBand - Assertion removed.\
padr\'e3o	14:07:35.429853-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] Getting delivered notifications\
padr\'e3o	14:07:35.432384-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] Removing all delivered notifications\
padr\'e3o	14:07:35.432429-0300	SpringBoard	[com.tecbiz.tecbizassociadospush] Remove all delivered notifications\
padr\'e3o	14:07:35.742069-0300	SpringBoard	[Main (EmbeddedDisplay)] dispatch event:\
<SBTransitionSwitcherModifierEvent: 0xb74572b80; type: MainTransition; transitionID: A9F3CE31-5E5E-4C1C-9102-44360C4EC97C; phase: Complete; animated: YES; fromAppLayout: <SBAppLayout: 0xb72ce3a00; primary: com.apple.mobilesafari:17E04A246B3A; environment: main>; toAppLayout: <SBAppLayout: 0xb72ce3b00; primary: com.tecbiz.tecbizassociadospush:default; environment: main>; pendingTermination: \{(\
)\}; morphingPIPLayoutRole: undefined>\
padr\'e3o	14:07:35.780992-0300	SpringBoard	Front display did change: <SBApplication: 0xb732ff600; com.tecbiz.tecbizassociadospush>\
padr\'e3o	14:07:35.796540-0300	SpringBoard	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.449136-0300	symptomsd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.656759-0300	symptomsd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.744497-0300	symptomsd	Data Usage for com.tecbiz.tecbizassociadospush on flow 3304 - WiFi in/out: 786961/241044, WiFi delta_in/delta_out: 7100/4912, Cell in/out: 390319/132162, Cell delta_in/delta_out: 0/0, RNF: 0, subscriber tag: 1, total duration: 17.165\
padr\'e3o	14:07:22.748409-0300	symptomsd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:22.756608-0300	symptomsd	com.tecbiz.tecbizassociadospush: Foreground: false\
padr\'e3o	14:07:22.756683-0300	symptomsd	call _saveAndUnloadSelectState on com.tecbiz.tecbizassociadospush exiting foreground state\
padr\'e3o	14:07:25.563308-0300	symptomsd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.071666-0300	symptomsd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.095928-0300	symptomsd	com.tecbiz.tecbizassociadospush: Foreground: true\
padr\'e3o	14:07:35.108094-0300	symptomsd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.124408-0300	symptomsd	Data Usage for com.tecbiz.tecbizassociadospush on flow 3636 - WiFi in/out: 787061/241807, WiFi delta_in/delta_out: 100/763, Cell in/out: 390319/132162, Cell delta_in/delta_out: 0/0, RNF: 0, subscriber tag: 1, total duration: 0.034\
padr\'e3o	14:07:40.299731-0300	symptomsd	Data Usage for com.tecbiz.tecbizassociadospush on flow 3636 - WiFi in/out: 795340/243104, WiFi delta_in/delta_out: 8279/1297, Cell in/out: 390319/132162, Cell delta_in/delta_out: 0/0, RNF: 0, subscriber tag: 1, total duration: 5.209\
padr\'e3o	14:07:15.581780-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.118s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:15.581876-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.118s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.581907-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.118s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.589982-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.121s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.598104-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.135s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.598134-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.135s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.598334-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.020s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:15.605532-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:receive_nexus @10.023s\
padr\'e3o	14:07:15.608772-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.146s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:15.608928-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.146s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:15.609013-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.146s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.609040-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.146s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.618371-0300	TecBizAssociado	nw_endpoint_resolver_update [C3.1 dry-run Hostname#ed926f08:443 in_progress resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Adding endpoint handler for IPv4#011bf45f:443\
padr\'e3o	14:07:15.807533-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.345s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:15.807682-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.345s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.807754-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.345s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.808511-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.345s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.808825-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.346s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.808882-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.346s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.809885-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.231s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:15.810484-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:receive_nexus @10.232s\
padr\'e3o	14:07:15.811598-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.348s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:15.811824-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.348s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:15.811927-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.349s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:15.812000-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @10.349s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:21.492530-0300	TecBizAssociado	TX focusApplication (peekAppEvent) stealKB:Y scene:com.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:21.493096-0300	TecBizAssociado	Evaluating dispatch of UIEvent: 0x102d85b20; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0\
padr\'e3o	14:07:21.493626-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to windows: 1\
padr\'e3o	14:07:21.493659-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102056c60>; contextId: 0x3B2DD7D9\
padr\'e3o	14:07:21.503118-0300	TecBizAssociado	Evaluating dispatch of UIEvent: 0x102d85b20; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0\
padr\'e3o	14:07:21.503151-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to windows: 1\
padr\'e3o	14:07:21.503195-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102056c60>; contextId: 0x3B2DD7D9\
padr\'e3o	14:07:21.520276-0300	TecBizAssociado	Evaluating dispatch of UIEvent: 0x102d85b20; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0\
padr\'e3o	14:07:21.520320-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to windows: 1\
padr\'e3o	14:07:21.520369-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102056c60>; contextId: 0x3B2DD7D9\
padr\'e3o	14:07:21.535269-0300	TecBizAssociado	Evaluating dispatch of UIEvent: 0x102d85b20; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0\
padr\'e3o	14:07:21.535569-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to windows: 1\
padr\'e3o	14:07:21.535650-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102056c60>; contextId: 0x3B2DD7D9\
padr\'e3o	14:07:21.551222-0300	TecBizAssociado	Not push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:21.551270-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:21.551537-0300	TecBizAssociado	Deactivation reason added: 0; deactivation reasons: 0 -> 1; animating application lifecycle event: 1\
padr\'e3o	14:07:21.551740-0300	TecBizAssociado	Deactivation reason added: 12; deactivation reasons: 1 -> 4097; animating application lifecycle event: 1\
padr\'e3o	14:07:21.553504-0300	TecBizAssociado	Evaluating dispatch of UIEvent: 0x102d85b20; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0\
padr\'e3o	14:07:21.553586-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to windows: 1\
padr\'e3o	14:07:21.553674-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102056c60>; contextId: 0x3B2DD7D9\
padr\'e3o	14:07:21.554332-0300	TecBizAssociado	Evaluating dispatch of UIEvent: 0x102d85b20; type: 0; subtype: 0; backing type: 11; shouldSend: 1; ignoreInteractionEvents: 0, systemGestureStateChange: 0\
padr\'e3o	14:07:21.554368-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to windows: 1\
padr\'e3o	14:07:21.554397-0300	TecBizAssociado	Sending UIEvent type: 0; subtype: 0; to window: <UIWindow: 0x102056c60>; contextId: 0x3B2DD7D9\
padr\'e3o	14:07:21.562505-0300	TecBizAssociado	Received configuration update from daemon (initial)\
padr\'e3o	14:07:21.598289-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:21.598318-0300	TecBizAssociado	Deactivation reason added: 5; deactivation reasons: 4097 -> 4129; animating application lifecycle event: 1\
padr\'e3o	14:07:21.598483-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:21.598556-0300	TecBizAssociado	Deactivation reason removed: 0; deactivation reasons: 4129 -> 4128; animating application lifecycle event: 1\
padr\'e3o	14:07:21.601912-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:21.601938-0300	TecBizAssociado	Deactivation reason added: 0; deactivation reasons: 4128 -> 4129; animating application lifecycle event: 1\
padr\'e3o	14:07:21.611268-0300	TecBizAssociado	policyStatus:<BKSHIDEventDeliveryPolicyObserver: 0x102ddc190; token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; status: none> was:ancestor\
padr\'e3o	14:07:21.611299-0300	TecBizAssociado	observerPolicyDidChange: 0x102ddc190 -> <_UIKeyWindowSceneObserver: 0x102e3c960>\
padr\'e3o	14:07:21.611326-0300	TecBizAssociado	Scene target of keyboard event deferring environment did change: 0; scene: UIWindowScene: 0x1020545e0; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:21.618918-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:22.422648-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:22.422996-0300	TecBizAssociado	Deactivation reason removed: 5; deactivation reasons: 4129 -> 4097; animating application lifecycle event: 1\
padr\'e3o	14:07:22.456573-0300	TecBizAssociado	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Received action(s) in scene-update: <FBSceneSnapshotAction: 0x00db0012>\
padr\'e3o	14:07:22.458338-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:22.458368-0300	TecBizAssociado	[0x102cc0300] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()\
padr\'e3o	14:07:22.458393-0300	TecBizAssociado	[0x102ddd220] Session canceled.\
padr\'e3o	14:07:22.461188-0300	TecBizAssociado	agent connection cancelled (details: Session manually canceled)\
padr\'e3o	14:07:22.461219-0300	TecBizAssociado	[0x102ddd220] Disposing of session\
padr\'e3o	14:07:22.476737-0300	TecBizAssociado	Deactivation reason added: 11; deactivation reasons: 4097 -> 6145; animating application lifecycle event: 0\
padr\'e3o	14:07:22.477761-0300	TecBizAssociado	Will add backgroundTask with taskName: <private>, expirationHandler: (null)\
padr\'e3o	14:07:22.478307-0300	TecBizAssociado	Creating new assertion because there is no existing background assertion.\
padr\'e3o	14:07:22.478332-0300	TecBizAssociado	Creating new background assertion\
padr\'e3o	14:07:22.478358-0300	TecBizAssociado	Created new background assertion <BKSProcessAssertion: 0x10675f2a0>\
padr\'e3o	14:07:22.479207-0300	TecBizAssociado	Incrementing reference count for background assertion <private>\
padr\'e3o	14:07:22.479235-0300	TecBizAssociado	Created background task <private>.\
padr\'e3o	14:07:22.479260-0300	TecBizAssociado	com.tecbiz.tecbizassociadospush(541) invalidateConnection (appDidSuspend)\
padr\'e3o	14:07:22.479288-0300	TecBizAssociado	[0x102cfd900] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()\
padr\'e3o	14:07:22.479314-0300	TecBizAssociado	Will add backgroundTask with taskName: <private>, expirationHandler: <__NSMallocBlock__: 0x106779590>\
padr\'e3o	14:07:22.479338-0300	TecBizAssociado	Reusing background assertion <BKSProcessAssertion: 0x10675f2a0>\
padr\'e3o	14:07:22.479363-0300	TecBizAssociado	Incrementing reference count for background assertion <private>\
padr\'e3o	14:07:22.482281-0300	TecBizAssociado	Created background task <private>.\
padr\'e3o	14:07:22.482413-0300	TecBizAssociado	Ending background task with UIBackgroundTaskIdentifier: 3\
padr\'e3o	14:07:22.482630-0300	TecBizAssociado	Ending task with identifier 3 and description: <private>, _expireHandler: <__NSMallocBlock__: 0x106779590>\
padr\'e3o	14:07:22.482656-0300	TecBizAssociado	Decrementing reference count for assertion <private> (used by background task with identifier 3: <private>)\
padr\'e3o	14:07:22.492636-0300	TecBizAssociado	Deactivation reason removed: 0; deactivation reasons: 6145 -> 6144; animating application lifecycle event: 0\
padr\'e3o	14:07:22.493299-0300	TecBizAssociado	Will add backgroundTask with taskName: <private>, expirationHandler: <__NSMallocBlock__: 0x106779590>\
padr\'e3o	14:07:22.493657-0300	TecBizAssociado	Reusing background assertion <BKSProcessAssertion: 0x10675f2a0>\
padr\'e3o	14:07:22.493782-0300	TecBizAssociado	Incrementing reference count for background assertion <private>\
padr\'e3o	14:07:22.494434-0300	TecBizAssociado	Created background task <private>.\
padr\'e3o	14:07:22.494486-0300	TecBizAssociado	Push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:22.494791-0300	TecBizAssociado	Should not send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:22.494817-0300	TecBizAssociado	Performing snapshot request 0x1067dce70 (type 1)\
padr\'e3o	14:07:22.494884-0300	TecBizAssociado	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Sending action(s): <FBSSceneSnapshotRequestAction: 0x021d0000>\
padr\'e3o	14:07:22.521534-0300	TecBizAssociado	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, unknown-NotVisible\
padr\'e3o	14:07:22.633841-0300	TecBizAssociado	Snapshot request 0x1067dce70 complete\
padr\'e3o	14:07:22.633997-0300	TecBizAssociado	Push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:22.638684-0300	TecBizAssociado	Should not send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:22.638756-0300	TecBizAssociado	Performing snapshot request 0x1067dcfc0 (type 1)\
padr\'e3o	14:07:22.638947-0300	TecBizAssociado	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Sending action(s): <FBSSceneSnapshotRequestAction: 0x021d0001>\
padr\'e3o	14:07:22.656435-0300	TecBizAssociado	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, unknown-NotVisible\
padr\'e3o	14:07:22.666390-0300	TecBizAssociado	Snapshot request 0x1067dcfc0 complete\
padr\'e3o	14:07:22.666415-0300	TecBizAssociado	Ending background task with UIBackgroundTaskIdentifier: 4\
padr\'e3o	14:07:22.666444-0300	TecBizAssociado	Ending task with identifier 4 and description: <private>, _expireHandler: <__NSMallocBlock__: 0x106779590>\
padr\'e3o	14:07:22.666471-0300	TecBizAssociado	Decrementing reference count for assertion <private> (used by background task with identifier 4: <private>)\
padr\'e3o	14:07:22.668016-0300	TecBizAssociado	Push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:22.668128-0300	TecBizAssociado	Should not send trait collection or coordinate space update, interface style 1 -> 1, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:22.669094-0300	TecBizAssociado	[0x102df84d0] [keyboardFocus] Disabling event deferring records requested: adding recreation reason: detachedContext; for reason: _UIEventDeferringManager: 0x102df84d0: disabling keyboardFocus: context detached for window: 0x102056c60; contextID: 0x3B2DD7D9\
padr\'e3o	14:07:22.669661-0300	TecBizAssociado	Will add backgroundTask with taskName: <private>, expirationHandler: (null)\
padr\'e3o	14:07:22.669686-0300	TecBizAssociado	Reusing background assertion <BKSProcessAssertion: 0x10675f2a0>\
padr\'e3o	14:07:22.669732-0300	TecBizAssociado	Incrementing reference count for background assertion <private>\
padr\'e3o	14:07:22.669756-0300	TecBizAssociado	Created background task <private>.\
padr\'e3o	14:07:22.669900-0300	TecBizAssociado	Target list changed:\
padr\'e3o	14:07:22.670500-0300	TecBizAssociado	Ending background task with UIBackgroundTaskIdentifier: 2\
padr\'e3o	14:07:22.670527-0300	TecBizAssociado	Ending task with identifier 2 and description: <private>, _expireHandler: (null)\
padr\'e3o	14:07:22.670552-0300	TecBizAssociado	Decrementing reference count for assertion <private> (used by background task with identifier 2: <private>)\
padr\'e3o	14:07:22.677947-0300	TecBizAssociado	Ending background task with UIBackgroundTaskIdentifier: 5\
padr\'e3o	14:07:22.678004-0300	TecBizAssociado	Ending task with identifier 5 and description: <private>, _expireHandler: (null)\
padr\'e3o	14:07:22.678034-0300	TecBizAssociado	Decrementing reference count for assertion <private> (used by background task with identifier 5: <private>)\
padr\'e3o	14:07:22.678067-0300	TecBizAssociado	Will invalidate assertion: <BKSProcessAssertion: 0x10675f2a0> for task identifier: 5\
padr\'e3o	14:07:35.012037-0300	TecBizAssociado	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, unknown-NotVisible\
padr\'e3o	14:07:35.012803-0300	TecBizAssociado	[(FBSceneManager):sceneID:com.tecbiz.tecbizassociadospush-default] Received action(s) in scene-update: UIOpenURLAction\
padr\'e3o	14:07:35.014242-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:35.014286-0300	TecBizAssociado	Deactivation reason added: 5; deactivation reasons: 6144 -> 6176; animating application lifecycle event: 1\
padr\'e3o	14:07:35.014313-0300	TecBizAssociado	Ignoring already applied deactivation reason: 12; deactivation reasons: 6176\
padr\'e3o	14:07:35.014363-0300	TecBizAssociado	Deactivation reason removed: 11; deactivation reasons: 6176 -> 4128; animating application lifecycle event: 1\
padr\'e3o	14:07:35.014386-0300	TecBizAssociado	Scene did update interface style to 1, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:35.014410-0300	TecBizAssociado	establishing connection to agent\
padr\'e3o	14:07:35.014525-0300	TecBizAssociado	[0x10675f070] Session created.\
padr\'e3o	14:07:35.014549-0300	TecBizAssociado	[0x10675f070] Session created from connection [0x1065c4800]\
padr\'e3o	14:07:35.014578-0300	TecBizAssociado	Push traits update to screen for new style 1, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:35.014621-0300	TecBizAssociado	[0x1065c4800] activating connection: mach=true listener=false peer=false name=com.apple.uiintelligencesupport.agent\
padr\'e3o	14:07:35.014965-0300	TecBizAssociado	[0x10675f070] Session activated\
padr\'e3o	14:07:35.016769-0300	TecBizAssociado	[0x102df84d0] [keyboardFocus] Recreation of event deferring records requested: removing recreation reason: detachedContext; for reason: _UIEventDeferringManager: 0x102df84d0: recreating keyboardFocus: context attached for window: 0x102056c60; contextID: 0xA5C09968\
padr\'e3o	14:07:35.025279-0300	TecBizAssociado	Target list changed: <CADisplay:LCD primary>\
padr\'e3o	14:07:35.025418-0300	TecBizAssociado	startConnection\
padr\'e3o	14:07:35.026995-0300	TecBizAssociado	[0x106775040] activating connection: mach=true listener=false peer=false name=com.apple.UIKit.KeyboardManagement.hosted\
padr\'e3o	14:07:35.027338-0300	TecBizAssociado	SecSecurityClientGet new thread!\
padr\'e3o	14:07:35.027415-0300	TecBizAssociado	handleKeyboardChange: set currentKeyboard:N (wasKeyboard:N)\
padr\'e3o	14:07:35.027492-0300	TecBizAssociado	forceReloadInputViews\
padr\'e3o	14:07:35.027595-0300	TecBizAssociado	Reloading input views for: <(null): 0x0; > force: 1\
padr\'e3o	14:07:35.033672-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:35.036302-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> resuming, timeouts(0.0, 604800.0) qos(0x19) voucher((null)) activity(00000000-0000-0000-0000-000000000000)\
padr\'e3o	14:07:35.037455-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.541s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.037508-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.541s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.037535-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.541s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.037955-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.541s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.038402-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.541s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.038428-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.541s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.039474-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.427s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.039940-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:receive_nexus @29.427s\
padr\'e3o	14:07:35.040397-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.543s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.040976-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.543s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.041110-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.543s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.041163-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied_change @29.543s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.041760-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:35.041785-0300	TecBizAssociado	Scene did update interface style to 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:35.041835-0300	TecBizAssociado	Push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:35.043340-0300	TecBizAssociado	quic_conn_handle_error_inner [C1.1.1.1:2] [-efba6f02abb8648c] received error: Socket is not connected (state quic_conn_state_connected, nw path 9a25bb8db01b23b9)\
padr\'e3o	14:07:35.043365-0300	TecBizAssociado	quic_migration_handle_error [C1.1.1.1:2] [-efba6f02abb8648c] path 0x102f496c0 on <private> received an error, closing it\
padr\'e3o	14:07:35.043414-0300	TecBizAssociado	quic_migration_evaluate [C1.1.1.1:2] [-efba6f02abb8648c] evaluating path migration\
padr\'e3o	14:07:35.043534-0300	TecBizAssociado	quic_migration_evaluate_block_invoke [C1.1.1.1:2] [-efba6f02abb8648c] path 9a25bb8db01b23b9 state unavailable (0), ifname en0, primary? 1, initial? 1, fallback? 0, preferred? 0 lossy? 0\
padr\'e3o	14:07:35.043611-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:35.043751-0300	TecBizAssociado	quic_migration_evaluate [C1.1.1.1:2] [-efba6f02abb8648c] lost primary path or better path available\
padr\'e3o	14:07:35.043857-0300	TecBizAssociado	quic_migration_evaluate [C1.1.1.1:2] [-efba6f02abb8648c] no better path available\
padr\'e3o	14:07:35.044228-0300	TecBizAssociado	nw_protocol_instance_set_current_path [C1.1.1.1:2] Calling notify nw_protocol_notification_type_migration (null)\
padr\'e3o	14:07:35.044450-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.545s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.044532-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.545s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.044581-0300	TecBizAssociado	nw_connection_add_timestamp_locked_on_nw_queue [C1] Hit maximum timestamp count, will start dropping events\
padr\'e3o	14:07:35.044606-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.044656-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.044861-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.044960-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.045012-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.045038-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.045198-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.045279-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.045335-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.045382-0300	TecBizAssociado	policyStatus:<BKSHIDEventDeliveryPolicyObserver: 0x102ddc190; token: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default; status: ancestor> was:none\
padr\'e3o	14:07:35.045408-0300	TecBizAssociado	observerPolicyDidChange: 0x102ddc190 -> <_UIKeyWindowSceneObserver: 0x102e3c960>\
padr\'e3o	14:07:35.045434-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.045459-0300	TecBizAssociado	Scene target of keyboard event deferring environment did change: 1; scene: UIWindowScene: 0x1020545e0; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.045569-0300	TecBizAssociado	[0x102df84d0] Scene target of event deferring environments did update: scene: 0x1020545e0; current systemShellManagesKeyboardFocus: 1; systemShellManagesKeyboardFocusForScene: 1; eligibleForRecordRemoval: 1;\
padr\'e3o	14:07:35.045622-0300	TecBizAssociado	Scene became target of keyboard event deferring environment: UIWindowScene: 0x1020545e0; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.045831-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.045936-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.045991-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.046019-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.046288-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.046530-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.046713-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.046766-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.546s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.047020-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.047123-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.047183-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.047209-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.047313-0300	TecBizAssociado	[C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.431s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.047665-0300	TecBizAssociado	[C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: C249B8A6-2FEF-4C3F-A382-54517566170D\
padr\'e3o	14:07:35.047743-0300	TecBizAssociado	[C1.1.1 Hostname#ed926f08:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: *************-4120-A076-BED5106552C4\
padr\'e3o	14:07:35.047796-0300	TecBizAssociado	[C1.1 Hostname#ed926f08:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.047868-0300	TecBizAssociado	[C1 IPv4#011bf45f:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:migrated @29.547s, uuid: 47A49545-BACB-446B-BBFC-E8D8F56B1D78\
padr\'e3o	14:07:35.047991-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.048075-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
erro	14:07:35.048099-0300	TecBizAssociado	nw_read_request_report [C1] Receive failed with error "Socket is not connected"\
padr\'e3o	14:07:35.048175-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
erro	14:07:35.048223-0300	TecBizAssociado	nw_read_request_report [C1] Receive failed with error "Socket is not connected"\
padr\'e3o	14:07:35.048633-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.048789-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.048964-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.049015-0300	TecBizAssociado	nw_protocol_boringssl_error(2008) [C2:1][0x102eed600] Lower protocol stack error post TLS handshake. [57: <private>]\
padr\'e3o	14:07:35.049113-0300	TecBizAssociado	nw_flow_disconnected [C2 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.049190-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.049246-0300	TecBizAssociado	Connection 1: final read 1:57, complete[Y], final[Y]\
padr\'e3o	14:07:35.049516-0300	TecBizAssociado	quic_frame_write_CONNECTION_CLOSE [C1.1.1.1:2] [-efba6f02abb8648c] sending APPLICATION_CLOSE, code 0x100, reason <null>\
padr\'e3o	14:07:35.049546-0300	TecBizAssociado	[C2 B7A9A329-2BFC-45DD-A815-2BD161BFC3C9 IPv4#011bf45f:443 quic-connection, url hash: e2daeaf6, tls, definite, attribution: developer] cancel\
padr\'e3o	14:07:35.050004-0300	TecBizAssociado	[C2 B7A9A329-2BFC-45DD-A815-2BD161BFC3C9 IPv4#011bf45f:443 quic-connection, url hash: e2daeaf6, tls, definite, attribution: developer] cancelled\
	[C2 C249B8A6-2FEF-4C3F-A382-54517566170D *************:56862<->IPv4#011bf45f:443]\
	Connected Path: satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi\
	Duration: 29.433s, QUIC @0.000s took 0.000s, TLS 1.3 took 0.063s\
	bytes in/out: 6148/5314, packets in/out: 17/20, rtt: 0.026s, retransmitted bytes: 0, out-of-order bytes: 1098\
	ecn packets sent/acked/marked/lost: 13/13/0/0\
padr\'e3o	14:07:35.050484-0300	TecBizAssociado	nw_flow_disconnected [C2 IPv4#011bf45f:443 cancelled channel-flow ((null))] Output protocol disconnected\
padr\'e3o	14:07:35.050642-0300	TecBizAssociado	nw_connection_report_state_with_handler_on_nw_queue [C2] reporting state cancelled\
padr\'e3o	14:07:35.050697-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.050785-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.050858-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.050920-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.050977-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051076-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051129-0300	TecBizAssociado	nw_protocol_boringssl_error(2008) [C2:1][0x102eed600] Lower protocol stack error post TLS handshake. [57: <private>]\
padr\'e3o	14:07:35.051223-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051584-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051635-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051711-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051791-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051871-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.051994-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.052021-0300	TecBizAssociado	nw_protocol_boringssl_error(2008) [C2:1][0x102eed600] Lower protocol stack error post TLS handshake. [57: <private>]\
padr\'e3o	14:07:35.052129-0300	TecBizAssociado	nw_flow_disconnected [C1.1.1.1 IPv4#011bf45f:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol disconnected\
padr\'e3o	14:07:35.052159-0300	TecBizAssociado	quic_conn_log_summary [C1.1.1.1:2] [-efba6f02abb8648c] \
	Connection attempts: 1, RETRY received: no, PTOs: 0\
	Early data: no, Keep-alives sent/acknowledged: 0/0\
	Migration events: 0, paths validated: 0\
	Inbound unidirectional/bidirectional streams: 3/0\
	Outbound unidirectional/bidirectional streams: 3/3\
	DATA_BLOCKED frames sent/received: 0/0\
	STREAM_DATA_BLOCKED frames sent/received: 0/0\
padr\'e3o	14:07:35.052189-0300	TecBizAssociado	Connection 1: cleaning up\
padr\'e3o	14:07:35.052242-0300	TecBizAssociado	[C1 AC8FCAE9-B7D6-4148-A0DA-4D4A85E8C6E6 Hostname#ed926f08:443 quic-connection, url hash: e2daeaf6, definite, attribution: developer] cancel\
padr\'e3o	14:07:35.052279-0300	TecBizAssociado	[C1 AC8FCAE9-B7D6-4148-A0DA-4D4A85E8C6E6 Hostname#ed926f08:443 quic-connection, url hash: e2daeaf6, definite, attribution: developer] cancelled\
	[C1.1.1.1 C249B8A6-2FEF-4C3F-A382-54517566170D *************:56862<->IPv4#011bf45f:443]\
	Connected Path: satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi\
	Privacy Stance: Not Eligible\
	Duration: 29.549s, DNS @0.000s took 0.113s, QUIC @0.115s took 0.063s\
	bytes in/out: 6148/5314, packets in/out: 17/20, rtt: 0.026s, retransmitted bytes: 0, out-of-order bytes: 1098\
	ecn packets sent/acked/marked/lost: 13/13/0/0\
erro	14:07:35.052949-0300	TecBizAssociado	quic_conn_send_frames_for_key_state_block_invoke [C1.1.1.1:2] [-efba6f02abb8648c] unable to request outbound data\
padr\'e3o	14:07:35.053831-0300	TecBizAssociado	nw_connection_report_state_with_handler_on_nw_queue [C1] reporting state cancelled\
padr\'e3o	14:07:35.054323-0300	TecBizAssociado	Connection 0: creating secure tcp or quic connection\
padr\'e3o	14:07:35.054395-0300	TecBizAssociado	Connection 5: enabling TLS\
padr\'e3o	14:07:35.054419-0300	TecBizAssociado	Connection 5: starting, TC(0x0)\
padr\'e3o	14:07:35.054444-0300	TecBizAssociado	[C5 E97D5100-FD0F-412A-84CC-668484828466 Hostname#4d459f4b:443 quic-connection, url hash: 824ba16e, definite, attribution: developer, context: com.apple.CFNetwork.NSURLSession.\{F9FC2C98-F6FC-4F1F-8AAA-4EF32337FB77\}\{(null)\}\{Y\}\{2\}\{0x0\} (private), proc: 6C96D682-F9DC-3BCF-83A8-BED73102C291] start\
padr\'e3o	14:07:35.054502-0300	TecBizAssociado	[C5 Hostname#4d459f4b:443 initial parent-flow ((null))] event: path:start @0.000s\
padr\'e3o	14:07:35.055013-0300	TecBizAssociado	[C5 Hostname#4d459f4b:443 waiting parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied @0.000s, uuid: 0C6F7DA2-6E5D-4B51-8CBA-1B07F4E2A767\
padr\'e3o	14:07:35.055227-0300	TecBizAssociado	[C5 Hostname#4d459f4b:443 in_progress parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:start_connect @0.000s\
padr\'e3o	14:07:35.055252-0300	TecBizAssociado	nw_connection_report_state_with_handler_on_nw_queue [C5] reporting state preparing\
padr\'e3o	14:07:35.055399-0300	TecBizAssociado	[C5 Hostname#4d459f4b:443 in_progress parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:start_child @0.000s\
padr\'e3o	14:07:35.055509-0300	TecBizAssociado	[C5.1 Hostname#4d459f4b:443 initial path ((null))] event: path:start @0.000s\
padr\'e3o	14:07:35.055783-0300	TecBizAssociado	[C5.1 Hostname#4d459f4b:443 waiting path (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied @0.000s, uuid: 0C6F7DA2-6E5D-4B51-8CBA-1B07F4E2A767\
padr\'e3o	14:07:35.055905-0300	TecBizAssociado	[C5.1 Hostname#4d459f4b:443 in_progress transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: transform:start @0.000s\
padr\'e3o	14:07:35.056040-0300	TecBizAssociado	[C5.1.1 Hostname#4d459f4b:443 initial path ((null))] event: path:start @0.000s\
padr\'e3o	14:07:35.056562-0300	TecBizAssociado	[C5.1.1 Hostname#4d459f4b:443 waiting path (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied @0.000s, uuid: E99AC9B5-ACC7-4745-BAFA-D4538B5DBAD2\
padr\'e3o	14:07:35.056684-0300	TecBizAssociado	[C5.1.1 Hostname#4d459f4b:443 in_progress resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: resolver:start_dns @0.000s\
padr\'e3o	14:07:35.056776-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> setting up Connection 5\
padr\'e3o	14:07:35.074399-0300	TecBizAssociado	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, unknown-NotVisible\
padr\'e3o	14:07:35.105259-0300	TecBizAssociado	nw_endpoint_resolver_update [C5.1.1 Hostname#4d459f4b:443 in_progress resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Adding endpoint handler for IPv4#181a64d9:443\
padr\'e3o	14:07:35.105420-0300	TecBizAssociado	[C5.1.1 Hostname#4d459f4b:443 in_progress resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: resolver:receive_dns @0.073s\
padr\'e3o	14:07:35.105911-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 initial path ((null))] event: path:start @0.074s\
padr\'e3o	14:07:35.106189-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 waiting path (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: path:satisfied @0.074s, uuid: 0BAA454F-519B-49AB-BD02-C927DF7E625B\
padr\'e3o	14:07:35.106304-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 in_progress channel-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:start_nexus @0.074s\
padr\'e3o	14:07:35.106633-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:receive_nexus @0.075s\
padr\'e3o	14:07:35.106795-0300	TecBizAssociado	user_tcp_init_all_block_invoke g_tcp_nw_assert_context is false value -1\
padr\'e3o	14:07:35.107290-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:start_connect @0.077s\
padr\'e3o	14:07:35.107490-0300	TecBizAssociado	tcp_output [C5.1.1.1:3] flags=[SEC] seq=3646540319, ack=0, win=65535 state=SYN_SENT rcv_nxt=0, snd_una=3646540319\
padr\'e3o	14:07:35.108232-0300	TecBizAssociado	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, unknown-NotVisible\
padr\'e3o	14:07:35.114744-0300	TecBizAssociado	tcp_input [C5.1.1.1:3] flags=[S.E] seq=2706372738, ack=3646540320, win=65160 state=SYN_SENT rcv_nxt=0, snd_una=3646540319\
padr\'e3o	14:07:35.114775-0300	TecBizAssociado	nw_flow_connected [C5.1.1.1 IPv4#181a64d9:443 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Transport protocol connected (tcp)\
padr\'e3o	14:07:35.114895-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:finish_transport @0.099s\
padr\'e3o	14:07:35.115142-0300	TecBizAssociado	boringssl_session_apply_protocol_options_for_transport_block_invoke(2182) [C5.1.1.1:2][0x106742800] TLS configured [min_version(0x0301) max_version(0x0304) name(redacted) tickets(false) false_start(false) enforce_ev(false) enforce_ats(false) ats_non_pfs_ciphersuite_allowed(false) ech(false) pqtls(false), pake(false)]\
padr\'e3o	14:07:35.115219-0300	TecBizAssociado	boringssl_context_info_handler(2374) [C5.1.1.1:2][0x106742800] Client handshake started\
padr\'e3o	14:07:35.115293-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS client enter_early_data\
padr\'e3o	14:07:35.115365-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS client read_server_hello\
padr\'e3o	14:07:35.136691-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_hello_retry_request\
padr\'e3o	14:07:35.136744-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_server_hello\
padr\'e3o	14:07:35.136815-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_encrypted_extensions\
padr\'e3o	14:07:35.137087-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_certificate_request\
padr\'e3o	14:07:35.137267-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_server_certificate\
padr\'e3o	14:07:35.137293-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_server_certificate_verify\
padr\'e3o	14:07:35.137705-0300	TecBizAssociado	boringssl_context_evaluate_trust_async(1819) [C5.1.1.1:2][0x106742800] Performing external trust evaluation\
padr\'e3o	14:07:35.137759-0300	TecBizAssociado	boringssl_context_evaluate_trust_async_external(1804) [C5.1.1.1:2][0x106742800] Asyncing for external verify block\
padr\'e3o	14:07:35.137883-0300	TecBizAssociado	Connection 5: asked to evaluate TLS Trust\
padr\'e3o	14:07:35.138154-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> auth completion disp=1 cred=0x0\
padr\'e3o	14:07:35.140846-0300	TecBizAssociado	(Trust 0x1065bfb40) No pending evals, starting\
padr\'e3o	14:07:35.141319-0300	TecBizAssociado	[0x1065c7000] activating connection: mach=true listener=false peer=false name=com.apple.trustd\
padr\'e3o	14:07:35.141629-0300	TecBizAssociado	(Trust 0x1065bfb40) Completed async eval kickoff\
padr\'e3o	14:07:35.145387-0300	TecBizAssociado	(Trust 0x1065bfb40) trustd returned 4\
padr\'e3o	14:07:35.145459-0300	TecBizAssociado	Connection 5: TLS Trust result 0\
padr\'e3o	14:07:35.145484-0300	TecBizAssociado	boringssl_context_evaluate_trust_async_external_block_invoke_3(1760) [C5.1.1.1:2][0x106742800] Returning from external verify block with result: true\
padr\'e3o	14:07:35.145536-0300	TecBizAssociado	boringssl_context_certificate_verify_callback(2000) [C5.1.1.1:2][0x106742800] Certificate verification result: OK\
padr\'e3o	14:07:35.145560-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client read_server_finished\
padr\'e3o	14:07:35.145782-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client send_end_of_early_data\
padr\'e3o	14:07:35.145807-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client send_client_encrypted_extensions\
padr\'e3o	14:07:35.145832-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client send_client_certificate\
padr\'e3o	14:07:35.145860-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client complete_second_flight\
padr\'e3o	14:07:35.145910-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS 1.3 client done\
padr\'e3o	14:07:35.145982-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS client finish_client_handshake\
padr\'e3o	14:07:35.146006-0300	TecBizAssociado	boringssl_context_info_handler(2391) [C5.1.1.1:2][0x106742800] Client handshake state: TLS client done\
padr\'e3o	14:07:35.146034-0300	TecBizAssociado	boringssl_context_info_handler(2380) [C5.1.1.1:2][0x106742800] Client handshake done\
padr\'e3o	14:07:35.146274-0300	TecBizAssociado	nw_protocol_boringssl_signal_connected(755) [C5.1.1.1:2][0x106742800] TLS connected [version(0x0304) ciphersuite(TLS_AES_256_GCM_SHA384) group(0x001d) signature_alg(0x0804) alpn(http/1.1) resumed(0) offered_ticket(0) false_started(0) ocsp_received(0) sct_received(0) connect_time(33ms) flight_time(23ms) rtt(23ms) write_stalls(0) read_stalls(6) pake(0x0000)]\
padr\'e3o	14:07:35.146361-0300	TecBizAssociado	nw_flow_connected [C5.1.1.1 IPv4#181a64d9:443 in_progress channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol connected (CFNetworkConnection-4287849336)\
padr\'e3o	14:07:35.146605-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:child_finish_connect @0.133s\
padr\'e3o	14:07:35.146883-0300	TecBizAssociado	[C5.1.1 Hostname#4d459f4b:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:child_finish_connect @0.133s\
padr\'e3o	14:07:35.147016-0300	TecBizAssociado	[C5.1 Hostname#4d459f4b:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:child_finish_connect @0.133s\
padr\'e3o	14:07:35.147125-0300	TecBizAssociado	[C5.1.1.1 IPv4#181a64d9:443 ready channel-flow (satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:changed_viability @0.133s\
padr\'e3o	14:07:35.147203-0300	TecBizAssociado	[C5.1.1 Hostname#4d459f4b:443 ready resolver (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:changed_viability @0.133s\
padr\'e3o	14:07:35.147255-0300	TecBizAssociado	[C5.1 Hostname#4d459f4b:443 ready transform (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:changed_viability @0.133s\
padr\'e3o	14:07:35.147306-0300	TecBizAssociado	nw_flow_connected [C5 IPv4#181a64d9:443 in_progress parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] Output protocol connected (endpoint_flow)\
padr\'e3o	14:07:35.147382-0300	TecBizAssociado	[C5 IPv4#181a64d9:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:finish_connect @0.134s\
padr\'e3o	14:07:35.147612-0300	TecBizAssociado	nw_connection_report_state_with_handler_on_nw_queue [C5] reporting state ready\
padr\'e3o	14:07:35.147639-0300	TecBizAssociado	[C5 IPv4#181a64d9:443 ready parent-flow (satisfied (Path is satisfied), interface: en0[802.11], ipv4, ipv6, dns, uses wifi)] event: flow:changed_viability @0.134s\
padr\'e3o	14:07:35.147679-0300	TecBizAssociado	nw_connection_send_viability_changed_on_nw_queue [C5] viability_changed_handler(true)\
padr\'e3o	14:07:35.147741-0300	TecBizAssociado	[0x1065c7000] invalidated because the current process cancelled the connection by calling xpc_connection_cancel()\
padr\'e3o	14:07:35.147825-0300	TecBizAssociado	Connection 5: connected successfully\
padr\'e3o	14:07:35.147850-0300	TecBizAssociado	Connection 5: TLS handshake complete\
padr\'e3o	14:07:35.147878-0300	TecBizAssociado	Connection 5: ready C(N) E(N)\
padr\'e3o	14:07:35.147952-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> now using Connection 5\
padr\'e3o	14:07:35.147999-0300	TecBizAssociado	Connection 5: received viability advisory(Y)\
padr\'e3o	14:07:35.148068-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> sent request, body N 0\
padr\'e3o	14:07:35.198796-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> received response, status 200 content K\
padr\'e3o	14:07:35.199374-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> response ended\
padr\'e3o	14:07:35.199404-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> done using Connection 5\
padr\'e3o	14:07:35.199815-0300	TecBizAssociado	[C5] event: client:connection_idle @0.186s\
padr\'e3o	14:07:35.200001-0300	TecBizAssociado	[C5] event: client:connection_idle @0.186s\
padr\'e3o	14:07:35.200076-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> summary for task success \{transaction_duration_ms=197, response_status=200, connection=5, protocol="http/1.1", domain_lookup_duration_ms=73, connect_duration_ms=57, secure_connection_duration_ms=33, private_relay=false, request_start_ms=145, request_duration_ms=0, response_start_ms=196, response_duration_ms=0, request_bytes=391, request_throughput_kbps=3934, response_bytes=2826, response_throughput_kbps=3636, cache_hit=false\}\
padr\'e3o	14:07:35.200141-0300	TecBizAssociado	Task <B5CCB0B4-14C9-4D23-9EA9-C0CC1A100744>.<4> finished successfully\
erro	14:07:35.218241-0300	TecBizAssociado	'\uc0\u10060  Erro ao confirmar email:', [TypeError: Cannot read property 'substring' of undefined]\
erro	14:07:35.226389-0300	TecBizAssociado	'\uc0\u10060  Falha na confirma\'e7\'e3o de email:', 'Erro de conex\'e3o. Verifique sua internet e tente novamente.'\
padr\'e3o	14:07:35.231046-0300	TecBizAssociado	UIWindowScene: 0x1020545e0: Window became key in scene: UIWindow: 0x10650fb80; contextId: 0xC49EDDF1: reason: UIWindowScene: 0x1020545e0: Window requested to become key in scene: 0x10650fb80\
padr\'e3o	14:07:35.231091-0300	TecBizAssociado	Key window needs update: 1; currentKeyWindowScene: 0x1020545e0; evaluatedKeyWindowScene: 0x1020545e0; currentApplicationKeyWindow: 0x102056c60; evaluatedApplicationKeyWindow: 0x10650fb80; reason: UIWindowScene: 0x1020545e0: Window requested to become key in scene: 0x10650fb80\
padr\'e3o	14:07:35.231122-0300	TecBizAssociado	Window did become application key: UIWindow: 0x10650fb80; contextId: 0xC49EDDF1; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.231153-0300	TecBizAssociado	[0x102df84d0] Beginning and ending local event deferring requested for token: 0x102c68fc0; environments: 1; reason: UIWindowScene: 0x1020545e0: Begin event deferring in keyboardFocus for window: 0x10650fb80\
padr\'e3o	14:07:35.231178-0300	TecBizAssociado	[0x102df84d0] Begin local event deferring requested for token: 0x1067d90e0; environments: 1; reason: UIWindowScene: 0x1020545e0: Begin event deferring in keyboardFocus for window: 0x10650fb80\
padr\'e3o	14:07:35.231547-0300	TecBizAssociado	Window resigning application key: UIWindow: 0x102056c60; contextId: 0xA5C09968; scene identity: com.apple.frontboard.systemappservices/FBSceneManager:sceneID%3Acom.tecbiz.tecbizassociadospush-default\
padr\'e3o	14:07:35.232497-0300	TecBizAssociado	Reloading input views for: <(null): 0x0; > force: 0\
padr\'e3o	14:07:35.234623-0300	TecBizAssociado	Change from input view set: (null)\
padr\'e3o	14:07:35.234648-0300	TecBizAssociado	Change to input view set: (null)\
padr\'e3o	14:07:35.253599-0300	TecBizAssociado	Override focusSystemState: (ENABLED) for reason(s): \{(\
    "<_UIAlertControllerPhoneTVMacView 0x102f2d900>"\
)\}\
padr\'e3o	14:07:35.253720-0300	TecBizAssociado	Not push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:35.274029-0300	TecBizAssociado	_willShowAlertController: <RCTAlertController: 0x102edd800>\
padr\'e3o	14:07:35.274220-0300	TecBizAssociado	_addAlertControllerToStack: Adding Alert to stack : <RCTAlertController: 0x102edd800>\
padr\'e3o	14:07:35.274358-0300	TecBizAssociado	Reloading input views for: <RCTAlertController: 0x102edd800; > force: 0\
padr\'e3o	14:07:35.274450-0300	TecBizAssociado	_reloadInputViewsForKeyWindowSceneResponder: 1 force: 0, fromBecomeFirstResponder: 1 (automaticKeyboard: 1, reloadIdentifier: 76E6754A-4C11-4B9C-9C51-E401A755F62C)\
padr\'e3o	14:07:35.274512-0300	TecBizAssociado	_inputViewsForResponder: <RCTAlertController: 0x102edd800; >, automaticKeyboard: 1, force: 0\
padr\'e3o	14:07:35.274596-0300	TecBizAssociado	_inputViewsForResponder, found custom inputView: <(null): 0x0>, customInputViewController: <(null): 0x0>\
padr\'e3o	14:07:35.274624-0300	TecBizAssociado	_inputViewsForResponder, found inputAccessoryView: <(null): 0x0>\
padr\'e3o	14:07:35.274652-0300	TecBizAssociado	_inputViewsForResponder, responderRequiresKeyboard 0 (automaticKeyboardEnabled: 1, activeInstance: <(null): 0x0>, self.isOnScreen: 0, requiresKBWhenFirstResponder: 0)\
padr\'e3o	14:07:35.274679-0300	TecBizAssociado	_inputViewsForResponder, useKeyboard 0 (allowsSystemInputView: 1, !inputView <(null): 0x0>, responderRequiresKeyboard 0)\
padr\'e3o	14:07:35.274705-0300	TecBizAssociado	_inputViewsForResponder, configuring _responderWithoutAutomaticAppearanceEnabled: <(null): 0x0> (_automaticAppearEnabled: 1)\
padr\'e3o	14:07:35.274732-0300	TecBizAssociado	_inputViewsForResponder, needsIVPlaceholder: 0, needsIAVPlaceholder: 0, needsInputSetWithPlaceholder: 0\
padr\'e3o	14:07:35.274756-0300	TecBizAssociado	_inputViewsForResponder returning: <<UIInputViewSet: 0x10673f180>; (empty)>\
padr\'e3o	14:07:35.274854-0300	TecBizAssociado	endPlacementForInputViewSet: <<UIInputViewSet: 0x10673f180>; (empty)> windowScene: <UIWindowScene: 0x1020545e0; role: UIWindowSceneSessionRoleApplication; persistentIdentifier: 365F0271-AC9C-4B57-89E2-DC56A99BFF36; activationState: UISceneActivationStateForegroundInactive>\
padr\'e3o	14:07:35.274967-0300	TecBizAssociado	-[_UIRemoteKeyboards prepareToMoveKeyboard:withIAV:isIAVRelevant:showing:notifyRemote:forScene:] position: \{\{0, 0\}, \{0, 0\}\} visible: 0; notifyRemote: 1; isMinimized: NO\
padr\'e3o	14:07:35.275074-0300	TecBizAssociado	Show keyboard with visual mode windowed (0)\
padr\'e3o	14:07:35.275556-0300	TecBizAssociado	[0x1065c4b00] activating connection: mach=true listener=false peer=false name=com.apple.powerlog.plxpclogger.xpc\
padr\'e3o	14:07:35.425271-0300	TecBizAssociado	Not push traits update to screen for new style 2, <UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36)\
padr\'e3o	14:07:35.425539-0300	TecBizAssociado	sceneOfRecord: sceneID: sceneID:com.tecbiz.tecbizassociadospush-default  persistentID: 365F0271-AC9C-4B57-89E2-DC56A99BFF36\
padr\'e3o	14:07:35.425638-0300	TecBizAssociado	Deactivation reason removed: 12; deactivation reasons: 4128 -> 32; animating application lifecycle event: 1\
padr\'e3o	14:07:35.425690-0300	TecBizAssociado	Send setDeactivating: N (-DeactivationReason:SuspendedEventsOnly)\
padr\'e3o	14:07:35.426526-0300	TecBizAssociado	Deactivation reason removed: 5; deactivation reasons: 32 -> 0; animating application lifecycle event: 0\
padr\'e3o	14:07:35.427983-0300	TecBizAssociado	SecSecurityClientGet new thread!\
padr\'e3o	14:07:35.428127-0300	TecBizAssociado	[com.tecbiz.tecbizassociadospush] Getting delivered notifications\
padr\'e3o	14:07:35.431574-0300	TecBizAssociado	[com.tecbiz.tecbizassociadospush] Got 0 delivered notifications [ hasCompletionHandler: 1 ]\
padr\'e3o	14:07:35.431941-0300	TecBizAssociado	[com.tecbiz.tecbizassociadospush] Removing all delivered notifications\
padr\'e3o	14:07:35.685000-0300	TecBizAssociado	<UIWindowScene: 0x1020545e0> (365F0271-AC9C-4B57-89E2-DC56A99BFF36) Scene updated orientation preferences: ( Pu ) -> ( Pu Ll Lr )\
padr\'e3o	14:07:40.270935-0300	TecBizAssociado	tcp_input [C5.1.1.1:3] flags=[F.] seq=2706379822, ack=3646541330, win=503 state=ESTABLISHED rcv_nxt=2706379822, snd_una=3646541330\
padr\'e3o	14:07:40.270991-0300	TecBizAssociado	nw_protocol_tcp_log_summary [C5.1.1.1:3] \
	[A373F426-86C9-46A5-BC31-C3161D2647E1 <private>:56508<-><private>:443]\
	Init: 1, Conn_Time: 22.057ms, SYNs: 1, WR_T: 0/0, RD_T: 0/0, TFO: 0/0/0, ECN: 0/1/1, Accurate ECN (client/server): Disabled/Disabled, TS: 1, TSO: 0\
	rtt_cache: none, rtt_upd: 4, rtt: 25.437ms, rtt_var: 11.937ms rtt_nc: 25.437ms, rtt_var_nc: 11.937ms base rtt: 21ms\
	ACKs-compressed: 0, ACKs delayed: 0 delayed ACKs sent: 0\
padr\'e3o	14:07:40.271275-0300	TecBizAssociado	tcp_input [C5.1.1.1:3] flags=[F.] seq=2706379822, ack=3646541330, win=503 state=CLOSE_WAIT rcv_nxt=2706379823, snd_una=3646541330\
padr\'e3o	14:07:40.274060-0300	TecBizAssociado	Connection 5: read-side closed\
padr\'e3o	14:07:40.274275-0300	TecBizAssociado	Connection 5: cleaning up\
padr\'e3o	14:07:40.274415-0300	TecBizAssociado	[C5 E97D5100-FD0F-412A-84CC-668484828466 Hostname#4d459f4b:443 quic-connection, url hash: 824ba16e, definite, attribution: developer] cancel\
padr\'e3o	14:07:40.275357-0300	TecBizAssociado	[C5 E97D5100-FD0F-412A-84CC-668484828466 Hostname#4d459f4b:443 quic-connection, url hash: 824ba16e, definite, attribution: developer] cancelled\
	[C5.1.1.1 0BAA454F-519B-49AB-BD02-C927DF7E625B *************:56508<->IPv4#181a64d9:443]\
	Connected Path: satisfied (Path is satisfied), viable, interface: en0[802.11], ipv4, ipv6, dns, uses wifi\
	Privacy Stance: Not Eligible\
	Duration: 5.258s, DNS @0.000s took 0.073s, TCP @0.077s took 0.022s, TLS 1.3 took 0.033s\
	bytes in/out: 7083/1010, packets in/out: 9/11, rtt: 0.025s, retransmitted bytes: 0, out-of-order bytes: 0\
	ecn packets sent/acked/marked/lost: 3/3/0/0\
padr\'e3o	14:07:40.276237-0300	TecBizAssociado	nw_flow_disconnected [C5 IPv4#181a64d9:443 cancelled parent-flow ((null))] Output protocol disconnected\
padr\'e3o	14:07:40.277158-0300	TecBizAssociado	nw_connection_report_state_with_handler_on_nw_queue [C5] reporting state cancelled\
padr\'e3o	14:07:40.277600-0300	TecBizAssociado	tcp_output [C5.1.1.1:3] flags=[F.] seq=3646541354, ack=2706379823, win=2048 state=LAST_ACK rcv_nxt=2706379823, snd_una=3646541330\
padr\'e3o	14:07:22.522999-0300	useractivityd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.657458-0300	useractivityd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.750488-0300	useractivityd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.564909-0300	useractivityd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.073441-0300	useractivityd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.108983-0300	useractivityd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.448051-0300	UserEventAgent	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.656148-0300	UserEventAgent	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.748025-0300	UserEventAgent	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.562982-0300	UserEventAgent	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.049743-0300	UserEventAgent	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.107758-0300	UserEventAgent	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.534497-0300	watchdogd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.657299-0300	watchdogd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.749738-0300	watchdogd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.564645-0300	watchdogd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.072653-0300	watchdogd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.108959-0300	watchdogd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:15.473890-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.473920-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.474014-0300	wifid	__WiFiDeviceManagerApplyConstrainedInterfaceConfig: updated save data mode to Automatic(0) for network TecBiz_Tim_5G\
padr\'e3o	14:07:15.475348-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
erro	14:07:15.507236-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network TecBiz_2.4 excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
erro	14:07:15.512459-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network Tecbiz_2.4G excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
padr\'e3o	14:07:15.526485-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.567687-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.568038-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.568440-0300	wifid	__WiFiDeviceManagerApplyConstrainedInterfaceConfig: updated save data mode to Automatic(0) for network TecBiz_Tim_5G\
padr\'e3o	14:07:15.570044-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
erro	14:07:15.614484-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network TecBiz_2.4 excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
erro	14:07:15.617574-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network Tecbiz_2.4G excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
padr\'e3o	14:07:15.625906-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.631495-0300	wifid	WiFiNetworkSyncHelperCreateNetworkRef for <TecBiz_2.4>\
padr\'e3o	14:07:15.631787-0300	wifid	WiFiNetworkSyncHelperCreateNetworkRef: network content to be returned to caller: TecBiz_2.4: isHidden=0, isEAP=0, isSAE=0, isWPA=1, isWEP=0, WAPI=0, type=0, enabled=(null), saveData=0, responsiveness=(null) ((null)) isHome=Unknown, isForceFixed=0, transitionDisabledFlags=0, foundNanIe=0, isPH=0, isPublicAirPlayNetwork=0, is6EDisabled=0, hs20=0, Channel=0, isBundleIdentifierPresent=0, PolicyUUID=(null)\
padr\'e3o	14:07:15.631818-0300	wifid	__WiFiCloudSyncIsPasswordPresent - for network TecBiz_2.4\
padr\'e3o	14:07:15.633770-0300	wifid	WiFiManagerAddNetwork: reason 3, with SSID TecBiz_2.4\
padr\'e3o	14:07:15.634793-0300	wifid	__WiFiManagerModifyExistingNetwork: Network with the same SSID TecBiz_2.4 found at index 84\
padr\'e3o	14:07:15.635198-0300	wifid	__WiFiManagerModifyExistingNetwork: No AJ prefs detected for incoming network TecBiz_2.4, so preserve AJ settings in existing network TecBiz_2.4: isHidden=0, isEAP=0, isSAE=0, isWPA=1, isWEP=0, WAPI=0, type=0, enabled=true, saveData=0, responsiveness=(null) ((null)) isHome=Unknown, isForceFixed=0, transitionDisabledFlags=0, foundNanIe=0, isPH=0, isPublicAirPlayNetwork=0, is6EDisabled=0, hs20=0, Channel=36, isBundleIdentifierPresent=0, PolicyUUID=(null)\
padr\'e3o	14:07:15.635257-0300	wifid	WiFiNetworkSetDisabledUntilDate: Clearing disable-until property for SSID 'TecBiz_2.4'\
padr\'e3o	14:07:15.635288-0300	wifid	__WiFiManagerModifyExistingNetwork: network properties after AJ properties merge: TecBiz_2.4: isHidden=0, isEAP=0, isSAE=0, isWPA=1, isWEP=0, WAPI=0, type=0, enabled=true, saveData=0, responsiveness=(null) ((null)) isHome=Unknown, isForceFixed=0, transitionDisabledFlags=0, foundNanIe=0, isPH=0, isPublicAirPlayNetwork=0, is6EDisabled=0, hs20=0, Channel=36, isBundleIdentifierPresent=0, PolicyUUID=(null)\
padr\'e3o	14:07:15.635319-0300	wifid	__WiFiManagerModifyExistingNetwork: existingNetwork TecBiz_2.4: isHidden=0, isEAP=0, isSAE=0, isWPA=1, isWEP=0, WAPI=0, type=0, enabled=true, saveData=0, responsiveness=(null) ((null)) isHome=Unknown, isForceFixed=0, transitionDisabledFlags=0, foundNanIe=0, isPH=0, isPublicAirPlayNetwork=0, is6EDisabled=0, hs20=0, Channel=36, isBundleIdentifierPresent=0, PolicyUUID=(null) has CWF specific attributes\
padr\'e3o	14:07:15.635371-0300	wifid	__WiFiManagerModifyExistingNetwork: network properties after adding missing keys: TecBiz_2.4: isHidden=0, isEAP=0, isSAE=0, isWPA=1, isWEP=0, WAPI=0, type=0, enabled=true, saveData=0, responsiveness=(null) ((null)) isHome=Unknown, isForceFixed=0, transitionDisabledFlags=0, foundNanIe=0, isPH=0, isPublicAirPlayNetwork=0, is6EDisabled=0, hs20=0, Channel=36, isBundleIdentifierPresent=0, PolicyUUID=(null)\
padr\'e3o	14:07:15.635442-0300	wifid	__WiFiManagerModifyExistingNetwork: overriding previous join date from 2023-08-02 16:51:45 +0000 to 2023-10-06 16:32:36 +0000 for network TecBiz_2.4\
padr\'e3o	14:07:15.635469-0300	wifid	__WiFiManagerModifyExistingNetwork: network properties after modified network: TecBiz_2.4: isHidden=0, isEAP=0, isSAE=0, isWPA=1, isWEP=0, WAPI=0, type=0, enabled=true, saveData=0, responsiveness=(null) ((null)) isHome=Unknown, isForceFixed=0, transitionDisabledFlags=0, foundNanIe=0, isPH=0, isPublicAirPlayNetwork=0, is6EDisabled=0, hs20=0, Channel=36, isBundleIdentifierPresent=0, PolicyUUID=(null)\
padr\'e3o	14:07:15.673749-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.673799-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.673928-0300	wifid	__WiFiDeviceManagerApplyConstrainedInterfaceConfig: updated save data mode to Automatic(0) for network TecBiz_Tim_5G\
padr\'e3o	14:07:15.674850-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
erro	14:07:15.705049-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network TecBiz_2.4 excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
erro	14:07:15.710768-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network Tecbiz_2.4G excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
padr\'e3o	14:07:15.718257-0300	wifid	WiFiManagerAddNetwork: <TecBiz_2.4> added due to sync\
padr\'e3o	14:07:15.721004-0300	wifid	__WiFiManagerDispatchClientsNetworksChangedEvent: type modified network TecBiz_2.4\
padr\'e3o	14:07:15.721065-0300	wifid	 WiFiManagerAddPrivateMacNetwork WFMacRandomisation : Checking if network already present : Retrieving private mac cache version of the network <TecBiz_2.4>\
padr\'e3o	14:07:15.723915-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:15.728149-0300	wifid	WFMacRandomisation : Added/replaced network <TecBiz_2.4> to list of known private mac networks\
padr\'e3o	14:07:16.269314-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:16.269539-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:208.5secs Channel: 52 Bandwidth: 80Mhz Rssi: -43 \{-43 0\} Cca: 0 (S:0 O:0 I:0) Snr: 15 BcnPer: 2.0% (49, 52.6%) TxFrameCnt: 0 TxPer: 0.0% TxReTrans: 0 TxRetryRatio: 0.0% RxFrameCnt: 1 RxRetryFrames: 0 RxRetryRatio: 0.0% TxRate: 286760 RxRate: 1200950 FBRate: 172050 TxFwFrms: 0 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 16.9secs fgApp: com.tecbiz.tecbizassociadospush V: T\
padr\'e3o	14:07:16.273344-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:21.286883-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:21.287667-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:213.5secs Channel: 52 Bandwidth: 80Mhz Rssi: -44 \{-44 0\} Cca: 0 (S:0 O:0 I:0) Snr: 15 BcnPer: 2.0% (49, 52.6%) TxFrameCnt: 6 TxPer: 0.0% TxReTrans: 0 TxRetryRatio: 0.0% RxFrameCnt: 4 RxRetryFrames: 0 RxRetryRatio: 0.0% TxRate: 1200950 RxRate: 1200950 FBRate: 720580 TxFwFrms: 4 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 21.9secs fgApp: com.tecbiz.tecbizassociadospush V: T\
padr\'e3o	14:07:21.292193-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:21.619754-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:21.619868-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:21.620378-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:22.523767-0300	wifid	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.652190-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:22.660707-0300	wifid	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.752444-0300	wifid	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:22.753025-0300	wifid	-[WiFiUserInteractionMonitor setApplicationRunningState:foregroundState:andNetworkingState:forBundleId:]: com.tecbiz.tecbizassociadospush entered background\
padr\'e3o	14:07:22.754960-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush",\
    "com.apple.chrono.WidgetRenderer-Default"\
)\}\
padr\'e3o	14:07:23.992549-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:23.992573-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:23.993027-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:23.993495-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:24.003571-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush",\
    "com.apple.chrono.WidgetRenderer-Default"\
)\}\
padr\'e3o	14:07:25.555627-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:25.564805-0300	wifid	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:26.072683-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:26.086152-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:26.657392-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:26.658466-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:218.8secs Channel: 52 Bandwidth: 80Mhz Rssi: -39 \{-40 -49\} Cca: 0 (S:0 O:0 I:0) Snr: 18 BcnPer: 1.8% (55, 52.5%) TxFrameCnt: 28 TxPer: 3.6% TxReTrans: 13 TxRetryRatio: 46.4% RxFrameCnt: 26 RxRetryFrames: 4 RxRetryRatio: 15.4% TxRate: 1200950 RxRate: 1020830 FBRate: 720580 TxFwFrms: 8 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 27.3secs fgApp: com.apple.chrono.WidgetRenderer-Default V: T\
padr\'e3o	14:07:26.662206-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:26.862575-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:27.179800-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:27.546113-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:27.546428-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:27.546669-0300	wifid	__WiFiDeviceManagerApplyConstrainedInterfaceConfig: updated save data mode to Automatic(0) for network TecBiz_Tim_5G\
padr\'e3o	14:07:27.549094-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
erro	14:07:27.585700-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network TecBiz_2.4 excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
erro	14:07:27.591316-0300	wifid	WiFiDeviceManagerSetBackgroundScan: network Tecbiz_2.4G excluded from BGScan: isWoWBlacklisted=0 isAJBlacklisted=0 isTDBlacklisted=0 isAdHoc=0 isWoWAllowed=1 isAutoJoinAllowedInLockdownMode=1 isKnownNetworkDisallowed=1\
padr\'e3o	14:07:27.601982-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:28.486856-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:28.536025-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:28.536147-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:28.536407-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:28.536948-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:29.541868-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.WebKit.WebContent",\
    "com.apple.mobilesafari",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:29.575364-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.WebKit.WebContent",\
    "com.apple.mobilesafari",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.apple.WebKit.GPU",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:32.655627-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:32.655677-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:224.8secs Channel: 52 Bandwidth: 80Mhz Rssi: -39 \{-43 -46\} Cca: 0 (S:0 O:0 I:0) Snr: 16 BcnPer: 1.7% (59, 52.5%) TxFrameCnt: 643 TxPer: 0.3% TxReTrans: 150 TxRetryRatio: 23.3% RxFrameCnt: 822 RxRetryFrames: 1 RxRetryRatio: 0.1% TxRate: 1200950 RxRate: 1200950 FBRate: 720580 TxFwFrms: 6 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 33.3secs fgApp: com.apple.WebKit.GPU V: T\
padr\'e3o	14:07:32.656270-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:35.077450-0300	wifid	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.077764-0300	wifid	-[WiFiUserInteractionMonitor setApplicationRunningState:foregroundState:andNetworkingState:forBundleId:]: com.tecbiz.tecbizassociadospush entered foreground\
padr\'e3o	14:07:35.077788-0300	wifid	WifiDeviceManagerCatsWhitelistedApp: CATS en0:  deviceManager:0x682414000 FgApp:com.tecbiz.tecbizassociadospush stateChange:0 whitelisted=1\
padr\'e3o	14:07:35.077894-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.WebKit.WebContent",\
    "com.apple.mobilesafari",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.apple.WebKit.GPU",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:35.109149-0300	wifid	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.871262-0300	wifid	WifiDeviceManagerCatsWhitelistedApp: CATS en0:  deviceManager:0x682414000 FgApp:com.tecbiz.tecbizassociadospush stateChange:0 whitelisted=1\
padr\'e3o	14:07:35.871287-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.WebKit.WebContent",\
    "com.apple.mobilesafari",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.apple.WebKit.GPU",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:36.019581-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:36.019704-0300	wifid	WifiDeviceManagerCatsWhitelistedApp: CATS en0:  deviceManager:0x682414000 FgApp:com.tecbiz.tecbizassociadospush stateChange:0 whitelisted=1\
padr\'e3o	14:07:36.019731-0300	wifid	-[WiFiUsageApplicationSession applicationStateDidChange:withAttributes:]: application session resumed:\{(\
    "com.apple.chrono.WidgetRenderer-Default",\
    "net.whatsapp.WhatsApp",\
    "com.apple.WebKit.WebContent",\
    "com.apple.mobilesafari",\
    "com.apple.PhotosUIPrivate.PhotosPosterProvider",\
    "com.apple.WebKit.GPU",\
    "com.tecbiz.tecbizassociadospush"\
)\}\
padr\'e3o	14:07:37.663882-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:37.664088-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:229.9secs Channel: 52 Bandwidth: 80Mhz Rssi: -40 \{-41 -46\} Cca: 0 (S:0 O:0 I:0) Snr: 18 BcnPer: 0.0% (49, 52.4%) TxFrameCnt: 390 TxPer: 0.0% TxReTrans: 41 TxRetryRatio: 10.5% RxFrameCnt: 537 RxRetryFrames: 0 RxRetryRatio: 0.0% TxRate: 1200950 RxRate: 1200950 FBRate: 720580 TxFwFrms: 6 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 38.3secs fgApp: com.tecbiz.tecbizassociadospush V: T\
padr\'e3o	14:07:37.665647-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:40.138000-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:42.678200-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:42.678241-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:234.9secs Channel: 52 Bandwidth: 80Mhz Rssi: -39 \{-40 -49\} Cca: 0 (S:0 O:0 I:0) Snr: 23 BcnPer: 2.0% (49, 52.4%) TxFrameCnt: 33 TxPer: 0.0% TxReTrans: 2 TxRetryRatio: 6.1% RxFrameCnt: 26 RxRetryFrames: 0 RxRetryRatio: 0.0% TxRate: 1200950 RxRate: 1200950 FBRate: 720580 TxFwFrms: 14 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 43.3secs fgApp: com.tecbiz.tecbizassociadospush V: T\
padr\'e3o	14:07:42.679667-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:47.698421-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:47.698687-0300	wifid	__WiFiLQAMgrLogStats(TecBiz_Tim_5G:Moving): InfraUptime:239.9secs Channel: 52 Bandwidth: 80Mhz Rssi: -39 \{-39 -50\} Cca: 0 (S:0 O:0 I:0) Snr: 16 BcnPer: 0.0% (49, 52.4%) TxFrameCnt: 2 TxPer: 0.0% TxReTrans: 0 TxRetryRatio: 0.0% RxFrameCnt: 4 RxRetryFrames: 0 RxRetryRatio: 0.0% TxRate: 1200950 RxRate: 1200950 FBRate: 720580 TxFwFrms: 5 TxFwFail: 0 Noise: -88 \{-88 -89 -1\} time: 0.1secs fgApp: com.tecbiz.tecbizassociadospush V: T\
padr\'e3o	14:07:47.702930-0300	wifid	__WiFiDeviceManagerEvaluateAPEnvironment: WiFiRoam : BSS List info for network : TecBiz_Tim_5G : chanCount5GHz: [1] chanCount24GHz: [0] chanCount6GHz: [0]\
padr\'e3o	14:07:22.518472-0300	WirelessRadioManagerd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.656999-0300	WirelessRadioManagerd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:22.748857-0300	WirelessRadioManagerd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:25.563700-0300	WirelessRadioManagerd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-suspended-NotVisible\
padr\'e3o	14:07:35.080323-0300	WirelessRadioManagerd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
padr\'e3o	14:07:35.108433-0300	WirelessRadioManagerd	Received state update for 541 (app<com.tecbiz.tecbizassociadospush(6DA2C38B-1CE6-440E-9348-4F8D1B48D7C1)>, running-active-Visible\
}