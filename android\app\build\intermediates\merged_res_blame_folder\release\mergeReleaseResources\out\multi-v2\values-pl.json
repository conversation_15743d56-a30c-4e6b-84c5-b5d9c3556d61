{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,319,423,513,668,789,903,1028,1166,1319,1446,1574,1721,1821,1976,2097,2231,2370,2494,2616,2709,2836,2935,3068,3167,3300", "endColumns": "148,114,103,89,154,120,113,124,137,152,126,127,146,99,154,120,133,138,123,121,92,126,98,132,98,132,99", "endOffsets": "199,314,418,508,663,784,898,1023,1161,1314,1441,1569,1716,1816,1971,2092,2226,2365,2489,2611,2704,2831,2930,3063,3162,3295,3395"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,98,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2901,3050,6411,6724,6996,7151,7590,7704,7829,7967,8120,8247,8375,8522,8622,8777,8898,9032,9171,10438,12601,12694,12821,12920,13053,13152,13285", "endColumns": "148,114,103,89,154,120,113,124,137,152,126,127,146,99,154,120,133,138,123,121,92,126,98,132,98,132,99", "endOffsets": "3045,3160,6510,6809,7146,7267,7699,7824,7962,8115,8242,8370,8517,8617,8772,8893,9027,9166,9290,10555,12689,12816,12915,13048,13147,13280,13380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6515,7272,7371,7486", "endColumns": "99,98,114,103", "endOffsets": "6610,7366,7481,7585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5149", "endColumns": "139", "endOffsets": "5284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,189,284,369,478,583,660,737,830,920,1003,1086,1173,1245,1329,1405,1483,1559,1641,1709", "endColumns": "83,94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "184,279,364,473,578,655,732,825,915,998,1081,1168,1240,1324,1400,1478,1554,1636,1704,1824"}, "to": {"startLines": "29,39,40,62,64,65,87,88,91,92,95,96,101,102,106,109,111,116,117,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2817,3902,3997,6615,6814,6919,9522,9599,9847,9937,10198,10281,10728,10800,11127,11364,11514,11925,12007,12151", "endColumns": "83,94,84,108,104,76,76,92,89,82,82,86,71,83,75,77,75,81,67,119", "endOffsets": "2896,3992,4077,6719,6914,6991,9594,9687,9932,10015,10276,10363,10795,10879,11198,11437,11585,12002,12070,12266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,205,274,359,435,514,597,692,762,847,933,1008,1090,1173,1251,1323,1393,1479,1557,1633,1707", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "127,200,269,354,430,509,592,687,757,842,928,1003,1085,1168,1246,1318,1388,1474,1552,1628,1702,1782"}, "to": {"startLines": "41,84,85,86,89,90,93,94,97,99,103,104,105,107,108,110,112,113,115,118,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4082,9295,9368,9437,9692,9768,10020,10103,10368,10560,10884,10970,11045,11203,11286,11442,11590,11660,11847,12075,12271,12345", "endColumns": "76,72,68,84,75,78,82,94,69,84,85,74,81,82,77,71,69,85,77,75,73,79", "endOffsets": "4154,9363,9432,9517,9763,9842,10098,10193,10433,10640,10965,11040,11122,11281,11359,11509,11655,11741,11920,12146,12340,12420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "138,226", "endColumns": "87,87", "endOffsets": "221,309"}, "to": {"startLines": "122,123", "startColumns": "4,4", "startOffsets": "12425,12513", "endColumns": "87,87", "endOffsets": "12508,12596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,10645", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,10723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "32,33,34,35,36,37,38,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3165,3262,3364,3462,3561,3675,3780,11746", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3257,3359,3457,3556,3670,3775,3897,11842"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4159,4263,4431,4553,4663,4814,4939,5050,5289,5460,5569,5744,5872,6031,6192,6261,6327", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "4258,4426,4548,4658,4809,4934,5045,5144,5455,5564,5739,5867,6026,6187,6256,6322,6406"}}]}]}