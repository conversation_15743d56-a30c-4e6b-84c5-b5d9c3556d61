{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,10270", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,10347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "32,33,34,35,36,37,38,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3202,3302,3404,3507,3614,3716,3820,10876", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "3297,3399,3502,3609,3711,3815,3926,10972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4196,4302,4451,4580,4687,4832,4959,5074,5341,5510,5617,5767,5897,6034,6198,6262,6322", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "4297,4446,4575,4682,4827,4954,5069,5178,5505,5612,5762,5892,6029,6193,6257,6317,6395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6509,7253,7358,7467", "endColumns": "98,104,108,105", "endOffsets": "6603,7353,7462,7568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,287,371,481,581,666,748,846,935,1020,1105,1192,1265,1352,1426,1499,1572,1651,1719", "endColumns": "88,92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "189,282,366,476,576,661,743,841,930,1015,1100,1187,1260,1347,1421,1494,1567,1646,1714,1832"}, "to": {"startLines": "29,39,40,62,64,65,86,87,90,91,92,93,96,97,98,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2817,3931,4024,6608,6808,6908,9480,9562,9797,9886,9971,10056,10352,10425,10512,10586,10731,10977,11056,11124", "endColumns": "88,92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "2901,4019,4103,6713,6903,6988,9557,9655,9881,9966,10051,10138,10420,10507,10581,10654,10799,11051,11119,11237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,223,351,460,550,698,810,927,1047,1194,1323,1473,1598,1751,1850,2006,2135,2277,2436,2565,2692,2805,2951,3055,3195,3295,3431", "endColumns": "167,127,108,89,147,111,116,119,146,128,149,124,152,98,155,128,141,158,128,126,112,145,103,139,99,135,103", "endOffsets": "218,346,455,545,693,805,922,1042,1189,1318,1468,1593,1746,1845,2001,2130,2272,2431,2560,2687,2800,2946,3050,3190,3290,3426,3530"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,94,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2906,3074,6400,6718,6993,7141,7573,7690,7810,7957,8086,8236,8361,8514,8613,8769,8898,9040,9199,10143,11445,11558,11704,11808,11948,12048,12184", "endColumns": "167,127,108,89,147,111,116,119,146,128,149,124,152,98,155,128,141,158,128,126,112,145,103,139,99,135,103", "endOffsets": "3069,3197,6504,6803,7136,7248,7685,7805,7952,8081,8231,8356,8509,8608,8764,8893,9035,9194,9323,10265,11553,11699,11803,11943,12043,12179,12283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "143,242", "endColumns": "98,103", "endOffsets": "237,341"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11242,11341", "endColumns": "98,103", "endOffsets": "11336,11440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5183", "endColumns": "157", "endOffsets": "5336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,212,295,365,432,504", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "138,207,290,360,427,499,571"}, "to": {"startLines": "41,84,85,88,89,100,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4108,9328,9397,9660,9730,10659,10804", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "4191,9392,9475,9725,9792,10726,10871"}}]}]}