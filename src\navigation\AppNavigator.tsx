import React, { useState, useEffect } from 'react';
import { View, StatusBar, Alert, LogBox } from 'react-native';
// import { SafeAreaView } from 'react-native-safe-area-context';
// AsyncStorage removido - usando storage em memória
import { lockToPortrait } from '../utils/orientation';
import SplashScreen from '../screens/SplashScreen';
import LoginScreen from '../screens/LoginScreen';
import HomeScreen from '../screens/HomeScreen';
import SaldoScreen from '../screens/SaldoScreen';
import ExtratoScreen from '../screens/ExtratoScreen';
import EsqueceuSenhaScreen from '../screens/EsqueceuSenhaScreen';
import AutorizacoesPendentesScreen from '../screens/AutorizacoesPendentesScreen';
import AlterarSenhaScreen from '../screens/AlterarSenhaScreen';
import RedeConveniadaScreen from '../screens/RedeConveniadaScreen';
import RedeConveniadaResultadosScreen from '../screens/RedeConveniadaResultadosScreen';
import CartaoVirtualScreen from '../screens/CartaoVirtualScreen';
import BloquearCartaoScreen from '../screens/BloquearCartaoScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import SendMessageScreen from '../screens/SendMessageScreen';
import DebugTokenScreen from '../screens/DebugTokenScreen';
import DebugLogsScreen from '../screens/DebugLogsScreen';
import { setupDeepLinkListener, confirmarEmail, DeepLinkData } from '../services/deepLinking';
import { Platform } from 'react-native';
import firebaseSimpleService from '../services/firebaseSimpleService';
import pushNotificationService, { notificationEvents } from '../services/pushNotificationService';
import { usePushNotifications } from '../store/pushNotificationStore';
import { ScreenName } from '../types/navigation';

// Storage em memória para controle de notificações de exemplo
let hasSampleNotifications = false;

// Suprimir warnings do Expo Go sobre push notifications
LogBox.ignoreLogs([
  'expo-notifications: Android Push notifications',
  'expo-notifications functionality is not fully supported in Expo Go',
  'We recommend you instead use a development build'
]);

// Navegação simples sem dependências nativas
export default function AppNavigator() {
  const [currentScreen, setCurrentScreen] = useState<ScreenName>('Splash');
  const [homeParams, setHomeParams] = useState<any>(null);
  const [autorizacoesParams, setAutorizacoesParams] = useState<any>(null);
  const [alterarSenhaParams, setAlterarSenhaParams] = useState<any>(null);
  const [screenParams, setScreenParams] = useState<any>(null);

  // ✨ NOVO: Flag para controlar se estamos processando deep link
  const [isProcessingDeepLink, setIsProcessingDeepLink] = useState(false);

  // Push notifications store
  const pushStore = usePushNotifications();

  // Controle de orientação - bloquear rotação
  useEffect(() => {
    lockToPortrait();
  }, []);

  // Configurar deep linking
  useEffect(() => {
    console.log('🔗 Configurando deep linking...');

    const handleDeepLink = async (data: DeepLinkData) => {
      console.log('🔗 Deep link recebido:', data);
      console.log('📱 Plataforma atual:', Platform.OS);
      console.log('📱 Tela atual:', currentScreen);

      if (data.action === 'email_confirmation') {
        try {
          console.log('📧 Processando confirmação de email via deep link');
          console.log('📦 Parâmetros recebidos:', data.params);

          // ✨ NOVO: Marcar que estamos processando deep link
          setIsProcessingDeepLink(true);

          // ✨ NOVO: Limpar dados de notificações ANTES de processar deep link
          console.log('🔗 Limpando dados de notificações antes do processamento...');
          try {
            await pushNotificationService.clearPreviousUserData();
            console.log('✅ Dados de notificações limpos para deep link');
          } catch (error) {
            console.error('❌ Erro ao limpar dados para deep link:', error);
          }

          // Verificar se temos os dados essenciais do usuário vindos do PHP
          if (data.params.codass && data.params.codent) {
            const debugData = {
              codass: data.params.codass,
              codent: data.params.codent,
              cartao: data.params.cartao && typeof data.params.cartao === 'string' ? data.params.cartao.substring(0, 4) + '****' : 'N/A'
            };

            console.log('✅ Dados do usuário encontrados no deep link:', debugData);

            // Log visível no Expo Go
            console.warn('🔍 NAVIGATOR - Processando confirmação para:', debugData.codass);

            // ✨ MELHORADO: Processar deep link de forma mais robusta
            console.log('🔄 Iniciando confirmação de email via API...');

            let result;
            try {
              // Validar parâmetros antes de chamar a API
              // IMPORTANTE: O PHP espera 'modo' em base64 e deve ser 'confirmaEmail'
              const modo = 'confirmaEmail'; // Valor correto esperado pelo PHP
              const dados = data.params.dados || '';

              console.log('🔍 Parâmetros para confirmação:', { modo, dados: dados ? 'presente' : 'ausente' });

              result = await confirmarEmail(modo, dados);
              console.log('📧 Resultado da confirmação:', result);
            } catch (apiError) {
              console.error('❌ Erro na API de confirmação:', apiError);

              // Log visível no Expo Go
              const errorMsg = apiError instanceof Error ? apiError.message : 'Unknown error';
              console.warn('🚨 ERRO API:', errorMsg);

              // Para iOS, tentar continuar mesmo com erro de API se temos os dados do usuário
              if (Platform.OS === 'ios') {
                console.log('🍎 iOS: Continuando com dados do deep link mesmo com erro de API');
                result = { success: true, message: 'Processado via deep link' };
              } else {
                throw apiError;
              }
            }

            if (result.success) {
              // ✨ MELHORADO: Navegação mais direta sem alert intermediário
              console.log('✅ Email confirmado, preparando navegação direta...');

              // Preparar parâmetros para AlterarSenha
              const alterarSenhaParams = {
                fromEmail: true,
                emailConfirmed: true,
                userData: {
                  codass: data.params.codass,
                  codent: data.params.codent,
                  cartao: data.params.cartao,
                  emailCadastro: data.params.emailCadastro,
                  portador: data.params.portador
                },
                hideMenu: true,
                senhaInicial: true
              };

              console.log('🧭 Navegando diretamente para AlterarSenha...');
              setAlterarSenhaParams(alterarSenhaParams);

              // ✨ NOVO: Navegação imediata para evitar conflitos
              if (currentScreen === 'Splash') {
                // Se ainda estamos no splash, aguardar um pouco para ele terminar
                setTimeout(() => {
                  navigate('AlterarSenha');
                }, 1000);
              } else {
                // Navegar imediatamente
                navigate('AlterarSenha');
              }

              // ✨ NOVO: Mostrar alert APÓS a navegação e limpar flag
              setTimeout(() => {
                Alert.alert(
                  'Email Confirmado!',
                  'Agora você pode definir sua nova senha.',
                  [{
                    text: 'OK',
                    onPress: () => {
                      // Limpar flag após processamento completo
                      setIsProcessingDeepLink(false);
                    }
                  }]
                );
              }, 1500);

            } else {
              console.error('❌ Falha na confirmação de email:', result.message);
              setIsProcessingDeepLink(false); // Limpar flag em caso de erro
              Alert.alert('Erro', result.message || 'Erro ao confirmar email. Tente novamente.');
            }
          } else {
            console.warn('⚠️ Dados do usuário não encontrados no deep link');
            console.warn('⚠️ Parâmetros disponíveis:', Object.keys(data.params));
            setIsProcessingDeepLink(false); // Limpar flag em caso de erro
            Alert.alert(
              'Erro',
              'Não foi possível obter os dados necessários. Tente fazer login novamente.',
              [{ text: 'OK', onPress: () => navigate('Login') }]
            );
          }
        } catch (error) {
          console.error('❌ Erro ao processar confirmação:', error);
          console.error('❌ Detalhes do erro:', {
            message: error instanceof Error ? error.message : 'Unknown error',
            name: error instanceof Error ? error.name : 'Unknown',
            stack: error instanceof Error ? error.stack?.substring(0, 300) : 'No stack'
          });

          // ✨ MELHORADO: Tratamento de erro mais específico para iOS
          let errorMessage = 'Erro ao confirmar email. Tente novamente.';

          if (Platform.OS === 'ios') {
            if (error instanceof Error) {
              if (error.message.includes('Network') || error.message.includes('network')) {
                errorMessage = 'Erro de conexão no iOS. Verifique sua internet e tente novamente.';
              } else if (error.message.includes('SSL') || error.message.includes('certificate')) {
                errorMessage = 'Erro de certificado SSL. Tente novamente.';
              } else {
                errorMessage = 'Erro no iOS. Verifique sua conexão e tente novamente.';
              }
            }
          }

          setIsProcessingDeepLink(false); // Limpar flag em caso de erro
          Alert.alert('Erro', errorMessage, [
            { text: 'OK', onPress: () => navigate('Login') }
          ]);
        }
      }
    };

    // Configurar listener
    const removeListener = setupDeepLinkListener(handleDeepLink);

    // Cleanup
    return removeListener;
  }, [currentScreen]);

  // Inicializar push notifications
  useEffect(() => {
    const initializePushNotifications = async () => {
      try {
        console.log('🔔 Inicializando push notifications...');

        // Usar o novo serviço completo de push notifications
        console.log('🔄 Inicializando Push Notification Service...');
        let token = await pushNotificationService.initialize();

        // Se falhar, tentar o serviço simplificado como backup
        if (!token) {
          console.log('🔄 Fallback para Firebase Simple Service...');
          token = await firebaseSimpleService.initialize();
        }

        // Salvar token na store
        if (token) {
          pushStore.setFcmToken(token);
          console.log('🎯 Token salvo na store:', token.substring(0, 50) + '...');

          // Verificar se é token real ou simulado
          const isRealToken = token.startsWith('ExponentPushToken') || token.length > 100;

          if (isRealToken) {
            console.log('🚨 TOKEN REAL OBTIDO!');
            console.log('📱 Tipo: Expo Push Token REAL');
            console.log('🎯 Token completo:', token);
          } else {
            console.log('⚠️ Token simulado obtido (Expo Go limitation)');
            console.log('💡 Para tokens reais, use um development build ou dispositivo físico');
          }
        } else {
          console.log('❌ Não foi possível obter token');
        }

        // Atualizar estado na store
        pushStore.setInitialized(true);
        pushStore.setPermission(true); // Se chegou até aqui, tem permissão

        console.log('✅ Push notifications REAIS configuradas no AppNavigator');

      } catch (error) {
        console.error('❌ Erro ao inicializar push notifications:', error);
      }
    };

    initializePushNotifications();
  }, []);

  // Listener para navegação automática quando notificação é tocada
  useEffect(() => {
    const handleNotificationTapped = (data: any) => {
      console.log('🧭 Notificação tocada - verificando se pode navegar');
      console.log('📋 Dados da notificação:', data.notification?.title);
      console.log('🔗 Processando deep link?', isProcessingDeepLink);

      // ✨ NOVO: Não navegar para notificações se estamos processando deep link
      if (isProcessingDeepLink) {
        console.log('⚠️ Deep link em processamento - ignorando navegação para notificações');
        return;
      }

      // ✨ NOVO: Não navegar se já estamos na tela de AlterarSenha (pode ser deep link)
      if (currentScreen === 'AlterarSenha') {
        console.log('⚠️ Já na tela AlterarSenha - ignorando navegação para notificações');
        return;
      }

      console.log('✅ Navegando para tela de notificações');
      navigate('Notifications');
    };

    // Registrar listener
    notificationEvents.on('notificationTapped', handleNotificationTapped);

    // Cleanup
    return () => {
      notificationEvents.off('notificationTapped', handleNotificationTapped);
    };
  }, [isProcessingDeepLink, currentScreen]);

  const navigate = (screen: ScreenName, params?: any) => {
    console.log('🧭 Navegando para:', screen);
    console.log('🧭 Params recebidos:', JSON.stringify(params, null, 2));
    console.log('🧭 Tela atual antes:', currentScreen);

    if (params) {
      if (screen === 'Home') {
        console.log('🧭 Salvando em homeParams:', JSON.stringify(params, null, 2));
        setHomeParams(params);
      } else if (screen === 'AutorizacoesPendentes') {
        console.log('🧭 Salvando em autorizacoesParams:', JSON.stringify(params, null, 2));
        setAutorizacoesParams(params);
      } else if (screen === 'AlterarSenha') {
        console.log('🧭 Salvando em alterarSenhaParams:', JSON.stringify(params, null, 2));
        setAlterarSenhaParams(params);
      } else if (screen === 'RedeConveniadaResultados') {
        console.log('🧭 Salvando em screenParams:', JSON.stringify(params, null, 2));
        setScreenParams(params);
      }
    } else {
      console.log('🧭 Nenhum parâmetro para salvar');
    }

    // Aguardar um pouco para o estado ser atualizado antes de mudar a tela
    setTimeout(() => {
      console.log('🧭 Mudando tela para:', screen);
      setCurrentScreen(screen);
    }, 50);
  };

  const renderScreen = () => {
    console.log('🎬 Renderizando tela:', currentScreen);
    console.log('🎬 homeParams atual:', JSON.stringify(homeParams, null, 2));

    switch (currentScreen) {
      case 'Splash':
        return <SplashScreen navigation={{ navigate }} />;
      case 'Login':
        return <LoginScreen navigation={{ navigate }} />;
      case 'Home':
        return <HomeScreen navigation={{ navigate }} route={{ params: { userData: homeParams } }} />;
      case 'Saldo':
        return <SaldoScreen navigation={{ navigate }} />;
      case 'Extrato':
        return <ExtratoScreen navigation={{ navigate }} />;
      case 'EsqueceuSenha':
        return <EsqueceuSenhaScreen navigation={{ navigate, goBack: () => navigate('Login') }} />;
      case 'AutorizacoesPendentes':
        console.log('🎬 Renderizando AutorizacoesPendentes com params:', JSON.stringify(autorizacoesParams, null, 2));
        return <AutorizacoesPendentesScreen navigation={{ navigate }} route={{ params: autorizacoesParams }} />;
      case 'AlterarSenha':
        console.log('🎬 Renderizando AlterarSenha com params:', JSON.stringify(alterarSenhaParams, null, 2));
        return <AlterarSenhaScreen navigation={{ navigate, goBack: () => navigate('Home') }} route={{ params: alterarSenhaParams }} />;
      case 'RedeConveniada':
        return <RedeConveniadaScreen navigation={{ navigate, goBack: () => navigate('Home') }} />;
      case 'RedeConveniadaResultados':
        return <RedeConveniadaResultadosScreen navigation={{ goBack: () => navigate('RedeConveniada') }} route={{ params: screenParams }} />;
      case 'CartaoVirtual':
        return <CartaoVirtualScreen navigation={{ goBack: () => navigate('Home'), navigate }} />;
      case 'BloquearCartao':
        return <BloquearCartaoScreen navigation={{ goBack: () => navigate('Home'), navigate }} />;
      case 'Notifications':
        return <NotificationsScreen navigation={{ navigate, goBack: () => navigate('Home') }} />;
      default:
        return <SplashScreen navigation={{ navigate }} />;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {renderScreen()}
    </View>
  );
}