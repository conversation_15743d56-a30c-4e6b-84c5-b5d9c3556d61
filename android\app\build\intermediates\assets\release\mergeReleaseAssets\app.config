{"name": "<PERSON>c<PERSON><PERSON>", "slug": "TecBizExpoApp", "version": "5.0.9", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "android": {"package": "com.tecbiz.tecbizassociadospush", "googleServicesFile": "./google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/icon-foreground.png", "backgroundColor": "#FFFFFF"}, "minSdkVersion": 26, "compileSdkVersion": 35, "targetSdkVersion": 35, "allowBackup": true, "resizeableActivity": true, "supportsRtl": true, "largeScreenSupport": true, "screenOrientation": "portrait", "manifestPlaceholders": {"android:resizeableActivity": "true", "android:supportsPictureInPicture": "false", "android:maxAspectRatio": "2.4", "android:minAspectRatio": "1.33"}, "permissions": ["NOTIFICATIONS", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "WAKE_LOCK", "com.google.android.c2dm.permission.RECEIVE", "com.google.android.gms.permission.AD_ID"], "navigationBar": {"backgroundColor": "#FFB805", "barStyle": "light-content"}, "statusBar": {"backgroundColor": "#FFB805", "barStyle": "light-content", "translucent": false}}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.tecbiz.tecbizassociadospush", "googleServicesFile": "./GoogleService-Info.plist", "requireFullScreen": false, "buildNumber": "317", "deploymentTarget": "13.0", "scheme": "tecbizapp", "infoPlist": {"NSCameraUsageDescription": "Este app não usa a câmera.", "NSMicrophoneUsageDescription": "Este app não usa o microfone.", "NSLocationWhenInUseUsageDescription": "Este app não usa localização.", "NSPhotoLibraryUsageDescription": "Este app não acessa a galeria de fotos.", "UIBackgroundModes": ["remote-notification"], "ITSAppUsesNonExemptEncryption": false, "CFBundleURLTypes": [{"CFBundleURLName": "com.tecbiz.tecbizassociadospush", "CFBundleURLSchemes": ["tecbizapp"]}], "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": false, "NSExceptionDomains": {"www2.tecbiz.com.br": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSExceptionMinimumTLSVersion": "TLSv1.0", "NSIncludesSubdomains": true}}}}, "statusBar": {"backgroundColor": "#FFB805", "barStyle": "light-content"}}, "statusBar": {"backgroundColor": "#FFB805", "style": "light"}, "web": {"favicon": "./assets/favicon.png"}, "notification": {"icon": "./assets/notification-icon.png", "color": "#000000", "androidMode": "default", "androidCollapsedTitle": "TecBiz"}, "scheme": "tecbizapp", "plugins": [["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}], "expo-secure-store", ["expo-screen-orientation", {"initialOrientation": "PORTRAIT"}], "./plugins/android-compatibility-plugin.js"], "extra": {"eas": {"projectId": "78c39496-1095-4079-8e21-a92c90832c74"}, "expoProjectId": "78c39496-1095-4079-8e21-a92c90832c74"}, "owner": "mauriciocdz07", "sdkVersion": "54.0.0", "platforms": ["ios", "android", "web"]}