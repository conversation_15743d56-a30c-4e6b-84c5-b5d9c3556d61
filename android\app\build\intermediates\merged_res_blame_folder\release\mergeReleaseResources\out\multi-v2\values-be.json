{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,138,209,294,365", "endColumns": "82,70,84,70,66", "endOffsets": "133,204,289,360,427"}, "to": {"startLines": "41,84,85,88,89", "startColumns": "4,4,4,4,4", "startOffsets": "4131,9434,9505,9759,9830", "endColumns": "82,70,84,70,66", "endOffsets": "4209,9500,9585,9825,9892"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "32,33,34,35,36,37,38,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3223,3321,3423,3523,3624,3730,3833,10857", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3316,3418,3518,3619,3725,3828,3949,10953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4214,4321,4483,4608,4718,4873,4999,5114,5364,5526,5633,5796,5924,6077,6236,6305,6367", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "4316,4478,4603,4713,4868,4994,5109,5213,5521,5628,5791,5919,6072,6231,6300,6362,6441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,10378", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,10455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6558,7342,7450,7562", "endColumns": "108,107,111,106", "endOffsets": "6662,7445,7557,7664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5218", "endColumns": "145", "endOffsets": "5359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "136,223", "endColumns": "86,102", "endOffsets": "218,321"}, "to": {"startLines": "105,106", "startColumns": "4,4", "startOffsets": "11236,11323", "endColumns": "86,102", "endOffsets": "11318,11421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,187,280,364,458,561,647,727,816,904,986,1069,1156,1228,1315,1399,1477,1553,1638,1708", "endColumns": "81,92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "182,275,359,453,556,642,722,811,899,981,1064,1151,1223,1310,1394,1472,1548,1633,1703,1826"}, "to": {"startLines": "29,39,40,62,64,65,86,87,90,91,92,93,96,97,98,99,100,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2835,3954,4047,6667,6855,6958,9590,9670,9897,9985,10067,10150,10460,10532,10619,10703,10781,10958,11043,11113", "endColumns": "81,92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "2912,4042,4126,6756,6953,7039,9665,9754,9980,10062,10145,10232,10527,10614,10698,10776,10852,11038,11108,11231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,361,473,567,739,865,981,1111,1262,1401,1530,1657,1808,1907,2081,2209,2351,2503,2630,2771,2874,3023,3131,3285,3395,3551", "endColumns": "175,129,111,93,171,125,115,129,150,138,128,126,150,98,173,127,141,151,126,140,102,148,107,153,109,155,122", "endOffsets": "226,356,468,562,734,860,976,1106,1257,1396,1525,1652,1803,1902,2076,2204,2346,2498,2625,2766,2869,3018,3126,3280,3390,3546,3669"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,94,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2917,3093,6446,6761,7044,7216,7669,7785,7915,8066,8205,8334,8461,8612,8711,8885,9013,9155,9307,10237,11426,11529,11678,11786,11940,12050,12206", "endColumns": "175,129,111,93,171,125,115,129,150,138,128,126,150,98,173,127,141,151,126,140,102,148,107,153,109,155,122", "endOffsets": "3088,3218,6553,6850,7211,7337,7780,7910,8061,8200,8329,8456,8607,8706,8880,9008,9150,9302,9429,10373,11524,11673,11781,11935,12045,12201,12324"}}]}]}