<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#FFFFFF</color>
    <color name="notification_icon_color">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">TecBiz Associado</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="gcm_defaultSenderId" translatable="false">608372388905</string>
    <string name="google_api_key" translatable="false">AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk</string>
    <string name="google_app_id" translatable="false">1:608372388905:android:25e23e2d6867cb314ad838</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk</string>
    <string name="google_storage_bucket" translatable="false">tecbizappass.firebasestorage.app</string>
    <string name="project_id" translatable="false">tecbizappass</string>
    <string name="react_native_dev_server_ip" translatable="false">*************</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="android:enforceNavigationBarContrast" ns1:targetApi="29">true</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/ic_launcher_background</item>
  </style>
</resources>