<?xml version="1.0" encoding="utf-8"?>
<!--
     Copyright (C) 2019 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/date_picker_actions"
  android:layout_width="match_parent"
  android:layout_height="@dimen/mtrl_calendar_action_height"
  android:paddingEnd="@dimen/mtrl_calendar_action_padding"
  android:paddingRight="@dimen/mtrl_calendar_action_padding"
  android:gravity="end"
  android:orientation="horizontal">

  <Button
    android:id="@+id/cancel_button"
    style="?attr/buttonBarNegativeButtonStyle"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:text="@string/mtrl_picker_cancel"/>

  <Button
    android:id="@+id/confirm_button"
    style="?attr/buttonBarPositiveButtonStyle"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:text="@string/mtrl_picker_confirm"/>

</LinearLayout>
