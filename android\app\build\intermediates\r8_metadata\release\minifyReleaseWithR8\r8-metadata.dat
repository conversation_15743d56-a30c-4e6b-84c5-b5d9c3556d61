{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": false, "isRuntimeInvisibleParameterAnnotationsKept": false, "isRuntimeInvisibleTypeAnnotationsKept": false, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": false, "isStackMapTableKept": false}, "isAccessModificationEnabled": true, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": true, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "24", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 113644202700, "numberOfThreads": 12}, "dexFiles": [{"checksum": "b5b05f00c7ebb48ca8767325bfb9e1d041726504283989f59ee3d835f10ee027", "startup": false}, {"checksum": "6fdce1db932b0dc28f77aad26b56fc99cb65c7331427899f579de4641cb5854f", "startup": false}], "stats": {"noObfuscationPercentage": 57.53, "noOptimizationPercentage": 57.81, "noShrinkingPercentage": 56.59}, "featureSplits": {"featureSplits": [{"dexFiles": []}], "isolatedSplits": false}, "resourceOptimization": {"isOptimizedShrinkingEnabled": false}, "version": "8.11.18"}