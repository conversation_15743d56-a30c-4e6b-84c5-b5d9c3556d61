{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,274,358,454,556,641,724,819,906,991,1076,1162,1234,1321,1398,1471,1544,1620,1686", "endColumns": "78,89,83,95,101,84,82,94,86,84,84,85,71,86,76,72,72,75,65,119", "endOffsets": "179,269,353,449,551,636,719,814,901,986,1071,1157,1229,1316,1393,1466,1539,1615,1681,1801"}, "to": {"startLines": "30,40,41,63,65,66,88,89,93,94,97,98,102,103,107,110,112,117,118,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2871,3971,4061,6794,6988,7090,9626,9709,10023,10110,10360,10445,10812,10884,11213,11453,11603,12015,12091,12234", "endColumns": "78,89,83,95,101,84,82,94,86,84,84,85,71,86,76,72,72,75,65,119", "endOffsets": "2945,4056,4140,6885,7085,7170,9704,9799,10105,10190,10440,10526,10879,10966,11285,11521,11671,12086,12152,12349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,333,443,541,687,796,904,1019,1148,1275,1410,1530,1668,1768,1924,2048,2187,2328,2451,2574,2670,2798,2885,3004,3101,3230", "endColumns": "154,122,109,97,145,108,107,114,128,126,134,119,137,99,155,123,138,140,122,122,95,127,86,118,96,128,99", "endOffsets": "205,328,438,536,682,791,899,1014,1143,1270,1405,1525,1663,1763,1919,2043,2182,2323,2446,2569,2665,2793,2880,2999,3096,3225,3325"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,99,125,126,127,128,129,130,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2950,3105,6579,6890,7175,7321,7751,7859,7974,8103,8230,8365,8485,8623,8723,8879,9003,9142,9283,10531,12676,12772,12900,12987,13106,13203,13332", "endColumns": "154,122,109,97,145,108,107,114,128,126,134,119,137,99,155,123,138,140,122,122,95,127,86,118,96,128,99", "endOffsets": "3100,3223,6684,6983,7316,7425,7854,7969,8098,8225,8360,8480,8618,8718,8874,8998,9137,9278,9401,10649,12767,12895,12982,13101,13198,13327,13427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "133,220", "endColumns": "86,90", "endOffsets": "215,306"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "12498,12585", "endColumns": "86,90", "endOffsets": "12580,12671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,204,274,342,424,493,567,643,725,808,885,968,1042,1127,1212,1290,1367,1444,1530,1605,1682,1752", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "121,199,269,337,419,488,562,638,720,803,880,963,1037,1122,1207,1285,1362,1439,1525,1600,1677,1747,1821"}, "to": {"startLines": "29,42,85,86,87,90,91,92,95,96,100,104,105,106,108,109,111,113,114,116,119,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2800,4145,9406,9476,9544,9804,9873,9947,10195,10277,10654,10971,11054,11128,11290,11375,11526,11676,11753,11940,12157,12354,12424", "endColumns": "70,77,69,67,81,68,73,75,81,82,76,82,73,84,84,77,76,76,85,74,76,69,73", "endOffsets": "2866,4218,9471,9539,9621,9868,9942,10018,10272,10355,10726,11049,11123,11208,11370,11448,11598,11748,11834,12010,12229,12419,12493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4223,4329,4502,4632,4738,4910,5042,5162,5415,5600,5709,5884,6019,6190,6365,6432,6496", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "4324,4497,4627,4733,4905,5037,5157,5264,5595,5704,5879,6014,6185,6360,6427,6491,6574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,10731", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,10807"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5269", "endColumns": "145", "endOffsets": "5410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "33,34,35,36,37,38,39,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3228,3323,3425,3522,3632,3738,3856,11839", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3318,3420,3517,3627,3733,3851,3966,11935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6689,7430,7530,7649", "endColumns": "104,99,118,101", "endOffsets": "6789,7525,7644,7746"}}]}]}