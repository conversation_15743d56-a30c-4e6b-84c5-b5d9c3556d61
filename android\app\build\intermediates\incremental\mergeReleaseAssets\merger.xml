<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:barcode-scanning:17.3.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-updates-interface\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-json-utils\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-manifests\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-client\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-constants\android\build\intermediates\assets\release\mergeReleaseAssets"><file name="app.config" path="D:\Projetos\TecBizExpoApp\node_modules\expo-constants\android\build\intermediates\assets\release\mergeReleaseAssets\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu-interface\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\node_modules\expo\android\build\intermediates\assets\release\mergeReleaseAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="D:\Projetos\TecBizExpoApp\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projetos\TecBizExpoApp\android\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>