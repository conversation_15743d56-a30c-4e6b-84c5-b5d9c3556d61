{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6778,7560,7668,7780", "endColumns": "110,107,111,109", "endOffsets": "6884,7663,7775,7885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,11141", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,11220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4357,4466,4631,4766,4877,5044,5179,5298,5545,5714,5826,6001,6139,6296,6462,6532,6591", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "4461,4626,4761,4872,5039,5174,5293,5402,5709,5821,5996,6134,6291,6457,6527,6586,6659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,242,378,492,586,755,873,986,1108,1238,1371,1507,1629,1792,1893,2073,2202,2336,2473,2604,2751,2861,3022,3129,3287,3391,3546", "endColumns": "186,135,113,93,168,117,112,121,129,132,135,121,162,100,179,128,133,136,130,146,109,160,106,157,103,154,125", "endOffsets": "237,373,487,581,750,868,981,1103,1233,1366,1502,1624,1787,1888,2068,2197,2331,2468,2599,2746,2856,3017,3124,3282,3386,3541,3667"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3013,3200,6664,6992,7273,7442,7890,8003,8125,8255,8388,8524,8646,8809,8910,9090,9219,9353,9490,10907,13117,13227,13388,13495,13653,13757,13912", "endColumns": "186,135,113,93,168,117,112,121,129,132,135,121,162,100,179,128,133,136,130,146,109,160,106,157,103,154,125", "endOffsets": "3195,3331,6773,7081,7437,7555,7998,8120,8250,8383,8519,8641,8804,8905,9085,9214,9348,9485,9616,11049,13222,13383,13490,13648,13752,13907,14033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,282,357,445,514,581,661,743,830,910,981,1068,1155,1229,1308,1390,1467,1544,1619,1703,1778,1860,1930", "endColumns": "69,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "120,204,277,352,440,509,576,656,738,825,905,976,1063,1150,1224,1303,1385,1462,1539,1614,1698,1773,1855,1925,2010"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2854,4273,9621,9694,9769,10024,10093,10160,10240,10497,10584,10836,11054,11382,11469,11543,11699,11781,11936,12089,12164,12349,12576,12779,12849", "endColumns": "69,83,72,74,87,68,66,79,81,86,79,70,86,86,73,78,81,76,76,74,83,74,81,69,84", "endOffsets": "2919,4352,9689,9764,9852,10088,10155,10235,10317,10579,10659,10902,11136,11464,11538,11617,11776,11853,12008,12159,12243,12419,12653,12844,12929"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,297,390,493,596,680,756,847,938,1022,1106,1194,1266,1351,1428,1506,1582,1665,1734", "endColumns": "88,102,92,102,102,83,75,90,90,83,83,87,71,84,76,77,75,82,68,120", "endOffsets": "189,292,385,488,591,675,751,842,933,1017,1101,1189,1261,1346,1423,1501,1577,1660,1729,1850"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2924,4077,4180,6889,7086,7189,9857,9933,10322,10413,10664,10748,11225,11297,11622,11858,12013,12424,12507,12658", "endColumns": "88,102,92,102,102,83,75,90,90,83,83,87,71,84,76,77,75,82,68,120", "endOffsets": "3008,4175,4268,6987,7184,7268,9928,10019,10408,10492,10743,10831,11292,11377,11694,11931,12084,12502,12571,12774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-bg\\values-bg.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "143,231", "endColumns": "87,94", "endOffsets": "226,321"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "12934,13022", "endColumns": "87,94", "endOffsets": "13017,13112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3336,3433,3543,3645,3746,3853,3958,12248", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3428,3538,3640,3741,3848,3953,4072,12344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5407", "endColumns": "137", "endOffsets": "5540"}}]}]}