{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,326,438,529,671,785,900,1024,1150,1282,1409,1531,1672,1775,1929,2056,2185,2320,2449,2574,2673,2799,2890,3008,3112,3243", "endColumns": "148,121,111,90,141,113,114,123,125,131,126,121,140,102,153,126,128,134,128,124,98,125,90,117,103,130,100", "endOffsets": "199,321,433,524,666,780,895,1019,1145,1277,1404,1526,1667,1770,1924,2051,2180,2315,2444,2569,2668,2794,2885,3003,3107,3238,3339"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2970,3119,6417,6736,7008,7150,7592,7707,7831,7957,8089,8216,8338,8479,8582,8736,8863,8992,9127,10513,12714,12813,12939,13030,13148,13252,13383", "endColumns": "148,121,111,90,141,113,114,123,125,131,126,121,140,102,153,126,128,134,128,124,98,125,90,117,103,130,100", "endOffsets": "3114,3236,6524,6822,7145,7259,7702,7826,7952,8084,8211,8333,8474,8577,8731,8858,8987,9122,9251,10633,12808,12934,13025,13143,13247,13378,13479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6529,7264,7371,7487", "endColumns": "107,106,115,104", "endOffsets": "6632,7366,7482,7587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4227,4335,4489,4613,4726,4868,4992,5108,5345,5496,5611,5767,5898,6042,6203,6276,6337", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "4330,4484,4608,4721,4863,4987,5103,5201,5491,5606,5762,5893,6037,6198,6271,6332,6412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,279,347,430,499,567,643,722,805,891,960,1040,1129,1209,1292,1377,1456,1533,1613,1705,1778,1857,1929", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "119,201,274,342,425,494,562,638,717,800,886,955,1035,1124,1204,1287,1372,1451,1528,1608,1700,1773,1852,1924,2002"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2821,4145,9256,9329,9397,9656,9725,9793,9869,10114,10197,10444,10638,10957,11046,11126,11284,11369,11532,11690,11770,11963,12184,12381,12453", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "2885,4222,9324,9392,9475,9720,9788,9864,9943,10192,10278,10508,10713,11041,11121,11204,11364,11443,11604,11765,11857,12031,12258,12448,12526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-si\\values-si.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "134,224", "endColumns": "89,92", "endOffsets": "219,312"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "12531,12621", "endColumns": "89,92", "endOffsets": "12616,12709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,10718", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,10795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3241,3343,3446,3551,3656,3755,3859,11862", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3338,3441,3546,3651,3750,3854,3968,11958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,185,274,357,456,555,637,722,813,899,979,1058,1140,1213,1297,1372,1456,1537,1618,1685", "endColumns": "79,88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "180,269,352,451,550,632,717,808,894,974,1053,1135,1208,1292,1367,1451,1532,1613,1680,1798"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2890,3973,4062,6637,6827,6926,9480,9565,9948,10034,10283,10362,10800,10873,11209,11448,11609,12036,12117,12263", "endColumns": "79,88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "2965,4057,4140,6731,6921,7003,9560,9651,10029,10109,10357,10439,10868,10952,11279,11527,11685,12112,12179,12376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5206", "endColumns": "138", "endOffsets": "5340"}}]}]}