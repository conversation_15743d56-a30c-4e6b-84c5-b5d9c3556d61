{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5070", "endColumns": "131", "endOffsets": "5197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "60,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6372,7106,7204,7313", "endColumns": "99,97,108,100", "endOffsets": "6467,7199,7308,7409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,9715", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,9795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,206,325,432,530,672,782,892,1007,1136,1274,1408,1537,1672,1772,1926,2048,2187,2324,2448,2572,2668,2796,2883,3002,3101,3232", "endColumns": "150,118,106,97,141,109,109,114,128,137,133,128,134,99,153,121,138,136,123,123,95,127,86,118,98,130,99", "endOffsets": "201,320,427,525,667,777,887,1002,1131,1269,1403,1532,1667,1767,1921,2043,2182,2319,2443,2567,2663,2791,2878,2997,3096,3227,3327"}, "to": {"startLines": "30,31,59,62,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,89,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2892,3043,6265,6570,6854,6996,7414,7524,7639,7768,7906,8040,8169,8304,8404,8558,8680,8819,8956,9591,10731,10827,10955,11042,11161,11260,11391", "endColumns": "150,118,106,97,141,109,109,114,128,137,133,128,134,99,153,121,138,136,123,123,95,127,86,118,98,130,99", "endOffsets": "3038,3157,6367,6663,6991,7101,7519,7634,7763,7901,8035,8164,8299,8399,8553,8675,8814,8951,9075,9710,10822,10950,11037,11156,11255,11386,11486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "32,33,34,35,36,37,38,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3162,3257,3359,3456,3553,3659,3777,10191", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3252,3354,3451,3548,3654,3772,3887,10287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4064,4171,4335,4461,4567,4722,4849,4964,5202,5368,5473,5637,5763,5918,6062,6126,6186", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4166,4330,4456,4562,4717,4844,4959,5065,5363,5468,5632,5758,5913,6057,6121,6181,6260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,185,275,357,455,555,641,724,815,902,987,1069,1152,1224,1316,1393,1470,1543,1621,1687", "endColumns": "79,89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "180,270,352,450,550,636,719,810,897,982,1064,1147,1219,1311,1388,1465,1538,1616,1682,1801"}, "to": {"startLines": "29,39,40,61,63,64,83,84,85,86,87,88,91,92,93,94,95,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2812,3892,3982,6472,6668,6768,9080,9163,9254,9341,9426,9508,9800,9872,9964,10041,10118,10292,10370,10436", "endColumns": "79,89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "2887,3977,4059,6565,6763,6849,9158,9249,9336,9421,9503,9586,9867,9959,10036,10113,10186,10365,10431,10550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "134,220", "endColumns": "85,89", "endOffsets": "215,305"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "10555,10641", "endColumns": "85,89", "endOffsets": "10636,10726"}}]}]}