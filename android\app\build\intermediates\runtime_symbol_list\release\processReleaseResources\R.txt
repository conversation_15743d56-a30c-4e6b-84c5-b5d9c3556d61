int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim catalyst_fade_in 0x7f010018
int anim catalyst_fade_out 0x7f010019
int anim catalyst_push_up_in 0x7f01001a
int anim catalyst_push_up_out 0x7f01001b
int anim catalyst_slide_down 0x7f01001c
int anim catalyst_slide_up 0x7f01001d
int anim design_bottom_sheet_slide_in 0x7f01001e
int anim design_bottom_sheet_slide_out 0x7f01001f
int anim design_snackbar_in 0x7f010020
int anim design_snackbar_out 0x7f010021
int anim fragment_fast_out_extra_slow_in 0x7f010022
int anim mtrl_bottom_sheet_slide_in 0x7f010023
int anim mtrl_bottom_sheet_slide_out 0x7f010024
int anim mtrl_card_lowers_interpolator 0x7f010025
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator mtrl_btn_state_list_anim 0x7f020009
int animator mtrl_btn_unelevated_state_list_anim 0x7f02000a
int animator mtrl_card_state_list_anim 0x7f02000b
int animator mtrl_chip_state_list_anim 0x7f02000c
int animator mtrl_extended_fab_change_size_motion_spec 0x7f02000d
int animator mtrl_extended_fab_hide_motion_spec 0x7f02000e
int animator mtrl_extended_fab_show_motion_spec 0x7f02000f
int animator mtrl_extended_fab_state_list_animator 0x7f020010
int animator mtrl_fab_hide_motion_spec 0x7f020011
int animator mtrl_fab_show_motion_spec 0x7f020012
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f020013
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f020014
int array assume_strong_biometrics_models 0x7f030000
int array crypto_fingerprint_fallback_prefixes 0x7f030001
int array crypto_fingerprint_fallback_vendors 0x7f030002
int array delay_showing_prompt_models 0x7f030003
int array hide_fingerprint_instantly_prefixes 0x7f030004
int array keyguard_biometric_and_credential_exclude_vendors 0x7f030005
int attr action 0x7f040000
int attr actionBarDivider 0x7f040001
int attr actionBarItemBackground 0x7f040002
int attr actionBarPopupTheme 0x7f040003
int attr actionBarSize 0x7f040004
int attr actionBarSplitStyle 0x7f040005
int attr actionBarStyle 0x7f040006
int attr actionBarTabBarStyle 0x7f040007
int attr actionBarTabStyle 0x7f040008
int attr actionBarTabTextStyle 0x7f040009
int attr actionBarTheme 0x7f04000a
int attr actionBarWidgetTheme 0x7f04000b
int attr actionButtonStyle 0x7f04000c
int attr actionDropDownStyle 0x7f04000d
int attr actionLayout 0x7f04000e
int attr actionMenuTextAppearance 0x7f04000f
int attr actionMenuTextColor 0x7f040010
int attr actionModeBackground 0x7f040011
int attr actionModeCloseButtonStyle 0x7f040012
int attr actionModeCloseContentDescription 0x7f040013
int attr actionModeCloseDrawable 0x7f040014
int attr actionModeCopyDrawable 0x7f040015
int attr actionModeCutDrawable 0x7f040016
int attr actionModeFindDrawable 0x7f040017
int attr actionModePasteDrawable 0x7f040018
int attr actionModePopupWindowStyle 0x7f040019
int attr actionModeSelectAllDrawable 0x7f04001a
int attr actionModeShareDrawable 0x7f04001b
int attr actionModeSplitBackground 0x7f04001c
int attr actionModeStyle 0x7f04001d
int attr actionModeTheme 0x7f04001e
int attr actionModeWebSearchDrawable 0x7f04001f
int attr actionOverflowButtonStyle 0x7f040020
int attr actionOverflowMenuStyle 0x7f040021
int attr actionProviderClass 0x7f040022
int attr actionTextColorAlpha 0x7f040023
int attr actionViewClass 0x7f040024
int attr activityChooserViewStyle 0x7f040025
int attr actualImageResource 0x7f040026
int attr actualImageScaleType 0x7f040027
int attr actualImageUri 0x7f040028
int attr alertDialogButtonGroupStyle 0x7f040029
int attr alertDialogCenterButtons 0x7f04002a
int attr alertDialogStyle 0x7f04002b
int attr alertDialogTheme 0x7f04002c
int attr allowStacking 0x7f04002d
int attr alpha 0x7f04002e
int attr alphabeticModifiers 0x7f04002f
int attr animationMode 0x7f040030
int attr appBarLayoutStyle 0x7f040031
int attr argType 0x7f040032
int attr arrowHeadLength 0x7f040033
int attr arrowShaftLength 0x7f040034
int attr autoCompleteTextViewStyle 0x7f040035
int attr autoSizeMaxTextSize 0x7f040036
int attr autoSizeMinTextSize 0x7f040037
int attr autoSizePresetSizes 0x7f040038
int attr autoSizeStepGranularity 0x7f040039
int attr autoSizeTextType 0x7f04003a
int attr autofillInlineSuggestionChip 0x7f04003b
int attr autofillInlineSuggestionEndIconStyle 0x7f04003c
int attr autofillInlineSuggestionStartIconStyle 0x7f04003d
int attr autofillInlineSuggestionSubtitle 0x7f04003e
int attr autofillInlineSuggestionTitle 0x7f04003f
int attr background 0x7f040040
int attr backgroundColor 0x7f040041
int attr backgroundImage 0x7f040042
int attr backgroundInsetBottom 0x7f040043
int attr backgroundInsetEnd 0x7f040044
int attr backgroundInsetStart 0x7f040045
int attr backgroundInsetTop 0x7f040046
int attr backgroundOverlayColorAlpha 0x7f040047
int attr backgroundSplit 0x7f040048
int attr backgroundStacked 0x7f040049
int attr backgroundTint 0x7f04004a
int attr backgroundTintMode 0x7f04004b
int attr badgeGravity 0x7f04004c
int attr badgeStyle 0x7f04004d
int attr badgeTextColor 0x7f04004e
int attr barLength 0x7f04004f
int attr behavior_autoHide 0x7f040050
int attr behavior_autoShrink 0x7f040051
int attr behavior_draggable 0x7f040052
int attr behavior_expandedOffset 0x7f040053
int attr behavior_fitToContents 0x7f040054
int attr behavior_halfExpandedRatio 0x7f040055
int attr behavior_hideable 0x7f040056
int attr behavior_overlapTop 0x7f040057
int attr behavior_peekHeight 0x7f040058
int attr behavior_saveFlags 0x7f040059
int attr behavior_skipCollapsed 0x7f04005a
int attr borderWidth 0x7f04005b
int attr borderlessButtonStyle 0x7f04005c
int attr bottomAppBarStyle 0x7f04005d
int attr bottomNavigationStyle 0x7f04005e
int attr bottomSheetDialogTheme 0x7f04005f
int attr bottomSheetStyle 0x7f040060
int attr boxBackgroundColor 0x7f040061
int attr boxBackgroundMode 0x7f040062
int attr boxCollapsedPaddingTop 0x7f040063
int attr boxCornerRadiusBottomEnd 0x7f040064
int attr boxCornerRadiusBottomStart 0x7f040065
int attr boxCornerRadiusTopEnd 0x7f040066
int attr boxCornerRadiusTopStart 0x7f040067
int attr boxStrokeColor 0x7f040068
int attr boxStrokeErrorColor 0x7f040069
int attr boxStrokeWidth 0x7f04006a
int attr boxStrokeWidthFocused 0x7f04006b
int attr buttonBarButtonStyle 0x7f04006c
int attr buttonBarNegativeButtonStyle 0x7f04006d
int attr buttonBarNeutralButtonStyle 0x7f04006e
int attr buttonBarPositiveButtonStyle 0x7f04006f
int attr buttonBarStyle 0x7f040070
int attr buttonCompat 0x7f040071
int attr buttonGravity 0x7f040072
int attr buttonIconDimen 0x7f040073
int attr buttonPanelSideLayout 0x7f040074
int attr buttonSize 0x7f040075
int attr buttonStyle 0x7f040076
int attr buttonStyleSmall 0x7f040077
int attr buttonTint 0x7f040078
int attr buttonTintMode 0x7f040079
int attr cardBackgroundColor 0x7f04007a
int attr cardCornerRadius 0x7f04007b
int attr cardElevation 0x7f04007c
int attr cardForegroundColor 0x7f04007d
int attr cardMaxElevation 0x7f04007e
int attr cardPreventCornerOverlap 0x7f04007f
int attr cardUseCompatPadding 0x7f040080
int attr cardViewStyle 0x7f040081
int attr checkMarkCompat 0x7f040082
int attr checkMarkTint 0x7f040083
int attr checkMarkTintMode 0x7f040084
int attr checkboxStyle 0x7f040085
int attr checkedButton 0x7f040086
int attr checkedChip 0x7f040087
int attr checkedIcon 0x7f040088
int attr checkedIconEnabled 0x7f040089
int attr checkedIconTint 0x7f04008a
int attr checkedIconVisible 0x7f04008b
int attr checkedTextViewStyle 0x7f04008c
int attr chipBackgroundColor 0x7f04008d
int attr chipCornerRadius 0x7f04008e
int attr chipEndPadding 0x7f04008f
int attr chipGroupStyle 0x7f040090
int attr chipIcon 0x7f040091
int attr chipIconEnabled 0x7f040092
int attr chipIconSize 0x7f040093
int attr chipIconTint 0x7f040094
int attr chipIconVisible 0x7f040095
int attr chipMinHeight 0x7f040096
int attr chipMinTouchTargetSize 0x7f040097
int attr chipSpacing 0x7f040098
int attr chipSpacingHorizontal 0x7f040099
int attr chipSpacingVertical 0x7f04009a
int attr chipStandaloneStyle 0x7f04009b
int attr chipStartPadding 0x7f04009c
int attr chipStrokeColor 0x7f04009d
int attr chipStrokeWidth 0x7f04009e
int attr chipStyle 0x7f04009f
int attr chipSurfaceColor 0x7f0400a0
int attr circleCrop 0x7f0400a1
int attr closeIcon 0x7f0400a2
int attr closeIconEnabled 0x7f0400a3
int attr closeIconEndPadding 0x7f0400a4
int attr closeIconSize 0x7f0400a5
int attr closeIconStartPadding 0x7f0400a6
int attr closeIconTint 0x7f0400a7
int attr closeIconVisible 0x7f0400a8
int attr closeItemLayout 0x7f0400a9
int attr collapseContentDescription 0x7f0400aa
int attr collapseIcon 0x7f0400ab
int attr collapsedTitleGravity 0x7f0400ac
int attr collapsedTitleTextAppearance 0x7f0400ad
int attr color 0x7f0400ae
int attr colorAccent 0x7f0400af
int attr colorBackgroundFloating 0x7f0400b0
int attr colorButtonNormal 0x7f0400b1
int attr colorControlActivated 0x7f0400b2
int attr colorControlHighlight 0x7f0400b3
int attr colorControlNormal 0x7f0400b4
int attr colorError 0x7f0400b5
int attr colorOnBackground 0x7f0400b6
int attr colorOnError 0x7f0400b7
int attr colorOnPrimary 0x7f0400b8
int attr colorOnPrimarySurface 0x7f0400b9
int attr colorOnSecondary 0x7f0400ba
int attr colorOnSurface 0x7f0400bb
int attr colorPrimary 0x7f0400bc
int attr colorPrimaryDark 0x7f0400bd
int attr colorPrimarySurface 0x7f0400be
int attr colorPrimaryVariant 0x7f0400bf
int attr colorScheme 0x7f0400c0
int attr colorSecondary 0x7f0400c1
int attr colorSecondaryVariant 0x7f0400c2
int attr colorSurface 0x7f0400c3
int attr colorSwitchThumbNormal 0x7f0400c4
int attr commitIcon 0x7f0400c5
int attr contentDescription 0x7f0400c6
int attr contentInsetEnd 0x7f0400c7
int attr contentInsetEndWithActions 0x7f0400c8
int attr contentInsetLeft 0x7f0400c9
int attr contentInsetRight 0x7f0400ca
int attr contentInsetStart 0x7f0400cb
int attr contentInsetStartWithNavigation 0x7f0400cc
int attr contentPadding 0x7f0400cd
int attr contentPaddingBottom 0x7f0400ce
int attr contentPaddingLeft 0x7f0400cf
int attr contentPaddingRight 0x7f0400d0
int attr contentPaddingTop 0x7f0400d1
int attr contentScrim 0x7f0400d2
int attr controlBackground 0x7f0400d3
int attr coordinatorLayoutStyle 0x7f0400d4
int attr cornerFamily 0x7f0400d5
int attr cornerFamilyBottomLeft 0x7f0400d6
int attr cornerFamilyBottomRight 0x7f0400d7
int attr cornerFamilyTopLeft 0x7f0400d8
int attr cornerFamilyTopRight 0x7f0400d9
int attr cornerRadius 0x7f0400da
int attr cornerSize 0x7f0400db
int attr cornerSizeBottomLeft 0x7f0400dc
int attr cornerSizeBottomRight 0x7f0400dd
int attr cornerSizeTopLeft 0x7f0400de
int attr cornerSizeTopRight 0x7f0400df
int attr counterEnabled 0x7f0400e0
int attr counterMaxLength 0x7f0400e1
int attr counterOverflowTextAppearance 0x7f0400e2
int attr counterOverflowTextColor 0x7f0400e3
int attr counterTextAppearance 0x7f0400e4
int attr counterTextColor 0x7f0400e5
int attr customNavigationLayout 0x7f0400e6
int attr data 0x7f0400e7
int attr dataPattern 0x7f0400e8
int attr dayInvalidStyle 0x7f0400e9
int attr daySelectedStyle 0x7f0400ea
int attr dayStyle 0x7f0400eb
int attr dayTodayStyle 0x7f0400ec
int attr defaultQueryHint 0x7f0400ed
int attr destination 0x7f0400ee
int attr dialogCornerRadius 0x7f0400ef
int attr dialogPreferredPadding 0x7f0400f0
int attr dialogTheme 0x7f0400f1
int attr displayOptions 0x7f0400f2
int attr divider 0x7f0400f3
int attr dividerHorizontal 0x7f0400f4
int attr dividerPadding 0x7f0400f5
int attr dividerVertical 0x7f0400f6
int attr drawableBottomCompat 0x7f0400f7
int attr drawableEndCompat 0x7f0400f8
int attr drawableLeftCompat 0x7f0400f9
int attr drawableRightCompat 0x7f0400fa
int attr drawableSize 0x7f0400fb
int attr drawableStartCompat 0x7f0400fc
int attr drawableTint 0x7f0400fd
int attr drawableTintMode 0x7f0400fe
int attr drawableTopCompat 0x7f0400ff
int attr drawerArrowStyle 0x7f040100
int attr dropDownListViewStyle 0x7f040101
int attr dropdownListPreferredItemHeight 0x7f040102
int attr editTextBackground 0x7f040103
int attr editTextColor 0x7f040104
int attr editTextStyle 0x7f040105
int attr elevation 0x7f040106
int attr elevationOverlayColor 0x7f040107
int attr elevationOverlayEnabled 0x7f040108
int attr emojiCompatEnabled 0x7f040109
int attr endIconCheckable 0x7f04010a
int attr endIconContentDescription 0x7f04010b
int attr endIconDrawable 0x7f04010c
int attr endIconMode 0x7f04010d
int attr endIconTint 0x7f04010e
int attr endIconTintMode 0x7f04010f
int attr enforceMaterialTheme 0x7f040110
int attr enforceTextAppearance 0x7f040111
int attr ensureMinTouchTargetSize 0x7f040112
int attr enterAnim 0x7f040113
int attr errorContentDescription 0x7f040114
int attr errorEnabled 0x7f040115
int attr errorIconDrawable 0x7f040116
int attr errorIconTint 0x7f040117
int attr errorIconTintMode 0x7f040118
int attr errorTextAppearance 0x7f040119
int attr errorTextColor 0x7f04011a
int attr exitAnim 0x7f04011b
int attr expandActivityOverflowButtonDrawable 0x7f04011c
int attr expanded 0x7f04011d
int attr expandedTitleGravity 0x7f04011e
int attr expandedTitleMargin 0x7f04011f
int attr expandedTitleMarginBottom 0x7f040120
int attr expandedTitleMarginEnd 0x7f040121
int attr expandedTitleMarginStart 0x7f040122
int attr expandedTitleMarginTop 0x7f040123
int attr expandedTitleTextAppearance 0x7f040124
int attr extendMotionSpec 0x7f040125
int attr extendedFloatingActionButtonStyle 0x7f040126
int attr fabAlignmentMode 0x7f040127
int attr fabAnimationMode 0x7f040128
int attr fabCradleMargin 0x7f040129
int attr fabCradleRoundedCornerRadius 0x7f04012a
int attr fabCradleVerticalOffset 0x7f04012b
int attr fabCustomSize 0x7f04012c
int attr fabSize 0x7f04012d
int attr fadeDuration 0x7f04012e
int attr failureImage 0x7f04012f
int attr failureImageScaleType 0x7f040130
int attr fastScrollEnabled 0x7f040131
int attr fastScrollHorizontalThumbDrawable 0x7f040132
int attr fastScrollHorizontalTrackDrawable 0x7f040133
int attr fastScrollVerticalThumbDrawable 0x7f040134
int attr fastScrollVerticalTrackDrawable 0x7f040135
int attr firstBaselineToTopHeight 0x7f040136
int attr floatingActionButtonStyle 0x7f040137
int attr font 0x7f040138
int attr fontFamily 0x7f040139
int attr fontProviderAuthority 0x7f04013a
int attr fontProviderCerts 0x7f04013b
int attr fontProviderFallbackQuery 0x7f04013c
int attr fontProviderFetchStrategy 0x7f04013d
int attr fontProviderFetchTimeout 0x7f04013e
int attr fontProviderPackage 0x7f04013f
int attr fontProviderQuery 0x7f040140
int attr fontProviderSystemFontFamily 0x7f040141
int attr fontStyle 0x7f040142
int attr fontVariationSettings 0x7f040143
int attr fontWeight 0x7f040144
int attr foregroundInsidePadding 0x7f040145
int attr gapBetweenBars 0x7f040146
int attr gestureInsetBottomIgnored 0x7f040147
int attr goIcon 0x7f040148
int attr graph 0x7f040149
int attr haloColor 0x7f04014a
int attr haloRadius 0x7f04014b
int attr headerLayout 0x7f04014c
int attr height 0x7f04014d
int attr helperText 0x7f04014e
int attr helperTextEnabled 0x7f04014f
int attr helperTextTextAppearance 0x7f040150
int attr helperTextTextColor 0x7f040151
int attr hideMotionSpec 0x7f040152
int attr hideOnContentScroll 0x7f040153
int attr hideOnScroll 0x7f040154
int attr hintAnimationEnabled 0x7f040155
int attr hintEnabled 0x7f040156
int attr hintTextAppearance 0x7f040157
int attr hintTextColor 0x7f040158
int attr homeAsUpIndicator 0x7f040159
int attr homeLayout 0x7f04015a
int attr horizontalOffset 0x7f04015b
int attr hoveredFocusedTranslationZ 0x7f04015c
int attr icon 0x7f04015d
int attr iconEndPadding 0x7f04015e
int attr iconGravity 0x7f04015f
int attr iconPadding 0x7f040160
int attr iconSize 0x7f040161
int attr iconStartPadding 0x7f040162
int attr iconTint 0x7f040163
int attr iconTintMode 0x7f040164
int attr iconifiedByDefault 0x7f040165
int attr imageAspectRatio 0x7f040166
int attr imageAspectRatioAdjust 0x7f040167
int attr imageButtonStyle 0x7f040168
int attr indeterminateProgressStyle 0x7f040169
int attr initialActivityCount 0x7f04016a
int attr insetForeground 0x7f04016b
int attr isAutofillInlineSuggestionTheme 0x7f04016c
int attr isLightTheme 0x7f04016d
int attr isMaterialTheme 0x7f04016e
int attr itemBackground 0x7f04016f
int attr itemFillColor 0x7f040170
int attr itemHorizontalPadding 0x7f040171
int attr itemHorizontalTranslationEnabled 0x7f040172
int attr itemIconPadding 0x7f040173
int attr itemIconSize 0x7f040174
int attr itemIconTint 0x7f040175
int attr itemMaxLines 0x7f040176
int attr itemPadding 0x7f040177
int attr itemRippleColor 0x7f040178
int attr itemShapeAppearance 0x7f040179
int attr itemShapeAppearanceOverlay 0x7f04017a
int attr itemShapeFillColor 0x7f04017b
int attr itemShapeInsetBottom 0x7f04017c
int attr itemShapeInsetEnd 0x7f04017d
int attr itemShapeInsetStart 0x7f04017e
int attr itemShapeInsetTop 0x7f04017f
int attr itemSpacing 0x7f040180
int attr itemStrokeColor 0x7f040181
int attr itemStrokeWidth 0x7f040182
int attr itemTextAppearance 0x7f040183
int attr itemTextAppearanceActive 0x7f040184
int attr itemTextAppearanceInactive 0x7f040185
int attr itemTextColor 0x7f040186
int attr keylines 0x7f040187
int attr lStar 0x7f040188
int attr labelBehavior 0x7f040189
int attr labelStyle 0x7f04018a
int attr labelVisibilityMode 0x7f04018b
int attr lastBaselineToBottomHeight 0x7f04018c
int attr launchSingleTop 0x7f04018d
int attr layout 0x7f04018e
int attr layoutManager 0x7f04018f
int attr layout_anchor 0x7f040190
int attr layout_anchorGravity 0x7f040191
int attr layout_behavior 0x7f040192
int attr layout_collapseMode 0x7f040193
int attr layout_collapseParallaxMultiplier 0x7f040194
int attr layout_dodgeInsetEdges 0x7f040195
int attr layout_insetEdge 0x7f040196
int attr layout_keyline 0x7f040197
int attr layout_scrollFlags 0x7f040198
int attr layout_scrollInterpolator 0x7f040199
int attr liftOnScroll 0x7f04019a
int attr liftOnScrollTargetViewId 0x7f04019b
int attr lineHeight 0x7f04019c
int attr lineSpacing 0x7f04019d
int attr listChoiceBackgroundIndicator 0x7f04019e
int attr listChoiceIndicatorMultipleAnimated 0x7f04019f
int attr listChoiceIndicatorSingleAnimated 0x7f0401a0
int attr listDividerAlertDialog 0x7f0401a1
int attr listItemLayout 0x7f0401a2
int attr listLayout 0x7f0401a3
int attr listMenuViewStyle 0x7f0401a4
int attr listPopupWindowStyle 0x7f0401a5
int attr listPreferredItemHeight 0x7f0401a6
int attr listPreferredItemHeightLarge 0x7f0401a7
int attr listPreferredItemHeightSmall 0x7f0401a8
int attr listPreferredItemPaddingEnd 0x7f0401a9
int attr listPreferredItemPaddingLeft 0x7f0401aa
int attr listPreferredItemPaddingRight 0x7f0401ab
int attr listPreferredItemPaddingStart 0x7f0401ac
int attr logo 0x7f0401ad
int attr logoDescription 0x7f0401ae
int attr materialAlertDialogBodyTextStyle 0x7f0401af
int attr materialAlertDialogTheme 0x7f0401b0
int attr materialAlertDialogTitleIconStyle 0x7f0401b1
int attr materialAlertDialogTitlePanelStyle 0x7f0401b2
int attr materialAlertDialogTitleTextStyle 0x7f0401b3
int attr materialButtonOutlinedStyle 0x7f0401b4
int attr materialButtonStyle 0x7f0401b5
int attr materialButtonToggleGroupStyle 0x7f0401b6
int attr materialCalendarDay 0x7f0401b7
int attr materialCalendarFullscreenTheme 0x7f0401b8
int attr materialCalendarHeaderConfirmButton 0x7f0401b9
int attr materialCalendarHeaderDivider 0x7f0401ba
int attr materialCalendarHeaderLayout 0x7f0401bb
int attr materialCalendarHeaderSelection 0x7f0401bc
int attr materialCalendarHeaderTitle 0x7f0401bd
int attr materialCalendarHeaderToggleButton 0x7f0401be
int attr materialCalendarStyle 0x7f0401bf
int attr materialCalendarTheme 0x7f0401c0
int attr materialCardViewStyle 0x7f0401c1
int attr materialThemeOverlay 0x7f0401c2
int attr maxActionInlineWidth 0x7f0401c3
int attr maxButtonHeight 0x7f0401c4
int attr maxCharacterCount 0x7f0401c5
int attr maxImageSize 0x7f0401c6
int attr maxLines 0x7f0401c7
int attr measureWithLargestChild 0x7f0401c8
int attr menu 0x7f0401c9
int attr mimeType 0x7f0401ca
int attr minTouchTargetSize 0x7f0401cb
int attr multiChoiceItemLayout 0x7f0401cc
int attr navGraph 0x7f0401cd
int attr navigationContentDescription 0x7f0401ce
int attr navigationIcon 0x7f0401cf
int attr navigationMode 0x7f0401d0
int attr navigationViewStyle 0x7f0401d1
int attr nestedScrollViewStyle 0x7f0401d2
int attr nullable 0x7f0401d3
int attr number 0x7f0401d4
int attr numericModifiers 0x7f0401d5
int attr overlapAnchor 0x7f0401d6
int attr overlayImage 0x7f0401d7
int attr paddingBottomNoButtons 0x7f0401d8
int attr paddingBottomSystemWindowInsets 0x7f0401d9
int attr paddingEnd 0x7f0401da
int attr paddingLeftSystemWindowInsets 0x7f0401db
int attr paddingRightSystemWindowInsets 0x7f0401dc
int attr paddingStart 0x7f0401dd
int attr paddingTopNoTitle 0x7f0401de
int attr panelBackground 0x7f0401df
int attr panelMenuListTheme 0x7f0401e0
int attr panelMenuListWidth 0x7f0401e1
int attr passwordToggleContentDescription 0x7f0401e2
int attr passwordToggleDrawable 0x7f0401e3
int attr passwordToggleEnabled 0x7f0401e4
int attr passwordToggleTint 0x7f0401e5
int attr passwordToggleTintMode 0x7f0401e6
int attr placeholderImage 0x7f0401e7
int attr placeholderImageScaleType 0x7f0401e8
int attr placeholderText 0x7f0401e9
int attr placeholderTextAppearance 0x7f0401ea
int attr placeholderTextColor 0x7f0401eb
int attr popEnterAnim 0x7f0401ec
int attr popExitAnim 0x7f0401ed
int attr popUpTo 0x7f0401ee
int attr popUpToInclusive 0x7f0401ef
int attr popUpToSaveState 0x7f0401f0
int attr popupMenuBackground 0x7f0401f1
int attr popupMenuStyle 0x7f0401f2
int attr popupTheme 0x7f0401f3
int attr popupWindowStyle 0x7f0401f4
int attr prefixText 0x7f0401f5
int attr prefixTextAppearance 0x7f0401f6
int attr prefixTextColor 0x7f0401f7
int attr preserveIconSpacing 0x7f0401f8
int attr pressedStateOverlayImage 0x7f0401f9
int attr pressedTranslationZ 0x7f0401fa
int attr progressBarAutoRotateInterval 0x7f0401fb
int attr progressBarImage 0x7f0401fc
int attr progressBarImageScaleType 0x7f0401fd
int attr progressBarPadding 0x7f0401fe
int attr progressBarStyle 0x7f0401ff
int attr queryBackground 0x7f040200
int attr queryHint 0x7f040201
int attr queryPatterns 0x7f040202
int attr radioButtonStyle 0x7f040203
int attr rangeFillColor 0x7f040204
int attr ratingBarStyle 0x7f040205
int attr ratingBarStyleIndicator 0x7f040206
int attr ratingBarStyleSmall 0x7f040207
int attr recyclerViewStyle 0x7f040208
int attr restoreState 0x7f040209
int attr retryImage 0x7f04020a
int attr retryImageScaleType 0x7f04020b
int attr reverseLayout 0x7f04020c
int attr rippleColor 0x7f04020d
int attr roundAsCircle 0x7f04020e
int attr roundBottomEnd 0x7f04020f
int attr roundBottomLeft 0x7f040210
int attr roundBottomRight 0x7f040211
int attr roundBottomStart 0x7f040212
int attr roundTopEnd 0x7f040213
int attr roundTopLeft 0x7f040214
int attr roundTopRight 0x7f040215
int attr roundTopStart 0x7f040216
int attr roundWithOverlayColor 0x7f040217
int attr roundedCornerRadius 0x7f040218
int attr roundingBorderColor 0x7f040219
int attr roundingBorderPadding 0x7f04021a
int attr roundingBorderWidth 0x7f04021b
int attr route 0x7f04021c
int attr scopeUris 0x7f04021d
int attr scrimAnimationDuration 0x7f04021e
int attr scrimBackground 0x7f04021f
int attr scrimVisibleHeightTrigger 0x7f040220
int attr searchHintIcon 0x7f040221
int attr searchIcon 0x7f040222
int attr searchViewStyle 0x7f040223
int attr seekBarStyle 0x7f040224
int attr selectableItemBackground 0x7f040225
int attr selectableItemBackgroundBorderless 0x7f040226
int attr selectionRequired 0x7f040227
int attr shapeAppearance 0x7f040228
int attr shapeAppearanceLargeComponent 0x7f040229
int attr shapeAppearanceMediumComponent 0x7f04022a
int attr shapeAppearanceOverlay 0x7f04022b
int attr shapeAppearanceSmallComponent 0x7f04022c
int attr shortcutMatchRequired 0x7f04022d
int attr showAsAction 0x7f04022e
int attr showDividers 0x7f04022f
int attr showMotionSpec 0x7f040230
int attr showText 0x7f040231
int attr showTitle 0x7f040232
int attr shrinkMotionSpec 0x7f040233
int attr singleChoiceItemLayout 0x7f040234
int attr singleLine 0x7f040235
int attr singleSelection 0x7f040236
int attr sliderStyle 0x7f040237
int attr snackbarButtonStyle 0x7f040238
int attr snackbarStyle 0x7f040239
int attr snackbarTextViewStyle 0x7f04023a
int attr spanCount 0x7f04023b
int attr spinBars 0x7f04023c
int attr spinnerDropDownItemStyle 0x7f04023d
int attr spinnerStyle 0x7f04023e
int attr splitTrack 0x7f04023f
int attr srcCompat 0x7f040240
int attr stackFromEnd 0x7f040241
int attr startDestination 0x7f040242
int attr startIconCheckable 0x7f040243
int attr startIconContentDescription 0x7f040244
int attr startIconDrawable 0x7f040245
int attr startIconTint 0x7f040246
int attr startIconTintMode 0x7f040247
int attr state_above_anchor 0x7f040248
int attr state_collapsed 0x7f040249
int attr state_collapsible 0x7f04024a
int attr state_dragged 0x7f04024b
int attr state_liftable 0x7f04024c
int attr state_lifted 0x7f04024d
int attr statusBarBackground 0x7f04024e
int attr statusBarForeground 0x7f04024f
int attr statusBarScrim 0x7f040250
int attr strokeColor 0x7f040251
int attr strokeWidth 0x7f040252
int attr subMenuArrow 0x7f040253
int attr submitBackground 0x7f040254
int attr subtitle 0x7f040255
int attr subtitleTextAppearance 0x7f040256
int attr subtitleTextColor 0x7f040257
int attr subtitleTextStyle 0x7f040258
int attr suffixText 0x7f040259
int attr suffixTextAppearance 0x7f04025a
int attr suffixTextColor 0x7f04025b
int attr suggestionRowLayout 0x7f04025c
int attr swipeRefreshLayoutProgressSpinnerBackgroundColor 0x7f04025d
int attr switchMinWidth 0x7f04025e
int attr switchPadding 0x7f04025f
int attr switchStyle 0x7f040260
int attr switchTextAppearance 0x7f040261
int attr tabBackground 0x7f040262
int attr tabContentStart 0x7f040263
int attr tabGravity 0x7f040264
int attr tabIconTint 0x7f040265
int attr tabIconTintMode 0x7f040266
int attr tabIndicator 0x7f040267
int attr tabIndicatorAnimationDuration 0x7f040268
int attr tabIndicatorColor 0x7f040269
int attr tabIndicatorFullWidth 0x7f04026a
int attr tabIndicatorGravity 0x7f04026b
int attr tabIndicatorHeight 0x7f04026c
int attr tabInlineLabel 0x7f04026d
int attr tabMaxWidth 0x7f04026e
int attr tabMinWidth 0x7f04026f
int attr tabMode 0x7f040270
int attr tabPadding 0x7f040271
int attr tabPaddingBottom 0x7f040272
int attr tabPaddingEnd 0x7f040273
int attr tabPaddingStart 0x7f040274
int attr tabPaddingTop 0x7f040275
int attr tabRippleColor 0x7f040276
int attr tabSelectedTextColor 0x7f040277
int attr tabStyle 0x7f040278
int attr tabTextAppearance 0x7f040279
int attr tabTextColor 0x7f04027a
int attr tabUnboundedRipple 0x7f04027b
int attr targetPackage 0x7f04027c
int attr textAllCaps 0x7f04027d
int attr textAppearanceBody1 0x7f04027e
int attr textAppearanceBody2 0x7f04027f
int attr textAppearanceButton 0x7f040280
int attr textAppearanceCaption 0x7f040281
int attr textAppearanceHeadline1 0x7f040282
int attr textAppearanceHeadline2 0x7f040283
int attr textAppearanceHeadline3 0x7f040284
int attr textAppearanceHeadline4 0x7f040285
int attr textAppearanceHeadline5 0x7f040286
int attr textAppearanceHeadline6 0x7f040287
int attr textAppearanceLargePopupMenu 0x7f040288
int attr textAppearanceLineHeightEnabled 0x7f040289
int attr textAppearanceListItem 0x7f04028a
int attr textAppearanceListItemSecondary 0x7f04028b
int attr textAppearanceListItemSmall 0x7f04028c
int attr textAppearanceOverline 0x7f04028d
int attr textAppearancePopupMenuHeader 0x7f04028e
int attr textAppearanceSearchResultSubtitle 0x7f04028f
int attr textAppearanceSearchResultTitle 0x7f040290
int attr textAppearanceSmallPopupMenu 0x7f040291
int attr textAppearanceSubtitle1 0x7f040292
int attr textAppearanceSubtitle2 0x7f040293
int attr textColorAlertDialogListItem 0x7f040294
int attr textColorSearchUrl 0x7f040295
int attr textEndPadding 0x7f040296
int attr textInputLayoutFocusedRectEnabled 0x7f040297
int attr textInputStyle 0x7f040298
int attr textLocale 0x7f040299
int attr textStartPadding 0x7f04029a
int attr theme 0x7f04029b
int attr themeLineHeight 0x7f04029c
int attr thickness 0x7f04029d
int attr thumbColor 0x7f04029e
int attr thumbElevation 0x7f04029f
int attr thumbRadius 0x7f0402a0
int attr thumbTextPadding 0x7f0402a1
int attr thumbTint 0x7f0402a2
int attr thumbTintMode 0x7f0402a3
int attr tickColor 0x7f0402a4
int attr tickColorActive 0x7f0402a5
int attr tickColorInactive 0x7f0402a6
int attr tickMark 0x7f0402a7
int attr tickMarkTint 0x7f0402a8
int attr tickMarkTintMode 0x7f0402a9
int attr tint 0x7f0402aa
int attr tintMode 0x7f0402ab
int attr title 0x7f0402ac
int attr titleEnabled 0x7f0402ad
int attr titleMargin 0x7f0402ae
int attr titleMarginBottom 0x7f0402af
int attr titleMarginEnd 0x7f0402b0
int attr titleMarginStart 0x7f0402b1
int attr titleMarginTop 0x7f0402b2
int attr titleMargins 0x7f0402b3
int attr titleTextAppearance 0x7f0402b4
int attr titleTextColor 0x7f0402b5
int attr titleTextStyle 0x7f0402b6
int attr toolbarId 0x7f0402b7
int attr toolbarNavigationButtonStyle 0x7f0402b8
int attr toolbarStyle 0x7f0402b9
int attr tooltipForegroundColor 0x7f0402ba
int attr tooltipFrameBackground 0x7f0402bb
int attr tooltipStyle 0x7f0402bc
int attr tooltipText 0x7f0402bd
int attr track 0x7f0402be
int attr trackColor 0x7f0402bf
int attr trackColorActive 0x7f0402c0
int attr trackColorInactive 0x7f0402c1
int attr trackHeight 0x7f0402c2
int attr trackTint 0x7f0402c3
int attr trackTintMode 0x7f0402c4
int attr transitionShapeAppearance 0x7f0402c5
int attr ttcIndex 0x7f0402c6
int attr uri 0x7f0402c7
int attr useCompatPadding 0x7f0402c8
int attr useMaterialThemeColors 0x7f0402c9
int attr values 0x7f0402ca
int attr verticalOffset 0x7f0402cb
int attr viewAspectRatio 0x7f0402cc
int attr viewInflaterClass 0x7f0402cd
int attr voiceIcon 0x7f0402ce
int attr windowActionBar 0x7f0402cf
int attr windowActionBarOverlay 0x7f0402d0
int attr windowActionModeOverlay 0x7f0402d1
int attr windowFixedHeightMajor 0x7f0402d2
int attr windowFixedHeightMinor 0x7f0402d3
int attr windowFixedWidthMajor 0x7f0402d4
int attr windowFixedWidthMinor 0x7f0402d5
int attr windowMinWidthMajor 0x7f0402d6
int attr windowMinWidthMinor 0x7f0402d7
int attr windowNoTitle 0x7f0402d8
int attr yearSelectedStyle 0x7f0402d9
int attr yearStyle 0x7f0402da
int attr yearTodayStyle 0x7f0402db
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_config_actionMenuItemAllCaps 0x7f050001
int bool mtrl_btn_textappearance_all_caps 0x7f050002
int color abc_background_cache_hint_selector_material_dark 0x7f060000
int color abc_background_cache_hint_selector_material_light 0x7f060001
int color abc_btn_colored_borderless_text_material 0x7f060002
int color abc_btn_colored_text_material 0x7f060003
int color abc_color_highlight_material 0x7f060004
int color abc_decor_view_status_guard 0x7f060005
int color abc_decor_view_status_guard_light 0x7f060006
int color abc_hint_foreground_material_dark 0x7f060007
int color abc_hint_foreground_material_light 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color androidx_core_ripple_material_light 0x7f06001b
int color androidx_core_secondary_text_default_material_light 0x7f06001c
int color background_floating_material_dark 0x7f06001d
int color background_floating_material_light 0x7f06001e
int color background_material_dark 0x7f06001f
int color background_material_light 0x7f060020
int color biometric_error_color 0x7f060021
int color bright_foreground_disabled_material_dark 0x7f060022
int color bright_foreground_disabled_material_light 0x7f060023
int color bright_foreground_inverse_material_dark 0x7f060024
int color bright_foreground_inverse_material_light 0x7f060025
int color bright_foreground_material_dark 0x7f060026
int color bright_foreground_material_light 0x7f060027
int color browser_actions_bg_grey 0x7f060028
int color browser_actions_divider_color 0x7f060029
int color browser_actions_text_color 0x7f06002a
int color browser_actions_title_color 0x7f06002b
int color button_material_dark 0x7f06002c
int color button_material_light 0x7f06002d
int color call_notification_answer_color 0x7f06002e
int color call_notification_decline_color 0x7f06002f
int color cardview_dark_background 0x7f060030
int color cardview_light_background 0x7f060031
int color cardview_shadow_end_color 0x7f060032
int color cardview_shadow_start_color 0x7f060033
int color catalyst_logbox_background 0x7f060034
int color catalyst_redbox_background 0x7f060035
int color checkbox_themeable_attribute_color 0x7f060036
int color colorPrimary 0x7f060037
int color colorPrimaryDark 0x7f060038
int color common_google_signin_btn_text_dark 0x7f060039
int color common_google_signin_btn_text_dark_default 0x7f06003a
int color common_google_signin_btn_text_dark_disabled 0x7f06003b
int color common_google_signin_btn_text_dark_focused 0x7f06003c
int color common_google_signin_btn_text_dark_pressed 0x7f06003d
int color common_google_signin_btn_text_light 0x7f06003e
int color common_google_signin_btn_text_light_default 0x7f06003f
int color common_google_signin_btn_text_light_disabled 0x7f060040
int color common_google_signin_btn_text_light_focused 0x7f060041
int color common_google_signin_btn_text_light_pressed 0x7f060042
int color common_google_signin_btn_tint 0x7f060043
int color design_bottom_navigation_shadow_color 0x7f060044
int color design_box_stroke_color 0x7f060045
int color design_dark_default_color_background 0x7f060046
int color design_dark_default_color_error 0x7f060047
int color design_dark_default_color_on_background 0x7f060048
int color design_dark_default_color_on_error 0x7f060049
int color design_dark_default_color_on_primary 0x7f06004a
int color design_dark_default_color_on_secondary 0x7f06004b
int color design_dark_default_color_on_surface 0x7f06004c
int color design_dark_default_color_primary 0x7f06004d
int color design_dark_default_color_primary_dark 0x7f06004e
int color design_dark_default_color_primary_variant 0x7f06004f
int color design_dark_default_color_secondary 0x7f060050
int color design_dark_default_color_secondary_variant 0x7f060051
int color design_dark_default_color_surface 0x7f060052
int color design_default_color_background 0x7f060053
int color design_default_color_error 0x7f060054
int color design_default_color_on_background 0x7f060055
int color design_default_color_on_error 0x7f060056
int color design_default_color_on_primary 0x7f060057
int color design_default_color_on_secondary 0x7f060058
int color design_default_color_on_surface 0x7f060059
int color design_default_color_primary 0x7f06005a
int color design_default_color_primary_dark 0x7f06005b
int color design_default_color_primary_variant 0x7f06005c
int color design_default_color_secondary 0x7f06005d
int color design_default_color_secondary_variant 0x7f06005e
int color design_default_color_surface 0x7f06005f
int color design_error 0x7f060060
int color design_fab_shadow_end_color 0x7f060061
int color design_fab_shadow_mid_color 0x7f060062
int color design_fab_shadow_start_color 0x7f060063
int color design_fab_stroke_end_inner_color 0x7f060064
int color design_fab_stroke_end_outer_color 0x7f060065
int color design_fab_stroke_top_inner_color 0x7f060066
int color design_fab_stroke_top_outer_color 0x7f060067
int color design_icon_tint 0x7f060068
int color design_snackbar_background_color 0x7f060069
int color dev_launcher_backgroundColor 0x7f06006a
int color dev_launcher_colorAccentDark 0x7f06006b
int color dev_launcher_colorPrimaryDark 0x7f06006c
int color dev_launcher_primary 0x7f06006d
int color dev_launcher_secondaryBackgroundColor 0x7f06006e
int color dev_launcher_white 0x7f06006f
int color dim_foreground_disabled_material_dark 0x7f060070
int color dim_foreground_disabled_material_light 0x7f060071
int color dim_foreground_material_dark 0x7f060072
int color dim_foreground_material_light 0x7f060073
int color error_color_material_dark 0x7f060074
int color error_color_material_light 0x7f060075
int color foreground_material_dark 0x7f060076
int color foreground_material_light 0x7f060077
int color highlighted_text_material_dark 0x7f060078
int color highlighted_text_material_light 0x7f060079
int color iconBackground 0x7f06007a
int color material_blue_grey_800 0x7f06007b
int color material_blue_grey_900 0x7f06007c
int color material_blue_grey_950 0x7f06007d
int color material_deep_teal_200 0x7f06007e
int color material_deep_teal_500 0x7f06007f
int color material_grey_100 0x7f060080
int color material_grey_300 0x7f060081
int color material_grey_50 0x7f060082
int color material_grey_600 0x7f060083
int color material_grey_800 0x7f060084
int color material_grey_850 0x7f060085
int color material_grey_900 0x7f060086
int color material_on_background_disabled 0x7f060087
int color material_on_background_emphasis_high_type 0x7f060088
int color material_on_background_emphasis_medium 0x7f060089
int color material_on_primary_disabled 0x7f06008a
int color material_on_primary_emphasis_high_type 0x7f06008b
int color material_on_primary_emphasis_medium 0x7f06008c
int color material_on_surface_disabled 0x7f06008d
int color material_on_surface_emphasis_high_type 0x7f06008e
int color material_on_surface_emphasis_medium 0x7f06008f
int color material_on_surface_stroke 0x7f060090
int color material_slider_active_tick_marks_color 0x7f060091
int color material_slider_active_track_color 0x7f060092
int color material_slider_halo_color 0x7f060093
int color material_slider_inactive_tick_marks_color 0x7f060094
int color material_slider_inactive_track_color 0x7f060095
int color material_slider_thumb_color 0x7f060096
int color mtrl_bottom_nav_colored_item_tint 0x7f060097
int color mtrl_bottom_nav_colored_ripple_color 0x7f060098
int color mtrl_bottom_nav_item_tint 0x7f060099
int color mtrl_bottom_nav_ripple_color 0x7f06009a
int color mtrl_btn_bg_color_selector 0x7f06009b
int color mtrl_btn_ripple_color 0x7f06009c
int color mtrl_btn_stroke_color_selector 0x7f06009d
int color mtrl_btn_text_btn_bg_color_selector 0x7f06009e
int color mtrl_btn_text_btn_ripple_color 0x7f06009f
int color mtrl_btn_text_color_disabled 0x7f0600a0
int color mtrl_btn_text_color_selector 0x7f0600a1
int color mtrl_btn_transparent_bg_color 0x7f0600a2
int color mtrl_calendar_item_stroke_color 0x7f0600a3
int color mtrl_calendar_selected_range 0x7f0600a4
int color mtrl_card_view_foreground 0x7f0600a5
int color mtrl_card_view_ripple 0x7f0600a6
int color mtrl_chip_background_color 0x7f0600a7
int color mtrl_chip_close_icon_tint 0x7f0600a8
int color mtrl_chip_ripple_color 0x7f0600a9
int color mtrl_chip_surface_color 0x7f0600aa
int color mtrl_chip_text_color 0x7f0600ab
int color mtrl_choice_chip_background_color 0x7f0600ac
int color mtrl_choice_chip_ripple_color 0x7f0600ad
int color mtrl_choice_chip_text_color 0x7f0600ae
int color mtrl_error 0x7f0600af
int color mtrl_fab_bg_color_selector 0x7f0600b0
int color mtrl_fab_icon_text_color_selector 0x7f0600b1
int color mtrl_fab_ripple_color 0x7f0600b2
int color mtrl_filled_background_color 0x7f0600b3
int color mtrl_filled_icon_tint 0x7f0600b4
int color mtrl_filled_stroke_color 0x7f0600b5
int color mtrl_indicator_text_color 0x7f0600b6
int color mtrl_navigation_item_background_color 0x7f0600b7
int color mtrl_navigation_item_icon_tint 0x7f0600b8
int color mtrl_navigation_item_text_color 0x7f0600b9
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0600ba
int color mtrl_outlined_icon_tint 0x7f0600bb
int color mtrl_outlined_stroke_color 0x7f0600bc
int color mtrl_popupmenu_overlay_color 0x7f0600bd
int color mtrl_scrim_color 0x7f0600be
int color mtrl_tabs_colored_ripple_color 0x7f0600bf
int color mtrl_tabs_icon_color_selector 0x7f0600c0
int color mtrl_tabs_icon_color_selector_colored 0x7f0600c1
int color mtrl_tabs_legacy_text_color_selector 0x7f0600c2
int color mtrl_tabs_ripple_color 0x7f0600c3
int color mtrl_text_btn_text_color_selector 0x7f0600c4
int color mtrl_textinput_default_box_stroke_color 0x7f0600c5
int color mtrl_textinput_disabled_color 0x7f0600c6
int color mtrl_textinput_filled_box_default_background_color 0x7f0600c7
int color mtrl_textinput_focused_box_stroke_color 0x7f0600c8
int color mtrl_textinput_hovered_box_stroke_color 0x7f0600c9
int color notification_action_color_filter 0x7f0600ca
int color notification_icon_bg_color 0x7f0600cb
int color notification_icon_color 0x7f0600cc
int color notification_material_background_media_default_color 0x7f0600cd
int color primary_dark_material_dark 0x7f0600ce
int color primary_dark_material_light 0x7f0600cf
int color primary_material_dark 0x7f0600d0
int color primary_material_light 0x7f0600d1
int color primary_text_default_material_dark 0x7f0600d2
int color primary_text_default_material_light 0x7f0600d3
int color primary_text_disabled_material_dark 0x7f0600d4
int color primary_text_disabled_material_light 0x7f0600d5
int color radiobutton_themeable_attribute_color 0x7f0600d6
int color ripple_material_dark 0x7f0600d7
int color ripple_material_light 0x7f0600d8
int color secondary_text_default_material_dark 0x7f0600d9
int color secondary_text_default_material_light 0x7f0600da
int color secondary_text_disabled_material_dark 0x7f0600db
int color secondary_text_disabled_material_light 0x7f0600dc
int color splashscreen_background 0x7f0600dd
int color switch_thumb_disabled_material_dark 0x7f0600de
int color switch_thumb_disabled_material_light 0x7f0600df
int color switch_thumb_material_dark 0x7f0600e0
int color switch_thumb_material_light 0x7f0600e1
int color switch_thumb_normal_material_dark 0x7f0600e2
int color switch_thumb_normal_material_light 0x7f0600e3
int color test_mtrl_calendar_day 0x7f0600e4
int color test_mtrl_calendar_day_selected 0x7f0600e5
int color tooltip_background_dark 0x7f0600e6
int color tooltip_background_light 0x7f0600e7
int color vector_tint_color 0x7f0600e8
int color vector_tint_theme_color 0x7f0600e9
int dimen abc_action_bar_content_inset_material 0x7f070000
int dimen abc_action_bar_content_inset_with_nav 0x7f070001
int dimen abc_action_bar_default_height_material 0x7f070002
int dimen abc_action_bar_default_padding_end_material 0x7f070003
int dimen abc_action_bar_default_padding_start_material 0x7f070004
int dimen abc_action_bar_elevation_material 0x7f070005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070006
int dimen abc_action_bar_overflow_padding_end_material 0x7f070007
int dimen abc_action_bar_overflow_padding_start_material 0x7f070008
int dimen abc_action_bar_stacked_max_height 0x7f070009
int dimen abc_action_bar_stacked_tab_max_width 0x7f07000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07000c
int dimen abc_action_button_min_height_material 0x7f07000d
int dimen abc_action_button_min_width_material 0x7f07000e
int dimen abc_action_button_min_width_overflow_material 0x7f07000f
int dimen abc_alert_dialog_button_bar_height 0x7f070010
int dimen abc_alert_dialog_button_dimen 0x7f070011
int dimen abc_button_inset_horizontal_material 0x7f070012
int dimen abc_button_inset_vertical_material 0x7f070013
int dimen abc_button_padding_horizontal_material 0x7f070014
int dimen abc_button_padding_vertical_material 0x7f070015
int dimen abc_cascading_menus_min_smallest_width 0x7f070016
int dimen abc_config_prefDialogWidth 0x7f070017
int dimen abc_control_corner_material 0x7f070018
int dimen abc_control_inset_material 0x7f070019
int dimen abc_control_padding_material 0x7f07001a
int dimen abc_dialog_corner_radius_material 0x7f07001b
int dimen abc_dialog_fixed_height_major 0x7f07001c
int dimen abc_dialog_fixed_height_minor 0x7f07001d
int dimen abc_dialog_fixed_width_major 0x7f07001e
int dimen abc_dialog_fixed_width_minor 0x7f07001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070020
int dimen abc_dialog_list_padding_top_no_title 0x7f070021
int dimen abc_dialog_min_width_major 0x7f070022
int dimen abc_dialog_min_width_minor 0x7f070023
int dimen abc_dialog_padding_material 0x7f070024
int dimen abc_dialog_padding_top_material 0x7f070025
int dimen abc_dialog_title_divider_material 0x7f070026
int dimen abc_disabled_alpha_material_dark 0x7f070027
int dimen abc_disabled_alpha_material_light 0x7f070028
int dimen abc_dropdownitem_icon_width 0x7f070029
int dimen abc_dropdownitem_text_padding_left 0x7f07002a
int dimen abc_dropdownitem_text_padding_right 0x7f07002b
int dimen abc_edit_text_inset_bottom_material 0x7f07002c
int dimen abc_edit_text_inset_horizontal_material 0x7f07002d
int dimen abc_edit_text_inset_top_material 0x7f07002e
int dimen abc_floating_window_z 0x7f07002f
int dimen abc_list_item_height_large_material 0x7f070030
int dimen abc_list_item_height_material 0x7f070031
int dimen abc_list_item_height_small_material 0x7f070032
int dimen abc_list_item_padding_horizontal_material 0x7f070033
int dimen abc_panel_menu_list_width 0x7f070034
int dimen abc_progress_bar_height_material 0x7f070035
int dimen abc_search_view_preferred_height 0x7f070036
int dimen abc_search_view_preferred_width 0x7f070037
int dimen abc_seekbar_track_background_height_material 0x7f070038
int dimen abc_seekbar_track_progress_height_material 0x7f070039
int dimen abc_select_dialog_padding_start_material 0x7f07003a
int dimen abc_star_big 0x7f07003b
int dimen abc_star_medium 0x7f07003c
int dimen abc_star_small 0x7f07003d
int dimen abc_switch_padding 0x7f07003e
int dimen abc_text_size_body_1_material 0x7f07003f
int dimen abc_text_size_body_2_material 0x7f070040
int dimen abc_text_size_button_material 0x7f070041
int dimen abc_text_size_caption_material 0x7f070042
int dimen abc_text_size_display_1_material 0x7f070043
int dimen abc_text_size_display_2_material 0x7f070044
int dimen abc_text_size_display_3_material 0x7f070045
int dimen abc_text_size_display_4_material 0x7f070046
int dimen abc_text_size_headline_material 0x7f070047
int dimen abc_text_size_large_material 0x7f070048
int dimen abc_text_size_medium_material 0x7f070049
int dimen abc_text_size_menu_header_material 0x7f07004a
int dimen abc_text_size_menu_material 0x7f07004b
int dimen abc_text_size_small_material 0x7f07004c
int dimen abc_text_size_subhead_material 0x7f07004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f07004e
int dimen abc_text_size_title_material 0x7f07004f
int dimen abc_text_size_title_material_toolbar 0x7f070050
int dimen action_bar_size 0x7f070051
int dimen appcompat_dialog_background_inset 0x7f070052
int dimen autofill_inline_suggestion_icon_size 0x7f070053
int dimen browser_actions_context_menu_max_width 0x7f070054
int dimen browser_actions_context_menu_min_padding 0x7f070055
int dimen cardview_compat_inset_shadow 0x7f070056
int dimen cardview_default_elevation 0x7f070057
int dimen cardview_default_radius 0x7f070058
int dimen compat_button_inset_horizontal_material 0x7f070059
int dimen compat_button_inset_vertical_material 0x7f07005a
int dimen compat_button_padding_horizontal_material 0x7f07005b
int dimen compat_button_padding_vertical_material 0x7f07005c
int dimen compat_control_corner_material 0x7f07005d
int dimen compat_notification_large_icon_max_height 0x7f07005e
int dimen compat_notification_large_icon_max_width 0x7f07005f
int dimen default_dimension 0x7f070060
int dimen design_appbar_elevation 0x7f070061
int dimen design_bottom_navigation_active_item_max_width 0x7f070062
int dimen design_bottom_navigation_active_item_min_width 0x7f070063
int dimen design_bottom_navigation_active_text_size 0x7f070064
int dimen design_bottom_navigation_elevation 0x7f070065
int dimen design_bottom_navigation_height 0x7f070066
int dimen design_bottom_navigation_icon_size 0x7f070067
int dimen design_bottom_navigation_item_max_width 0x7f070068
int dimen design_bottom_navigation_item_min_width 0x7f070069
int dimen design_bottom_navigation_margin 0x7f07006a
int dimen design_bottom_navigation_shadow_height 0x7f07006b
int dimen design_bottom_navigation_text_size 0x7f07006c
int dimen design_bottom_sheet_elevation 0x7f07006d
int dimen design_bottom_sheet_modal_elevation 0x7f07006e
int dimen design_bottom_sheet_peek_height_min 0x7f07006f
int dimen design_fab_border_width 0x7f070070
int dimen design_fab_elevation 0x7f070071
int dimen design_fab_image_size 0x7f070072
int dimen design_fab_size_mini 0x7f070073
int dimen design_fab_size_normal 0x7f070074
int dimen design_fab_translation_z_hovered_focused 0x7f070075
int dimen design_fab_translation_z_pressed 0x7f070076
int dimen design_navigation_elevation 0x7f070077
int dimen design_navigation_icon_padding 0x7f070078
int dimen design_navigation_icon_size 0x7f070079
int dimen design_navigation_item_horizontal_padding 0x7f07007a
int dimen design_navigation_item_icon_padding 0x7f07007b
int dimen design_navigation_max_width 0x7f07007c
int dimen design_navigation_padding_bottom 0x7f07007d
int dimen design_navigation_separator_vertical_padding 0x7f07007e
int dimen design_snackbar_action_inline_max_width 0x7f07007f
int dimen design_snackbar_action_text_color_alpha 0x7f070080
int dimen design_snackbar_background_corner_radius 0x7f070081
int dimen design_snackbar_elevation 0x7f070082
int dimen design_snackbar_extra_spacing_horizontal 0x7f070083
int dimen design_snackbar_max_width 0x7f070084
int dimen design_snackbar_min_width 0x7f070085
int dimen design_snackbar_padding_horizontal 0x7f070086
int dimen design_snackbar_padding_vertical 0x7f070087
int dimen design_snackbar_padding_vertical_2lines 0x7f070088
int dimen design_snackbar_text_size 0x7f070089
int dimen design_tab_max_width 0x7f07008a
int dimen design_tab_scrollable_min_width 0x7f07008b
int dimen design_tab_text_size 0x7f07008c
int dimen design_tab_text_size_2line 0x7f07008d
int dimen design_textinput_caption_translate_y 0x7f07008e
int dimen disabled_alpha_material_dark 0x7f07008f
int dimen disabled_alpha_material_light 0x7f070090
int dimen fastscroll_default_thickness 0x7f070091
int dimen fastscroll_margin 0x7f070092
int dimen fastscroll_minimum_range 0x7f070093
int dimen fingerprint_icon_size 0x7f070094
int dimen highlight_alpha_material_colored 0x7f070095
int dimen highlight_alpha_material_dark 0x7f070096
int dimen highlight_alpha_material_light 0x7f070097
int dimen hint_alpha_material_dark 0x7f070098
int dimen hint_alpha_material_light 0x7f070099
int dimen hint_pressed_alpha_material_dark 0x7f07009a
int dimen hint_pressed_alpha_material_light 0x7f07009b
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f07009c
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f07009d
int dimen item_touch_helper_swipe_escape_velocity 0x7f07009e
int dimen material_emphasis_disabled 0x7f07009f
int dimen material_emphasis_high_type 0x7f0700a0
int dimen material_emphasis_medium 0x7f0700a1
int dimen material_text_view_test_line_height 0x7f0700a2
int dimen material_text_view_test_line_height_override 0x7f0700a3
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f0700a4
int dimen mtrl_alert_dialog_background_inset_end 0x7f0700a5
int dimen mtrl_alert_dialog_background_inset_start 0x7f0700a6
int dimen mtrl_alert_dialog_background_inset_top 0x7f0700a7
int dimen mtrl_alert_dialog_picker_background_inset 0x7f0700a8
int dimen mtrl_badge_horizontal_edge_offset 0x7f0700a9
int dimen mtrl_badge_long_text_horizontal_padding 0x7f0700aa
int dimen mtrl_badge_radius 0x7f0700ab
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f0700ac
int dimen mtrl_badge_text_size 0x7f0700ad
int dimen mtrl_badge_with_text_radius 0x7f0700ae
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f0700af
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f0700b0
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f0700b1
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0700b2
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f0700b3
int dimen mtrl_bottomappbar_height 0x7f0700b4
int dimen mtrl_btn_corner_radius 0x7f0700b5
int dimen mtrl_btn_dialog_btn_min_width 0x7f0700b6
int dimen mtrl_btn_disabled_elevation 0x7f0700b7
int dimen mtrl_btn_disabled_z 0x7f0700b8
int dimen mtrl_btn_elevation 0x7f0700b9
int dimen mtrl_btn_focused_z 0x7f0700ba
int dimen mtrl_btn_hovered_z 0x7f0700bb
int dimen mtrl_btn_icon_btn_padding_left 0x7f0700bc
int dimen mtrl_btn_icon_padding 0x7f0700bd
int dimen mtrl_btn_inset 0x7f0700be
int dimen mtrl_btn_letter_spacing 0x7f0700bf
int dimen mtrl_btn_padding_bottom 0x7f0700c0
int dimen mtrl_btn_padding_left 0x7f0700c1
int dimen mtrl_btn_padding_right 0x7f0700c2
int dimen mtrl_btn_padding_top 0x7f0700c3
int dimen mtrl_btn_pressed_z 0x7f0700c4
int dimen mtrl_btn_stroke_size 0x7f0700c5
int dimen mtrl_btn_text_btn_icon_padding 0x7f0700c6
int dimen mtrl_btn_text_btn_padding_left 0x7f0700c7
int dimen mtrl_btn_text_btn_padding_right 0x7f0700c8
int dimen mtrl_btn_text_size 0x7f0700c9
int dimen mtrl_btn_z 0x7f0700ca
int dimen mtrl_calendar_action_height 0x7f0700cb
int dimen mtrl_calendar_action_padding 0x7f0700cc
int dimen mtrl_calendar_bottom_padding 0x7f0700cd
int dimen mtrl_calendar_content_padding 0x7f0700ce
int dimen mtrl_calendar_day_corner 0x7f0700cf
int dimen mtrl_calendar_day_height 0x7f0700d0
int dimen mtrl_calendar_day_horizontal_padding 0x7f0700d1
int dimen mtrl_calendar_day_today_stroke 0x7f0700d2
int dimen mtrl_calendar_day_vertical_padding 0x7f0700d3
int dimen mtrl_calendar_day_width 0x7f0700d4
int dimen mtrl_calendar_days_of_week_height 0x7f0700d5
int dimen mtrl_calendar_dialog_background_inset 0x7f0700d6
int dimen mtrl_calendar_header_content_padding 0x7f0700d7
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f0700d8
int dimen mtrl_calendar_header_divider_thickness 0x7f0700d9
int dimen mtrl_calendar_header_height 0x7f0700da
int dimen mtrl_calendar_header_height_fullscreen 0x7f0700db
int dimen mtrl_calendar_header_selection_line_height 0x7f0700dc
int dimen mtrl_calendar_header_text_padding 0x7f0700dd
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f0700de
int dimen mtrl_calendar_header_toggle_margin_top 0x7f0700df
int dimen mtrl_calendar_landscape_header_width 0x7f0700e0
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f0700e1
int dimen mtrl_calendar_month_horizontal_padding 0x7f0700e2
int dimen mtrl_calendar_month_vertical_padding 0x7f0700e3
int dimen mtrl_calendar_navigation_bottom_padding 0x7f0700e4
int dimen mtrl_calendar_navigation_height 0x7f0700e5
int dimen mtrl_calendar_navigation_top_padding 0x7f0700e6
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f0700e7
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f0700e8
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f0700e9
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f0700ea
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f0700eb
int dimen mtrl_calendar_text_input_padding_top 0x7f0700ec
int dimen mtrl_calendar_title_baseline_to_top 0x7f0700ed
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f0700ee
int dimen mtrl_calendar_year_corner 0x7f0700ef
int dimen mtrl_calendar_year_height 0x7f0700f0
int dimen mtrl_calendar_year_horizontal_padding 0x7f0700f1
int dimen mtrl_calendar_year_vertical_padding 0x7f0700f2
int dimen mtrl_calendar_year_width 0x7f0700f3
int dimen mtrl_card_checked_icon_margin 0x7f0700f4
int dimen mtrl_card_checked_icon_size 0x7f0700f5
int dimen mtrl_card_corner_radius 0x7f0700f6
int dimen mtrl_card_dragged_z 0x7f0700f7
int dimen mtrl_card_elevation 0x7f0700f8
int dimen mtrl_card_spacing 0x7f0700f9
int dimen mtrl_chip_pressed_translation_z 0x7f0700fa
int dimen mtrl_chip_text_size 0x7f0700fb
int dimen mtrl_edittext_rectangle_top_offset 0x7f0700fc
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f0700fd
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f0700fe
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f0700ff
int dimen mtrl_extended_fab_bottom_padding 0x7f070100
int dimen mtrl_extended_fab_corner_radius 0x7f070101
int dimen mtrl_extended_fab_disabled_elevation 0x7f070102
int dimen mtrl_extended_fab_disabled_translation_z 0x7f070103
int dimen mtrl_extended_fab_elevation 0x7f070104
int dimen mtrl_extended_fab_end_padding 0x7f070105
int dimen mtrl_extended_fab_end_padding_icon 0x7f070106
int dimen mtrl_extended_fab_icon_size 0x7f070107
int dimen mtrl_extended_fab_icon_text_spacing 0x7f070108
int dimen mtrl_extended_fab_min_height 0x7f070109
int dimen mtrl_extended_fab_min_width 0x7f07010a
int dimen mtrl_extended_fab_start_padding 0x7f07010b
int dimen mtrl_extended_fab_start_padding_icon 0x7f07010c
int dimen mtrl_extended_fab_top_padding 0x7f07010d
int dimen mtrl_extended_fab_translation_z_base 0x7f07010e
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f07010f
int dimen mtrl_extended_fab_translation_z_pressed 0x7f070110
int dimen mtrl_fab_elevation 0x7f070111
int dimen mtrl_fab_min_touch_target 0x7f070112
int dimen mtrl_fab_translation_z_hovered_focused 0x7f070113
int dimen mtrl_fab_translation_z_pressed 0x7f070114
int dimen mtrl_high_ripple_default_alpha 0x7f070115
int dimen mtrl_high_ripple_focused_alpha 0x7f070116
int dimen mtrl_high_ripple_hovered_alpha 0x7f070117
int dimen mtrl_high_ripple_pressed_alpha 0x7f070118
int dimen mtrl_large_touch_target 0x7f070119
int dimen mtrl_low_ripple_default_alpha 0x7f07011a
int dimen mtrl_low_ripple_focused_alpha 0x7f07011b
int dimen mtrl_low_ripple_hovered_alpha 0x7f07011c
int dimen mtrl_low_ripple_pressed_alpha 0x7f07011d
int dimen mtrl_min_touch_target_size 0x7f07011e
int dimen mtrl_navigation_elevation 0x7f07011f
int dimen mtrl_navigation_item_horizontal_padding 0x7f070120
int dimen mtrl_navigation_item_icon_padding 0x7f070121
int dimen mtrl_navigation_item_icon_size 0x7f070122
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f070123
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f070124
int dimen mtrl_shape_corner_size_large_component 0x7f070125
int dimen mtrl_shape_corner_size_medium_component 0x7f070126
int dimen mtrl_shape_corner_size_small_component 0x7f070127
int dimen mtrl_slider_halo_radius 0x7f070128
int dimen mtrl_slider_label_padding 0x7f070129
int dimen mtrl_slider_label_radius 0x7f07012a
int dimen mtrl_slider_label_square_side 0x7f07012b
int dimen mtrl_slider_thumb_elevation 0x7f07012c
int dimen mtrl_slider_thumb_radius 0x7f07012d
int dimen mtrl_slider_track_height 0x7f07012e
int dimen mtrl_slider_track_side_padding 0x7f07012f
int dimen mtrl_slider_track_top 0x7f070130
int dimen mtrl_slider_widget_height 0x7f070131
int dimen mtrl_snackbar_action_text_color_alpha 0x7f070132
int dimen mtrl_snackbar_background_corner_radius 0x7f070133
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f070134
int dimen mtrl_snackbar_margin 0x7f070135
int dimen mtrl_switch_thumb_elevation 0x7f070136
int dimen mtrl_textinput_box_corner_radius_medium 0x7f070137
int dimen mtrl_textinput_box_corner_radius_small 0x7f070138
int dimen mtrl_textinput_box_label_cutout_padding 0x7f070139
int dimen mtrl_textinput_box_stroke_width_default 0x7f07013a
int dimen mtrl_textinput_box_stroke_width_focused 0x7f07013b
int dimen mtrl_textinput_counter_margin_start 0x7f07013c
int dimen mtrl_textinput_end_icon_margin_start 0x7f07013d
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f07013e
int dimen mtrl_textinput_start_icon_margin_end 0x7f07013f
int dimen mtrl_toolbar_default_height 0x7f070140
int dimen mtrl_tooltip_arrowSize 0x7f070141
int dimen mtrl_tooltip_cornerSize 0x7f070142
int dimen mtrl_tooltip_minHeight 0x7f070143
int dimen mtrl_tooltip_minWidth 0x7f070144
int dimen mtrl_tooltip_padding 0x7f070145
int dimen mtrl_transition_shared_axis_slide_distance 0x7f070146
int dimen notification_action_icon_size 0x7f070147
int dimen notification_action_text_size 0x7f070148
int dimen notification_big_circle_margin 0x7f070149
int dimen notification_content_margin_start 0x7f07014a
int dimen notification_large_icon_height 0x7f07014b
int dimen notification_large_icon_width 0x7f07014c
int dimen notification_main_column_padding_top 0x7f07014d
int dimen notification_media_narrow_margin 0x7f07014e
int dimen notification_right_icon_size 0x7f07014f
int dimen notification_right_side_padding_top 0x7f070150
int dimen notification_small_icon_background_padding 0x7f070151
int dimen notification_small_icon_size_as_large 0x7f070152
int dimen notification_subtext_size 0x7f070153
int dimen notification_top_pad 0x7f070154
int dimen notification_top_pad_large_text 0x7f070155
int dimen subtitle_corner_radius 0x7f070156
int dimen subtitle_outline_width 0x7f070157
int dimen subtitle_shadow_offset 0x7f070158
int dimen subtitle_shadow_radius 0x7f070159
int dimen test_mtrl_calendar_day_cornerSize 0x7f07015a
int dimen tooltip_corner_radius 0x7f07015b
int dimen tooltip_horizontal_padding 0x7f07015c
int dimen tooltip_margin 0x7f07015d
int dimen tooltip_precise_anchor_extra_offset 0x7f07015e
int dimen tooltip_precise_anchor_threshold 0x7f07015f
int dimen tooltip_vertical_padding 0x7f070160
int dimen tooltip_y_offset_non_touch 0x7f070161
int dimen tooltip_y_offset_touch 0x7f070162
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080006
int drawable abc_action_bar_item_background_material 0x7f080007
int drawable abc_btn_borderless_material 0x7f080008
int drawable abc_btn_check_material 0x7f080009
int drawable abc_btn_check_material_anim 0x7f08000a
int drawable abc_btn_check_to_on_mtrl_000 0x7f08000b
int drawable abc_btn_check_to_on_mtrl_015 0x7f08000c
int drawable abc_btn_colored_material 0x7f08000d
int drawable abc_btn_default_mtrl_shape 0x7f08000e
int drawable abc_btn_radio_material 0x7f08000f
int drawable abc_btn_radio_material_anim 0x7f080010
int drawable abc_btn_radio_to_on_mtrl_000 0x7f080011
int drawable abc_btn_radio_to_on_mtrl_015 0x7f080012
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f080013
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f080014
int drawable abc_cab_background_internal_bg 0x7f080015
int drawable abc_cab_background_top_material 0x7f080016
int drawable abc_cab_background_top_mtrl_alpha 0x7f080017
int drawable abc_control_background_material 0x7f080018
int drawable abc_dialog_material_background 0x7f080019
int drawable abc_edit_text_material 0x7f08001a
int drawable abc_ic_ab_back_material 0x7f08001b
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f08001c
int drawable abc_ic_clear_material 0x7f08001d
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f08001e
int drawable abc_ic_go_search_api_material 0x7f08001f
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f080020
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f080021
int drawable abc_ic_menu_overflow_material 0x7f080022
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f080023
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f080024
int drawable abc_ic_menu_share_mtrl_alpha 0x7f080025
int drawable abc_ic_search_api_material 0x7f080026
int drawable abc_ic_voice_search_api_material 0x7f080027
int drawable abc_item_background_holo_dark 0x7f080028
int drawable abc_item_background_holo_light 0x7f080029
int drawable abc_list_divider_material 0x7f08002a
int drawable abc_list_divider_mtrl_alpha 0x7f08002b
int drawable abc_list_focused_holo 0x7f08002c
int drawable abc_list_longpressed_holo 0x7f08002d
int drawable abc_list_pressed_holo_dark 0x7f08002e
int drawable abc_list_pressed_holo_light 0x7f08002f
int drawable abc_list_selector_background_transition_holo_dark 0x7f080030
int drawable abc_list_selector_background_transition_holo_light 0x7f080031
int drawable abc_list_selector_disabled_holo_dark 0x7f080032
int drawable abc_list_selector_disabled_holo_light 0x7f080033
int drawable abc_list_selector_holo_dark 0x7f080034
int drawable abc_list_selector_holo_light 0x7f080035
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080036
int drawable abc_popup_background_mtrl_mult 0x7f080037
int drawable abc_ratingbar_indicator_material 0x7f080038
int drawable abc_ratingbar_material 0x7f080039
int drawable abc_ratingbar_small_material 0x7f08003a
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08003b
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f08003c
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f08003d
int drawable abc_scrubber_primary_mtrl_alpha 0x7f08003e
int drawable abc_scrubber_track_mtrl_alpha 0x7f08003f
int drawable abc_seekbar_thumb_material 0x7f080040
int drawable abc_seekbar_tick_mark_material 0x7f080041
int drawable abc_seekbar_track_material 0x7f080042
int drawable abc_spinner_mtrl_am_alpha 0x7f080043
int drawable abc_spinner_textfield_background_material 0x7f080044
int drawable abc_star_black_48dp 0x7f080045
int drawable abc_star_half_black_48dp 0x7f080046
int drawable abc_switch_thumb_material 0x7f080047
int drawable abc_switch_track_mtrl_alpha 0x7f080048
int drawable abc_tab_indicator_material 0x7f080049
int drawable abc_tab_indicator_mtrl_alpha 0x7f08004a
int drawable abc_text_cursor_material 0x7f08004b
int drawable abc_text_select_handle_left_mtrl 0x7f08004c
int drawable abc_text_select_handle_middle_mtrl 0x7f08004d
int drawable abc_text_select_handle_right_mtrl 0x7f08004e
int drawable abc_textfield_activated_mtrl_alpha 0x7f08004f
int drawable abc_textfield_default_mtrl_alpha 0x7f080050
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080051
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080052
int drawable abc_textfield_search_material 0x7f080053
int drawable abc_vector_test 0x7f080054
int drawable alert 0x7f080055
int drawable autofill_inline_suggestion_chip_background 0x7f080056
int drawable avd_hide_password 0x7f080057
int drawable avd_show_password 0x7f080058
int drawable btn_checkbox_checked_mtrl 0x7f080059
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f08005a
int drawable btn_checkbox_unchecked_mtrl 0x7f08005b
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f08005c
int drawable btn_radio_off_mtrl 0x7f08005d
int drawable btn_radio_off_to_on_mtrl_animation 0x7f08005e
int drawable btn_radio_on_mtrl 0x7f08005f
int drawable btn_radio_on_to_off_mtrl_animation 0x7f080060
int drawable bug 0x7f080061
int drawable check_circle 0x7f080062
int drawable chevron_right 0x7f080063
int drawable common_full_open_on_phone 0x7f080064
int drawable common_google_signin_btn_icon_dark 0x7f080065
int drawable common_google_signin_btn_icon_dark_focused 0x7f080066
int drawable common_google_signin_btn_icon_dark_normal 0x7f080067
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f080068
int drawable common_google_signin_btn_icon_disabled 0x7f080069
int drawable common_google_signin_btn_icon_light 0x7f08006a
int drawable common_google_signin_btn_icon_light_focused 0x7f08006b
int drawable common_google_signin_btn_icon_light_normal 0x7f08006c
int drawable common_google_signin_btn_icon_light_normal_background 0x7f08006d
int drawable common_google_signin_btn_text_dark 0x7f08006e
int drawable common_google_signin_btn_text_dark_focused 0x7f08006f
int drawable common_google_signin_btn_text_dark_normal 0x7f080070
int drawable common_google_signin_btn_text_dark_normal_background 0x7f080071
int drawable common_google_signin_btn_text_disabled 0x7f080072
int drawable common_google_signin_btn_text_light 0x7f080073
int drawable common_google_signin_btn_text_light_focused 0x7f080074
int drawable common_google_signin_btn_text_light_normal 0x7f080075
int drawable common_google_signin_btn_text_light_normal_background 0x7f080076
int drawable copy 0x7f080077
int drawable design_bottom_navigation_item_background 0x7f080078
int drawable design_fab_background 0x7f080079
int drawable design_ic_visibility 0x7f08007a
int drawable design_ic_visibility_off 0x7f08007b
int drawable design_password_eye 0x7f08007c
int drawable design_snackbar_background 0x7f08007d
int drawable dev_menu_fab_icon 0x7f08007e
int drawable download 0x7f08007f
int drawable ellipsis_horizontal 0x7f080080
int drawable expo_logo 0x7f080081
int drawable fast_refresh 0x7f080082
int drawable fingerprint_dialog_error 0x7f080083
int drawable fingerprint_dialog_fp_icon 0x7f080084
int drawable googleg_disabled_color_18 0x7f080085
int drawable googleg_standard_color_18 0x7f080086
int drawable home 0x7f080087
int drawable ic_call_answer 0x7f080088
int drawable ic_call_answer_low 0x7f080089
int drawable ic_call_answer_video 0x7f08008a
int drawable ic_call_answer_video_low 0x7f08008b
int drawable ic_call_decline 0x7f08008c
int drawable ic_call_decline_low 0x7f08008d
int drawable ic_launcher_background 0x7f08008e
int drawable ic_mtrl_checked_circle 0x7f08008f
int drawable ic_mtrl_chip_checked_black 0x7f080090
int drawable ic_mtrl_chip_checked_circle 0x7f080091
int drawable ic_mtrl_chip_close_circle 0x7f080092
int drawable ic_resume 0x7f080093
int drawable inspect 0x7f080094
int drawable log_in 0x7f080095
int drawable material_ic_calendar_black_24dp 0x7f080096
int drawable material_ic_clear_black_24dp 0x7f080097
int drawable material_ic_edit_black_24dp 0x7f080098
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f080099
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f08009a
int drawable material_ic_menu_arrow_down_black_24dp 0x7f08009b
int drawable material_ic_menu_arrow_up_black_24dp 0x7f08009c
int drawable mtrl_dialog_background 0x7f08009d
int drawable mtrl_dropdown_arrow 0x7f08009e
int drawable mtrl_ic_arrow_drop_down 0x7f08009f
int drawable mtrl_ic_arrow_drop_up 0x7f0800a0
int drawable mtrl_ic_cancel 0x7f0800a1
int drawable mtrl_ic_error 0x7f0800a2
int drawable mtrl_popupmenu_background 0x7f0800a3
int drawable mtrl_popupmenu_background_dark 0x7f0800a4
int drawable mtrl_tabs_default_indicator 0x7f0800a5
int drawable navigation_empty_icon 0x7f0800a6
int drawable notification_action_background 0x7f0800a7
int drawable notification_bg 0x7f0800a8
int drawable notification_bg_low 0x7f0800a9
int drawable notification_bg_low_normal 0x7f0800aa
int drawable notification_bg_low_pressed 0x7f0800ab
int drawable notification_bg_normal 0x7f0800ac
int drawable notification_bg_normal_pressed 0x7f0800ad
int drawable notification_icon 0x7f0800ae
int drawable notification_icon_background 0x7f0800af
int drawable notification_oversize_large_icon_bg 0x7f0800b0
int drawable notification_template_icon_bg 0x7f0800b1
int drawable notification_template_icon_low_bg 0x7f0800b2
int drawable notification_tile_bg 0x7f0800b3
int drawable notify_panel_notification_icon_bg 0x7f0800b4
int drawable paused_in_debugger_background 0x7f0800b5
int drawable paused_in_debugger_dialog_background 0x7f0800b6
int drawable performance 0x7f0800b7
int drawable plus 0x7f0800b8
int drawable redbox_top_border_background 0x7f0800b9
int drawable refresh 0x7f0800ba
int drawable refresh_round_icon 0x7f0800bb
int drawable ripple_effect 0x7f0800bc
int drawable rn_edit_text_material 0x7f0800bd
int drawable scan 0x7f0800be
int drawable settings 0x7f0800bf
int drawable show_at_launch 0x7f0800c0
int drawable splashscreen_logo 0x7f0800c1
int drawable test_custom_background 0x7f0800c2
int drawable test_level_drawable 0x7f0800c3
int drawable tooltip_frame_dark 0x7f0800c4
int drawable tooltip_frame_light 0x7f0800c5
int drawable updates_nav 0x7f0800c6
int drawable user 0x7f0800c7
int drawable x_close 0x7f0800c8
int font inter_bold 0x7f090000
int font inter_medium 0x7f090001
int font inter_regular 0x7f090002
int font inter_semibold 0x7f090003
int font jetbrains_mono_light 0x7f090004
int font jetbrains_mono_medium 0x7f090005
int font jetbrains_mono_regular 0x7f090006
int id ALT 0x7f0a0000
int id BOTTOM_END 0x7f0a0001
int id BOTTOM_START 0x7f0a0002
int id CTRL 0x7f0a0003
int id FUNCTION 0x7f0a0004
int id META 0x7f0a0005
int id SHIFT 0x7f0a0006
int id SYM 0x7f0a0007
int id TOP_END 0x7f0a0008
int id TOP_START 0x7f0a0009
int id accessibility_action_clickable_span 0x7f0a000a
int id accessibility_actions 0x7f0a000b
int id accessibility_collection 0x7f0a000c
int id accessibility_collection_item 0x7f0a000d
int id accessibility_custom_action_0 0x7f0a000e
int id accessibility_custom_action_1 0x7f0a000f
int id accessibility_custom_action_10 0x7f0a0010
int id accessibility_custom_action_11 0x7f0a0011
int id accessibility_custom_action_12 0x7f0a0012
int id accessibility_custom_action_13 0x7f0a0013
int id accessibility_custom_action_14 0x7f0a0014
int id accessibility_custom_action_15 0x7f0a0015
int id accessibility_custom_action_16 0x7f0a0016
int id accessibility_custom_action_17 0x7f0a0017
int id accessibility_custom_action_18 0x7f0a0018
int id accessibility_custom_action_19 0x7f0a0019
int id accessibility_custom_action_2 0x7f0a001a
int id accessibility_custom_action_20 0x7f0a001b
int id accessibility_custom_action_21 0x7f0a001c
int id accessibility_custom_action_22 0x7f0a001d
int id accessibility_custom_action_23 0x7f0a001e
int id accessibility_custom_action_24 0x7f0a001f
int id accessibility_custom_action_25 0x7f0a0020
int id accessibility_custom_action_26 0x7f0a0021
int id accessibility_custom_action_27 0x7f0a0022
int id accessibility_custom_action_28 0x7f0a0023
int id accessibility_custom_action_29 0x7f0a0024
int id accessibility_custom_action_3 0x7f0a0025
int id accessibility_custom_action_30 0x7f0a0026
int id accessibility_custom_action_31 0x7f0a0027
int id accessibility_custom_action_4 0x7f0a0028
int id accessibility_custom_action_5 0x7f0a0029
int id accessibility_custom_action_6 0x7f0a002a
int id accessibility_custom_action_7 0x7f0a002b
int id accessibility_custom_action_8 0x7f0a002c
int id accessibility_custom_action_9 0x7f0a002d
int id accessibility_hint 0x7f0a002e
int id accessibility_label 0x7f0a002f
int id accessibility_links 0x7f0a0030
int id accessibility_order 0x7f0a0031
int id accessibility_order_parent 0x7f0a0032
int id accessibility_role 0x7f0a0033
int id accessibility_state 0x7f0a0034
int id accessibility_state_expanded 0x7f0a0035
int id accessibility_value 0x7f0a0036
int id action0 0x7f0a0037
int id action_bar 0x7f0a0038
int id action_bar_activity_content 0x7f0a0039
int id action_bar_container 0x7f0a003a
int id action_bar_root 0x7f0a003b
int id action_bar_spinner 0x7f0a003c
int id action_bar_subtitle 0x7f0a003d
int id action_bar_title 0x7f0a003e
int id action_container 0x7f0a003f
int id action_context_bar 0x7f0a0040
int id action_divider 0x7f0a0041
int id action_image 0x7f0a0042
int id action_menu_divider 0x7f0a0043
int id action_menu_presenter 0x7f0a0044
int id action_mode_bar 0x7f0a0045
int id action_mode_bar_stub 0x7f0a0046
int id action_mode_close_button 0x7f0a0047
int id action_text 0x7f0a0048
int id actions 0x7f0a0049
int id activity_chooser_view_content 0x7f0a004a
int id add 0x7f0a004b
int id adjust_height 0x7f0a004c
int id adjust_width 0x7f0a004d
int id alertTitle 0x7f0a004e
int id alert_title 0x7f0a004f
int id all 0x7f0a0050
int id always 0x7f0a0051
int id androidx_compose_ui_view_composition_context 0x7f0a0052
int id async 0x7f0a0053
int id auto 0x7f0a0054
int id autofill_inline_suggestion_end_icon 0x7f0a0055
int id autofill_inline_suggestion_start_icon 0x7f0a0056
int id autofill_inline_suggestion_subtitle 0x7f0a0057
int id autofill_inline_suggestion_title 0x7f0a0058
int id beginning 0x7f0a0059
int id blocking 0x7f0a005a
int id bottom 0x7f0a005b
int id browser_actions_header_text 0x7f0a005c
int id browser_actions_menu_item_icon 0x7f0a005d
int id browser_actions_menu_item_text 0x7f0a005e
int id browser_actions_menu_items 0x7f0a005f
int id browser_actions_menu_view 0x7f0a0060
int id button 0x7f0a0061
int id buttonPanel 0x7f0a0062
int id button_text 0x7f0a0063
int id cancel_action 0x7f0a0064
int id cancel_button 0x7f0a0065
int id catalyst_redbox_title 0x7f0a0066
int id center 0x7f0a0067
int id centerCrop 0x7f0a0068
int id centerInside 0x7f0a0069
int id center_horizontal 0x7f0a006a
int id center_vertical 0x7f0a006b
int id checkbox 0x7f0a006c
int id checked 0x7f0a006d
int id chip 0x7f0a006e
int id chip1 0x7f0a006f
int id chip2 0x7f0a0070
int id chip3 0x7f0a0071
int id chip_group 0x7f0a0072
int id chronometer 0x7f0a0073
int id clear_text 0x7f0a0074
int id clip_horizontal 0x7f0a0075
int id clip_vertical 0x7f0a0076
int id collapseActionView 0x7f0a0077
int id compose_prefetch_scheduler 0x7f0a0078
int id compose_view_saveable_id_tag 0x7f0a0079
int id confirm_button 0x7f0a007a
int id consume_window_insets_tag 0x7f0a007b
int id container 0x7f0a007c
int id content 0x7f0a007d
int id contentPanel 0x7f0a007e
int id coordinator 0x7f0a007f
int id custom 0x7f0a0080
int id customPanel 0x7f0a0081
int id cut 0x7f0a0082
int id dark 0x7f0a0083
int id date_picker_actions 0x7f0a0084
int id decor_content_parent 0x7f0a0085
int id default_activity_button 0x7f0a0086
int id design_bottom_sheet 0x7f0a0087
int id design_menu_item_action_area 0x7f0a0088
int id design_menu_item_action_area_stub 0x7f0a0089
int id design_menu_item_text 0x7f0a008a
int id design_navigation_view 0x7f0a008b
int id dialog_button 0x7f0a008c
int id disableHome 0x7f0a008d
int id dropdown_menu 0x7f0a008e
int id edit_query 0x7f0a008f
int id edit_text_id 0x7f0a0090
int id end 0x7f0a0091
int id end_padder 0x7f0a0092
int id enterAlways 0x7f0a0093
int id enterAlwaysCollapsed 0x7f0a0094
int id exitUntilCollapsed 0x7f0a0095
int id expand_activities_button 0x7f0a0096
int id expanded_menu 0x7f0a0097
int id fade 0x7f0a0098
int id fill 0x7f0a0099
int id fill_horizontal 0x7f0a009a
int id fill_vertical 0x7f0a009b
int id filled 0x7f0a009c
int id filter 0x7f0a009d
int id fingerprint_description 0x7f0a009e
int id fingerprint_error 0x7f0a009f
int id fingerprint_icon 0x7f0a00a0
int id fingerprint_subtitle 0x7f0a00a1
int id fitBottomStart 0x7f0a00a2
int id fitCenter 0x7f0a00a3
int id fitEnd 0x7f0a00a4
int id fitStart 0x7f0a00a5
int id fitToContents 0x7f0a00a6
int id fitXY 0x7f0a00a7
int id fixed 0x7f0a00a8
int id floating 0x7f0a00a9
int id focusCrop 0x7f0a00aa
int id forever 0x7f0a00ab
int id fps_text 0x7f0a00ac
int id fragment_container_view_tag 0x7f0a00ad
int id ghost_view 0x7f0a00ae
int id ghost_view_holder 0x7f0a00af
int id gone 0x7f0a00b0
int id group_divider 0x7f0a00b1
int id hide_graphics_layer_in_inspector_tag 0x7f0a00b2
int id hide_ime_id 0x7f0a00b3
int id hide_in_inspector_tag 0x7f0a00b4
int id hideable 0x7f0a00b5
int id home 0x7f0a00b6
int id homeAsUp 0x7f0a00b7
int id icon 0x7f0a00b8
int id icon_group 0x7f0a00b9
int id icon_only 0x7f0a00ba
int id ifRoom 0x7f0a00bb
int id image 0x7f0a00bc
int id info 0x7f0a00bd
int id inspection_slot_table_set 0x7f0a00be
int id invalidate_transform 0x7f0a00bf
int id is_pooling_container_tag 0x7f0a00c0
int id italic 0x7f0a00c1
int id item_touch_helper_previous_elevation 0x7f0a00c2
int id labeled 0x7f0a00c3
int id labelled_by 0x7f0a00c4
int id largeLabel 0x7f0a00c5
int id left 0x7f0a00c6
int id light 0x7f0a00c7
int id line1 0x7f0a00c8
int id line3 0x7f0a00c9
int id listMode 0x7f0a00ca
int id list_item 0x7f0a00cb
int id masked 0x7f0a00cc
int id media_actions 0x7f0a00cd
int id message 0x7f0a00ce
int id middle 0x7f0a00cf
int id mini 0x7f0a00d0
int id mix_blend_mode 0x7f0a00d1
int id month_grid 0x7f0a00d2
int id month_navigation_bar 0x7f0a00d3
int id month_navigation_fragment_toggle 0x7f0a00d4
int id month_navigation_next 0x7f0a00d5
int id month_navigation_previous 0x7f0a00d6
int id month_title 0x7f0a00d7
int id mtrl_calendar_day_selector_frame 0x7f0a00d8
int id mtrl_calendar_days_of_week 0x7f0a00d9
int id mtrl_calendar_frame 0x7f0a00da
int id mtrl_calendar_main_pane 0x7f0a00db
int id mtrl_calendar_months 0x7f0a00dc
int id mtrl_calendar_selection_frame 0x7f0a00dd
int id mtrl_calendar_text_input_frame 0x7f0a00de
int id mtrl_calendar_year_selector_frame 0x7f0a00df
int id mtrl_card_checked_layer_id 0x7f0a00e0
int id mtrl_child_content_container 0x7f0a00e1
int id mtrl_internal_children_alpha_tag 0x7f0a00e2
int id mtrl_motion_snapshot_view 0x7f0a00e3
int id mtrl_picker_fullscreen 0x7f0a00e4
int id mtrl_picker_header 0x7f0a00e5
int id mtrl_picker_header_selection_text 0x7f0a00e6
int id mtrl_picker_header_title_and_selection 0x7f0a00e7
int id mtrl_picker_header_toggle 0x7f0a00e8
int id mtrl_picker_text_input_date 0x7f0a00e9
int id mtrl_picker_text_input_range_end 0x7f0a00ea
int id mtrl_picker_text_input_range_start 0x7f0a00eb
int id mtrl_picker_title_text 0x7f0a00ec
int id multiply 0x7f0a00ed
int id nav_controller_view_tag 0x7f0a00ee
int id navigation_header_container 0x7f0a00ef
int id never 0x7f0a00f0
int id noScroll 0x7f0a00f1
int id none 0x7f0a00f2
int id normal 0x7f0a00f3
int id notification_background 0x7f0a00f4
int id notification_main_column 0x7f0a00f5
int id notification_main_column_container 0x7f0a00f6
int id off 0x7f0a00f7
int id on 0x7f0a00f8
int id original_focusability 0x7f0a00f9
int id outline 0x7f0a00fa
int id parallax 0x7f0a00fb
int id parentPanel 0x7f0a00fc
int id parent_matrix 0x7f0a00fd
int id password_toggle 0x7f0a00fe
int id peekHeight 0x7f0a00ff
int id pin 0x7f0a0100
int id pointer_events 0x7f0a0101
int id pooling_container_listener_holder_tag 0x7f0a0102
int id progress_circular 0x7f0a0103
int id progress_horizontal 0x7f0a0104
int id radio 0x7f0a0105
int id react_test_id 0x7f0a0106
int id report_drawn 0x7f0a0107
int id right 0x7f0a0108
int id right_icon 0x7f0a0109
int id right_side 0x7f0a010a
int id rn_frame_file 0x7f0a010b
int id rn_frame_method 0x7f0a010c
int id rn_redbox_dismiss_button 0x7f0a010d
int id rn_redbox_line_separator 0x7f0a010e
int id rn_redbox_loading_indicator 0x7f0a010f
int id rn_redbox_reload_button 0x7f0a0110
int id rn_redbox_report_button 0x7f0a0111
int id rn_redbox_report_label 0x7f0a0112
int id rn_redbox_stack 0x7f0a0113
int id role 0x7f0a0114
int id rounded 0x7f0a0115
int id row_index_key 0x7f0a0116
int id save_non_transition_alpha 0x7f0a0117
int id save_overlay_view 0x7f0a0118
int id scale 0x7f0a0119
int id screen 0x7f0a011a
int id scroll 0x7f0a011b
int id scrollIndicatorDown 0x7f0a011c
int id scrollIndicatorUp 0x7f0a011d
int id scrollView 0x7f0a011e
int id scrollable 0x7f0a011f
int id search_badge 0x7f0a0120
int id search_bar 0x7f0a0121
int id search_button 0x7f0a0122
int id search_close_btn 0x7f0a0123
int id search_edit_frame 0x7f0a0124
int id search_go_btn 0x7f0a0125
int id search_mag_icon 0x7f0a0126
int id search_plate 0x7f0a0127
int id search_src_text 0x7f0a0128
int id search_voice_btn 0x7f0a0129
int id select_dialog_listview 0x7f0a012a
int id selected 0x7f0a012b
int id shortcut 0x7f0a012c
int id showCustom 0x7f0a012d
int id showHome 0x7f0a012e
int id showTitle 0x7f0a012f
int id skipCollapsed 0x7f0a0130
int id slide 0x7f0a0131
int id smallLabel 0x7f0a0132
int id snackbar_action 0x7f0a0133
int id snackbar_text 0x7f0a0134
int id snap 0x7f0a0135
int id snapMargins 0x7f0a0136
int id spacer 0x7f0a0137
int id special_effects_controller_view_tag 0x7f0a0138
int id split_action_bar 0x7f0a0139
int id src_atop 0x7f0a013a
int id src_in 0x7f0a013b
int id src_over 0x7f0a013c
int id standard 0x7f0a013d
int id start 0x7f0a013e
int id status_bar_latest_event_content 0x7f0a013f
int id stretch 0x7f0a0140
int id submenuarrow 0x7f0a0141
int id submit_area 0x7f0a0142
int id tabMode 0x7f0a0143
int id tag_accessibility_actions 0x7f0a0144
int id tag_accessibility_clickable_spans 0x7f0a0145
int id tag_accessibility_heading 0x7f0a0146
int id tag_accessibility_pane_title 0x7f0a0147
int id tag_on_apply_window_listener 0x7f0a0148
int id tag_on_receive_content_listener 0x7f0a0149
int id tag_on_receive_content_mime_types 0x7f0a014a
int id tag_screen_reader_focusable 0x7f0a014b
int id tag_state_description 0x7f0a014c
int id tag_transition_group 0x7f0a014d
int id tag_unhandled_key_event_manager 0x7f0a014e
int id tag_unhandled_key_listeners 0x7f0a014f
int id tag_window_insets_animation_callback 0x7f0a0150
int id test_checkbox_android_button_tint 0x7f0a0151
int id test_checkbox_app_button_tint 0x7f0a0152
int id test_radiobutton_android_button_tint 0x7f0a0153
int id test_radiobutton_app_button_tint 0x7f0a0154
int id text 0x7f0a0155
int id text2 0x7f0a0156
int id textEnd 0x7f0a0157
int id textSpacerNoButtons 0x7f0a0158
int id textSpacerNoTitle 0x7f0a0159
int id textStart 0x7f0a015a
int id text_input_end_icon 0x7f0a015b
int id text_input_start_icon 0x7f0a015c
int id textinput_counter 0x7f0a015d
int id textinput_error 0x7f0a015e
int id textinput_helper_text 0x7f0a015f
int id textinput_placeholder 0x7f0a0160
int id textinput_prefix_text 0x7f0a0161
int id textinput_suffix_text 0x7f0a0162
int id time 0x7f0a0163
int id title 0x7f0a0164
int id titleDividerNoCustom 0x7f0a0165
int id title_template 0x7f0a0166
int id top 0x7f0a0167
int id topPanel 0x7f0a0168
int id touch_outside 0x7f0a0169
int id transform 0x7f0a016a
int id transform_origin 0x7f0a016b
int id transition_current_scene 0x7f0a016c
int id transition_layout_save 0x7f0a016d
int id transition_position 0x7f0a016e
int id transition_scene_layoutid_cache 0x7f0a016f
int id transition_transform 0x7f0a0170
int id unchecked 0x7f0a0171
int id uniform 0x7f0a0172
int id unlabeled 0x7f0a0173
int id up 0x7f0a0174
int id useLogo 0x7f0a0175
int id use_hardware_layer 0x7f0a0176
int id view_clipped 0x7f0a0177
int id view_offset_helper 0x7f0a0178
int id view_tag_instance_handle 0x7f0a0179
int id view_tag_native_id 0x7f0a017a
int id view_tree_disjoint_parent 0x7f0a017b
int id view_tree_lifecycle_owner 0x7f0a017c
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0a017d
int id view_tree_saved_state_registry_owner 0x7f0a017e
int id view_tree_view_model_store_owner 0x7f0a017f
int id visible 0x7f0a0180
int id visible_removing_fragment_view_tag 0x7f0a0181
int id wide 0x7f0a0182
int id withText 0x7f0a0183
int id withinBounds 0x7f0a0184
int id wrap_content 0x7f0a0185
int id wrapped_composition_tag 0x7f0a0186
int id zero_corner_chip 0x7f0a0187
int integer abc_config_activityDefaultDur 0x7f0b0000
int integer abc_config_activityShortDur 0x7f0b0001
int integer app_bar_elevation_anim_duration 0x7f0b0002
int integer bottom_sheet_slide_duration 0x7f0b0003
int integer cancel_button_image_alpha 0x7f0b0004
int integer config_tooltipAnimTime 0x7f0b0005
int integer design_snackbar_text_max_lines 0x7f0b0006
int integer design_tab_indicator_anim_duration_ms 0x7f0b0007
int integer google_play_services_version 0x7f0b0008
int integer hide_password_duration 0x7f0b0009
int integer mtrl_badge_max_character_count 0x7f0b000a
int integer mtrl_btn_anim_delay_ms 0x7f0b000b
int integer mtrl_btn_anim_duration_ms 0x7f0b000c
int integer mtrl_calendar_header_orientation 0x7f0b000d
int integer mtrl_calendar_selection_text_lines 0x7f0b000e
int integer mtrl_calendar_year_selector_span 0x7f0b000f
int integer mtrl_card_anim_delay_ms 0x7f0b0010
int integer mtrl_card_anim_duration_ms 0x7f0b0011
int integer mtrl_chip_anim_duration 0x7f0b0012
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0b0013
int integer react_native_dev_server_port 0x7f0b0014
int integer show_password_duration 0x7f0b0015
int integer status_bar_notification_info_maxnum 0x7f0b0016
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0c0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0c0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0c0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0c0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0c0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0c0005
int interpolator fast_out_slow_in 0x7f0c0006
int interpolator mtrl_fast_out_linear_in 0x7f0c0007
int interpolator mtrl_fast_out_slow_in 0x7f0c0008
int interpolator mtrl_linear 0x7f0c0009
int interpolator mtrl_linear_out_slow_in 0x7f0c000a
int layout abc_action_bar_title_item 0x7f0d0000
int layout abc_action_bar_up_container 0x7f0d0001
int layout abc_action_menu_item_layout 0x7f0d0002
int layout abc_action_menu_layout 0x7f0d0003
int layout abc_action_mode_bar 0x7f0d0004
int layout abc_action_mode_close_item_material 0x7f0d0005
int layout abc_activity_chooser_view 0x7f0d0006
int layout abc_activity_chooser_view_list_item 0x7f0d0007
int layout abc_alert_dialog_button_bar_material 0x7f0d0008
int layout abc_alert_dialog_material 0x7f0d0009
int layout abc_alert_dialog_title_material 0x7f0d000a
int layout abc_cascading_menu_item_layout 0x7f0d000b
int layout abc_dialog_title_material 0x7f0d000c
int layout abc_expanded_menu_layout 0x7f0d000d
int layout abc_list_menu_item_checkbox 0x7f0d000e
int layout abc_list_menu_item_icon 0x7f0d000f
int layout abc_list_menu_item_layout 0x7f0d0010
int layout abc_list_menu_item_radio 0x7f0d0011
int layout abc_popup_menu_header_item_layout 0x7f0d0012
int layout abc_popup_menu_item_layout 0x7f0d0013
int layout abc_screen_content_include 0x7f0d0014
int layout abc_screen_simple 0x7f0d0015
int layout abc_screen_simple_overlay_action_mode 0x7f0d0016
int layout abc_screen_toolbar 0x7f0d0017
int layout abc_search_dropdown_item_icons_2line 0x7f0d0018
int layout abc_search_view 0x7f0d0019
int layout abc_select_dialog_material 0x7f0d001a
int layout abc_tooltip 0x7f0d001b
int layout alert_title_layout 0x7f0d001c
int layout autofill_inline_suggestion 0x7f0d001d
int layout browser_actions_context_menu_page 0x7f0d001e
int layout browser_actions_context_menu_row 0x7f0d001f
int layout custom_dialog 0x7f0d0020
int layout design_bottom_navigation_item 0x7f0d0021
int layout design_bottom_sheet_dialog 0x7f0d0022
int layout design_layout_snackbar 0x7f0d0023
int layout design_layout_snackbar_include 0x7f0d0024
int layout design_layout_tab_icon 0x7f0d0025
int layout design_layout_tab_text 0x7f0d0026
int layout design_menu_item_action_area 0x7f0d0027
int layout design_navigation_item 0x7f0d0028
int layout design_navigation_item_header 0x7f0d0029
int layout design_navigation_item_separator 0x7f0d002a
int layout design_navigation_item_subheader 0x7f0d002b
int layout design_navigation_menu 0x7f0d002c
int layout design_navigation_menu_item 0x7f0d002d
int layout design_text_input_end_icon 0x7f0d002e
int layout design_text_input_start_icon 0x7f0d002f
int layout dev_loading_view 0x7f0d0030
int layout fingerprint_dialog_layout 0x7f0d0031
int layout fps_view 0x7f0d0032
int layout ime_base_split_test_activity 0x7f0d0033
int layout ime_secondary_split_test_activity 0x7f0d0034
int layout mtrl_alert_dialog 0x7f0d0035
int layout mtrl_alert_dialog_actions 0x7f0d0036
int layout mtrl_alert_dialog_title 0x7f0d0037
int layout mtrl_alert_select_dialog_item 0x7f0d0038
int layout mtrl_alert_select_dialog_multichoice 0x7f0d0039
int layout mtrl_alert_select_dialog_singlechoice 0x7f0d003a
int layout mtrl_calendar_day 0x7f0d003b
int layout mtrl_calendar_day_of_week 0x7f0d003c
int layout mtrl_calendar_days_of_week 0x7f0d003d
int layout mtrl_calendar_horizontal 0x7f0d003e
int layout mtrl_calendar_month 0x7f0d003f
int layout mtrl_calendar_month_labeled 0x7f0d0040
int layout mtrl_calendar_month_navigation 0x7f0d0041
int layout mtrl_calendar_months 0x7f0d0042
int layout mtrl_calendar_vertical 0x7f0d0043
int layout mtrl_calendar_year 0x7f0d0044
int layout mtrl_layout_snackbar 0x7f0d0045
int layout mtrl_layout_snackbar_include 0x7f0d0046
int layout mtrl_picker_actions 0x7f0d0047
int layout mtrl_picker_dialog 0x7f0d0048
int layout mtrl_picker_fullscreen 0x7f0d0049
int layout mtrl_picker_header_dialog 0x7f0d004a
int layout mtrl_picker_header_fullscreen 0x7f0d004b
int layout mtrl_picker_header_selection_text 0x7f0d004c
int layout mtrl_picker_header_title_text 0x7f0d004d
int layout mtrl_picker_header_toggle 0x7f0d004e
int layout mtrl_picker_text_input_date 0x7f0d004f
int layout mtrl_picker_text_input_date_range 0x7f0d0050
int layout notification_action 0x7f0d0051
int layout notification_action_tombstone 0x7f0d0052
int layout notification_media_action 0x7f0d0053
int layout notification_media_cancel_action 0x7f0d0054
int layout notification_template_big_media 0x7f0d0055
int layout notification_template_big_media_custom 0x7f0d0056
int layout notification_template_big_media_narrow 0x7f0d0057
int layout notification_template_big_media_narrow_custom 0x7f0d0058
int layout notification_template_custom_big 0x7f0d0059
int layout notification_template_icon_group 0x7f0d005a
int layout notification_template_lines_media 0x7f0d005b
int layout notification_template_media 0x7f0d005c
int layout notification_template_media_custom 0x7f0d005d
int layout notification_template_part_chronometer 0x7f0d005e
int layout notification_template_part_time 0x7f0d005f
int layout paused_in_debugger_view 0x7f0d0060
int layout redbox_item_frame 0x7f0d0061
int layout redbox_item_title 0x7f0d0062
int layout redbox_view 0x7f0d0063
int layout select_dialog_item_material 0x7f0d0064
int layout select_dialog_multichoice_material 0x7f0d0065
int layout select_dialog_singlechoice_material 0x7f0d0066
int layout support_simple_spinner_dropdown_item 0x7f0d0067
int layout test_action_chip 0x7f0d0068
int layout test_chip_zero_corner_radius 0x7f0d0069
int layout test_design_checkbox 0x7f0d006a
int layout test_design_radiobutton 0x7f0d006b
int layout test_reflow_chipgroup 0x7f0d006c
int layout test_toolbar 0x7f0d006d
int layout test_toolbar_custom_background 0x7f0d006e
int layout test_toolbar_elevation 0x7f0d006f
int layout test_toolbar_surface 0x7f0d0070
int layout text_view_with_line_height_from_appearance 0x7f0d0071
int layout text_view_with_line_height_from_layout 0x7f0d0072
int layout text_view_with_line_height_from_style 0x7f0d0073
int layout text_view_with_theme_line_height 0x7f0d0074
int layout text_view_without_line_height 0x7f0d0075
int mipmap ic_launcher 0x7f0e0000
int mipmap ic_launcher_foreground 0x7f0e0001
int mipmap ic_launcher_round 0x7f0e0002
int plurals mtrl_badge_content_description 0x7f0f0000
int raw firebase_common_keep 0x7f100000
int raw keep 0x7f100001
int string abc_action_bar_home_description 0x7f110000
int string abc_action_bar_up_description 0x7f110001
int string abc_action_menu_overflow_description 0x7f110002
int string abc_action_mode_done 0x7f110003
int string abc_activity_chooser_view_see_all 0x7f110004
int string abc_activitychooserview_choose_application 0x7f110005
int string abc_capital_off 0x7f110006
int string abc_capital_on 0x7f110007
int string abc_menu_alt_shortcut_label 0x7f110008
int string abc_menu_ctrl_shortcut_label 0x7f110009
int string abc_menu_delete_shortcut_label 0x7f11000a
int string abc_menu_enter_shortcut_label 0x7f11000b
int string abc_menu_function_shortcut_label 0x7f11000c
int string abc_menu_meta_shortcut_label 0x7f11000d
int string abc_menu_shift_shortcut_label 0x7f11000e
int string abc_menu_space_shortcut_label 0x7f11000f
int string abc_menu_sym_shortcut_label 0x7f110010
int string abc_prepend_shortcut_label 0x7f110011
int string abc_search_hint 0x7f110012
int string abc_searchview_description_clear 0x7f110013
int string abc_searchview_description_query 0x7f110014
int string abc_searchview_description_search 0x7f110015
int string abc_searchview_description_submit 0x7f110016
int string abc_searchview_description_voice 0x7f110017
int string abc_shareactionprovider_share_with 0x7f110018
int string abc_shareactionprovider_share_with_application 0x7f110019
int string abc_toolbar_collapse_description 0x7f11001a
int string alert_description 0x7f11001b
int string androidx_startup 0x7f11001c
int string app_name 0x7f11001d
int string appbar_scrolling_view_behavior 0x7f11001e
int string autofill 0x7f11001f
int string biometric_or_screen_lock_prompt_message 0x7f110020
int string biometric_prompt_message 0x7f110021
int string bottom_sheet_behavior 0x7f110022
int string call_notification_answer_action 0x7f110023
int string call_notification_answer_video_action 0x7f110024
int string call_notification_decline_action 0x7f110025
int string call_notification_hang_up_action 0x7f110026
int string call_notification_incoming_text 0x7f110027
int string call_notification_ongoing_text 0x7f110028
int string call_notification_screening_text 0x7f110029
int string catalyst_change_bundle_location 0x7f11002a
int string catalyst_change_bundle_location_apply 0x7f11002b
int string catalyst_change_bundle_location_cancel 0x7f11002c
int string catalyst_change_bundle_location_input_hint 0x7f11002d
int string catalyst_change_bundle_location_input_label 0x7f11002e
int string catalyst_change_bundle_location_instructions 0x7f11002f
int string catalyst_copy_button 0x7f110030
int string catalyst_debug_connecting 0x7f110031
int string catalyst_debug_error 0x7f110032
int string catalyst_debug_open 0x7f110033
int string catalyst_debug_open_disabled 0x7f110034
int string catalyst_dev_menu_header 0x7f110035
int string catalyst_dev_menu_sub_header 0x7f110036
int string catalyst_dismiss_button 0x7f110037
int string catalyst_heap_capture 0x7f110038
int string catalyst_hot_reloading 0x7f110039
int string catalyst_hot_reloading_auto_disable 0x7f11003a
int string catalyst_hot_reloading_auto_enable 0x7f11003b
int string catalyst_hot_reloading_stop 0x7f11003c
int string catalyst_inspector_toggle 0x7f11003d
int string catalyst_loading_from_url 0x7f11003e
int string catalyst_open_debugger_error 0x7f11003f
int string catalyst_perf_monitor 0x7f110040
int string catalyst_perf_monitor_stop 0x7f110041
int string catalyst_reload 0x7f110042
int string catalyst_reload_button 0x7f110043
int string catalyst_reload_error 0x7f110044
int string catalyst_report_button 0x7f110045
int string catalyst_sample_profiler_toggle 0x7f110046
int string catalyst_settings 0x7f110047
int string catalyst_settings_title 0x7f110048
int string character_counter_content_description 0x7f110049
int string character_counter_overflowed_content_description 0x7f11004a
int string character_counter_pattern 0x7f11004b
int string chip_text 0x7f11004c
int string clear_text_end_icon_content_description 0x7f11004d
int string close_drawer 0x7f11004e
int string close_sheet 0x7f11004f
int string combobox_description 0x7f110050
int string common_google_play_services_enable_button 0x7f110051
int string common_google_play_services_enable_text 0x7f110052
int string common_google_play_services_enable_title 0x7f110053
int string common_google_play_services_install_button 0x7f110054
int string common_google_play_services_install_text 0x7f110055
int string common_google_play_services_install_title 0x7f110056
int string common_google_play_services_notification_channel_name 0x7f110057
int string common_google_play_services_notification_ticker 0x7f110058
int string common_google_play_services_unknown_issue 0x7f110059
int string common_google_play_services_unsupported_text 0x7f11005a
int string common_google_play_services_update_button 0x7f11005b
int string common_google_play_services_update_text 0x7f11005c
int string common_google_play_services_update_title 0x7f11005d
int string common_google_play_services_updating_text 0x7f11005e
int string common_google_play_services_wear_update_text 0x7f11005f
int string common_open_on_phone 0x7f110060
int string common_signin_button_text 0x7f110061
int string common_signin_button_text_long 0x7f110062
int string confirm_device_credential_password 0x7f110063
int string copy_toast_msg 0x7f110064
int string default_error_message 0x7f110065
int string default_error_msg 0x7f110066
int string default_popup_window_title 0x7f110067
int string dropdown_menu 0x7f110068
int string error_icon_content_description 0x7f110069
int string expo_notifications_fallback_channel_name 0x7f11006a
int string expo_splash_screen_resize_mode 0x7f11006b
int string expo_splash_screen_status_bar_translucent 0x7f11006c
int string exposed_dropdown_menu_content_description 0x7f11006d
int string fab_transformation_scrim_behavior 0x7f11006e
int string fab_transformation_sheet_behavior 0x7f11006f
int string face_or_screen_lock_prompt_message 0x7f110070
int string face_prompt_message 0x7f110071
int string fallback_menu_item_copy_link 0x7f110072
int string fallback_menu_item_open_in_browser 0x7f110073
int string fallback_menu_item_share_link 0x7f110074
int string fcm_fallback_notification_channel_label 0x7f110075
int string fingerprint_dialog_icon_description 0x7f110076
int string fingerprint_dialog_touch_sensor 0x7f110077
int string fingerprint_error_hw_not_available 0x7f110078
int string fingerprint_error_hw_not_present 0x7f110079
int string fingerprint_error_lockout 0x7f11007a
int string fingerprint_error_no_fingerprints 0x7f11007b
int string fingerprint_error_user_canceled 0x7f11007c
int string fingerprint_not_recognized 0x7f11007d
int string fingerprint_or_screen_lock_prompt_message 0x7f11007e
int string fingerprint_prompt_message 0x7f11007f
int string gcm_defaultSenderId 0x7f110080
int string generic_error_no_device_credential 0x7f110081
int string generic_error_no_keyguard 0x7f110082
int string generic_error_user_canceled 0x7f110083
int string google_api_key 0x7f110084
int string google_app_id 0x7f110085
int string google_crash_reporting_api_key 0x7f110086
int string google_storage_bucket 0x7f110087
int string header_description 0x7f110088
int string hide_bottom_view_on_scroll_behavior 0x7f110089
int string icon_content_description 0x7f11008a
int string image_description 0x7f11008b
int string imagebutton_description 0x7f11008c
int string in_progress 0x7f11008d
int string indeterminate 0x7f11008e
int string item_view_role_description 0x7f11008f
int string link_description 0x7f110090
int string material_slider_range_end 0x7f110091
int string material_slider_range_start 0x7f110092
int string menu_description 0x7f110093
int string menubar_description 0x7f110094
int string menuitem_description 0x7f110095
int string mtrl_badge_numberless_content_description 0x7f110096
int string mtrl_chip_close_icon_content_description 0x7f110097
int string mtrl_exceed_max_badge_number_content_description 0x7f110098
int string mtrl_exceed_max_badge_number_suffix 0x7f110099
int string mtrl_picker_a11y_next_month 0x7f11009a
int string mtrl_picker_a11y_prev_month 0x7f11009b
int string mtrl_picker_announce_current_selection 0x7f11009c
int string mtrl_picker_cancel 0x7f11009d
int string mtrl_picker_confirm 0x7f11009e
int string mtrl_picker_date_header_selected 0x7f11009f
int string mtrl_picker_date_header_title 0x7f1100a0
int string mtrl_picker_date_header_unselected 0x7f1100a1
int string mtrl_picker_day_of_week_column_header 0x7f1100a2
int string mtrl_picker_invalid_format 0x7f1100a3
int string mtrl_picker_invalid_format_example 0x7f1100a4
int string mtrl_picker_invalid_format_use 0x7f1100a5
int string mtrl_picker_invalid_range 0x7f1100a6
int string mtrl_picker_navigate_to_year_description 0x7f1100a7
int string mtrl_picker_out_of_range 0x7f1100a8
int string mtrl_picker_range_header_only_end_selected 0x7f1100a9
int string mtrl_picker_range_header_only_start_selected 0x7f1100aa
int string mtrl_picker_range_header_selected 0x7f1100ab
int string mtrl_picker_range_header_title 0x7f1100ac
int string mtrl_picker_range_header_unselected 0x7f1100ad
int string mtrl_picker_save 0x7f1100ae
int string mtrl_picker_text_input_date_hint 0x7f1100af
int string mtrl_picker_text_input_date_range_end_hint 0x7f1100b0
int string mtrl_picker_text_input_date_range_start_hint 0x7f1100b1
int string mtrl_picker_text_input_day_abbr 0x7f1100b2
int string mtrl_picker_text_input_month_abbr 0x7f1100b3
int string mtrl_picker_text_input_year_abbr 0x7f1100b4
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f1100b5
int string mtrl_picker_toggle_to_day_selection 0x7f1100b6
int string mtrl_picker_toggle_to_text_input_mode 0x7f1100b7
int string mtrl_picker_toggle_to_year_selection 0x7f1100b8
int string navigation_menu 0x7f1100b9
int string not_selected 0x7f1100ba
int string password_toggle_content_description 0x7f1100bb
int string path_password_eye 0x7f1100bc
int string path_password_eye_mask_strike_through 0x7f1100bd
int string path_password_eye_mask_visible 0x7f1100be
int string path_password_strike_through 0x7f1100bf
int string progressbar_description 0x7f1100c0
int string project_id 0x7f1100c1
int string radiogroup_description 0x7f1100c2
int string range_end 0x7f1100c3
int string range_start 0x7f1100c4
int string react_native_dev_server_ip 0x7f1100c5
int string rn_tab_description 0x7f1100c6
int string screen_lock_prompt_message 0x7f1100c7
int string scrollbar_description 0x7f1100c8
int string search_menu_title 0x7f1100c9
int string selected 0x7f1100ca
int string snackbar_pane_title 0x7f1100cb
int string spinbutton_description 0x7f1100cc
int string state_busy_description 0x7f1100cd
int string state_collapsed_description 0x7f1100ce
int string state_empty 0x7f1100cf
int string state_expanded_description 0x7f1100d0
int string state_mixed_description 0x7f1100d1
int string state_off 0x7f1100d2
int string state_off_description 0x7f1100d3
int string state_on 0x7f1100d4
int string state_on_description 0x7f1100d5
int string state_unselected_description 0x7f1100d6
int string status_bar_notification_info_overflow 0x7f1100d7
int string summary_description 0x7f1100d8
int string switch_role 0x7f1100d9
int string tab 0x7f1100da
int string tablist_description 0x7f1100db
int string template_percent 0x7f1100dc
int string timer_description 0x7f1100dd
int string toolbar_description 0x7f1100de
int string tooltip_description 0x7f1100df
int string tooltip_label 0x7f1100e0
int string use_biometric_label 0x7f1100e1
int string use_biometric_or_screen_lock_label 0x7f1100e2
int string use_face_label 0x7f1100e3
int string use_face_or_screen_lock_label 0x7f1100e4
int string use_fingerprint_label 0x7f1100e5
int string use_fingerprint_or_screen_lock_label 0x7f1100e6
int string use_screen_lock_label 0x7f1100e7
int style AlertDialog_AppCompat 0x7f120000
int style AlertDialog_AppCompat_Light 0x7f120001
int style AndroidThemeColorAccentYellow 0x7f120002
int style Animation_AppCompat_Dialog 0x7f120003
int style Animation_AppCompat_DropDownUp 0x7f120004
int style Animation_AppCompat_Tooltip 0x7f120005
int style Animation_Catalyst_LogBox 0x7f120006
int style Animation_Catalyst_RedBox 0x7f120007
int style Animation_Design_BottomSheetDialog 0x7f120008
int style Animation_MaterialComponents_BottomSheetDialog 0x7f120009
int style AppTheme 0x7f12000a
int style Base_AlertDialog_AppCompat 0x7f12000b
int style Base_AlertDialog_AppCompat_Light 0x7f12000c
int style Base_Animation_AppCompat_Dialog 0x7f12000d
int style Base_Animation_AppCompat_DropDownUp 0x7f12000e
int style Base_Animation_AppCompat_Tooltip 0x7f12000f
int style Base_CardView 0x7f120010
int style Base_DialogWindowTitle_AppCompat 0x7f120011
int style Base_DialogWindowTitleBackground_AppCompat 0x7f120012
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f120013
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f120014
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f120015
int style Base_TextAppearance_AppCompat 0x7f120016
int style Base_TextAppearance_AppCompat_Body1 0x7f120017
int style Base_TextAppearance_AppCompat_Body2 0x7f120018
int style Base_TextAppearance_AppCompat_Button 0x7f120019
int style Base_TextAppearance_AppCompat_Caption 0x7f12001a
int style Base_TextAppearance_AppCompat_Display1 0x7f12001b
int style Base_TextAppearance_AppCompat_Display2 0x7f12001c
int style Base_TextAppearance_AppCompat_Display3 0x7f12001d
int style Base_TextAppearance_AppCompat_Display4 0x7f12001e
int style Base_TextAppearance_AppCompat_Headline 0x7f12001f
int style Base_TextAppearance_AppCompat_Inverse 0x7f120020
int style Base_TextAppearance_AppCompat_Large 0x7f120021
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f120022
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f120023
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f120024
int style Base_TextAppearance_AppCompat_Medium 0x7f120025
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f120026
int style Base_TextAppearance_AppCompat_Menu 0x7f120027
int style Base_TextAppearance_AppCompat_SearchResult 0x7f120028
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f120029
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f12002a
int style Base_TextAppearance_AppCompat_Small 0x7f12002b
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f12002c
int style Base_TextAppearance_AppCompat_Subhead 0x7f12002d
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f12002e
int style Base_TextAppearance_AppCompat_Title 0x7f12002f
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f120030
int style Base_TextAppearance_AppCompat_Tooltip 0x7f120031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f120032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f120033
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f120034
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f120035
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f120036
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f120037
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f120038
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f120039
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f12003a
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f12003b
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f12003c
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f12003d
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f12003e
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f12003f
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f120040
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f120041
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f120042
int style Base_TextAppearance_MaterialComponents_Badge 0x7f120043
int style Base_TextAppearance_MaterialComponents_Button 0x7f120044
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f120045
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f120046
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f120047
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f120048
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f120049
int style Base_Theme_AppCompat 0x7f12004a
int style Base_Theme_AppCompat_CompactMenu 0x7f12004b
int style Base_Theme_AppCompat_Dialog 0x7f12004c
int style Base_Theme_AppCompat_Dialog_Alert 0x7f12004d
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f12004e
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f12004f
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f120050
int style Base_Theme_AppCompat_Light 0x7f120051
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f120052
int style Base_Theme_AppCompat_Light_Dialog 0x7f120053
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f120054
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f120055
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f120056
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f120057
int style Base_Theme_MaterialComponents 0x7f120058
int style Base_Theme_MaterialComponents_Bridge 0x7f120059
int style Base_Theme_MaterialComponents_CompactMenu 0x7f12005a
int style Base_Theme_MaterialComponents_Dialog 0x7f12005b
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f12005c
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f12005d
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f12005e
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f12005f
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f120060
int style Base_Theme_MaterialComponents_Light 0x7f120061
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f120062
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f120063
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f120064
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f120065
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f120066
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f120067
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f120068
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f120069
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f12006a
int style Base_ThemeOverlay_AppCompat 0x7f12006b
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f12006c
int style Base_ThemeOverlay_AppCompat_Dark 0x7f12006d
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f12006e
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f12006f
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f120070
int style Base_ThemeOverlay_AppCompat_Light 0x7f120071
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f120072
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f120073
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f120074
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f120075
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f120076
int style Base_V14_Theme_MaterialComponents 0x7f120077
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f120078
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f120079
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f12007a
int style Base_V14_Theme_MaterialComponents_Light 0x7f12007b
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f12007c
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f12007d
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f12007e
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f12007f
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f120080
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f120081
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f120082
int style Base_V21_Theme_AppCompat 0x7f120083
int style Base_V21_Theme_AppCompat_Dialog 0x7f120084
int style Base_V21_Theme_AppCompat_Light 0x7f120085
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f120086
int style Base_V21_Theme_MaterialComponents 0x7f120087
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f120088
int style Base_V21_Theme_MaterialComponents_Light 0x7f120089
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f12008a
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f12008b
int style Base_V22_Theme_AppCompat 0x7f12008c
int style Base_V22_Theme_AppCompat_Light 0x7f12008d
int style Base_V23_Theme_AppCompat 0x7f12008e
int style Base_V23_Theme_AppCompat_Light 0x7f12008f
int style Base_V26_Theme_AppCompat 0x7f120090
int style Base_V26_Theme_AppCompat_Light 0x7f120091
int style Base_V26_Widget_AppCompat_Toolbar 0x7f120092
int style Base_V28_Theme_AppCompat 0x7f120093
int style Base_V28_Theme_AppCompat_Light 0x7f120094
int style Base_V7_Theme_AppCompat 0x7f120095
int style Base_V7_Theme_AppCompat_Dialog 0x7f120096
int style Base_V7_Theme_AppCompat_Light 0x7f120097
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f120098
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f120099
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f12009a
int style Base_V7_Widget_AppCompat_EditText 0x7f12009b
int style Base_V7_Widget_AppCompat_Toolbar 0x7f12009c
int style Base_Widget_AppCompat_ActionBar 0x7f12009d
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f12009e
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f12009f
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f1200a0
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1200a1
int style Base_Widget_AppCompat_ActionButton 0x7f1200a2
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1200a3
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1200a4
int style Base_Widget_AppCompat_ActionMode 0x7f1200a5
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1200a6
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1200a7
int style Base_Widget_AppCompat_Button 0x7f1200a8
int style Base_Widget_AppCompat_Button_Borderless 0x7f1200a9
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1200aa
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1200ab
int style Base_Widget_AppCompat_Button_Colored 0x7f1200ac
int style Base_Widget_AppCompat_Button_Small 0x7f1200ad
int style Base_Widget_AppCompat_ButtonBar 0x7f1200ae
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1200af
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1200b0
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1200b1
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1200b2
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1200b3
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1200b4
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1200b5
int style Base_Widget_AppCompat_EditText 0x7f1200b6
int style Base_Widget_AppCompat_ImageButton 0x7f1200b7
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1200b8
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1200b9
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1200ba
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1200bb
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1200bc
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1200bd
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1200be
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1200bf
int style Base_Widget_AppCompat_ListMenuView 0x7f1200c0
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1200c1
int style Base_Widget_AppCompat_ListView 0x7f1200c2
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1200c3
int style Base_Widget_AppCompat_ListView_Menu 0x7f1200c4
int style Base_Widget_AppCompat_PopupMenu 0x7f1200c5
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1200c6
int style Base_Widget_AppCompat_PopupWindow 0x7f1200c7
int style Base_Widget_AppCompat_ProgressBar 0x7f1200c8
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1200c9
int style Base_Widget_AppCompat_RatingBar 0x7f1200ca
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1200cb
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1200cc
int style Base_Widget_AppCompat_SearchView 0x7f1200cd
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1200ce
int style Base_Widget_AppCompat_SeekBar 0x7f1200cf
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1200d0
int style Base_Widget_AppCompat_Spinner 0x7f1200d1
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1200d2
int style Base_Widget_AppCompat_TextView 0x7f1200d3
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1200d4
int style Base_Widget_AppCompat_Toolbar 0x7f1200d5
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1200d6
int style Base_Widget_Design_TabLayout 0x7f1200d7
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f1200d8
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f1200d9
int style Base_Widget_MaterialComponents_Chip 0x7f1200da
int style Base_Widget_MaterialComponents_PopupMenu 0x7f1200db
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f1200dc
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f1200dd
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f1200de
int style Base_Widget_MaterialComponents_Slider 0x7f1200df
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f1200e0
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f1200e1
int style Base_Widget_MaterialComponents_TextView 0x7f1200e2
int style CalendarDatePickerDialog 0x7f1200e3
int style CalendarDatePickerStyle 0x7f1200e4
int style CardView 0x7f1200e5
int style CardView_Dark 0x7f1200e6
int style CardView_Light 0x7f1200e7
int style DialogAnimationFade 0x7f1200e8
int style DialogAnimationSlide 0x7f1200e9
int style DialogWindowTheme 0x7f1200ea
int style EmptyTheme 0x7f1200eb
int style FloatingDialogWindowTheme 0x7f1200ec
int style MaterialAlertDialog_MaterialComponents 0x7f1200ed
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f1200ee
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f1200ef
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f1200f0
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f1200f1
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f1200f2
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f1200f3
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f1200f4
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f1200f5
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f1200f6
int style NoAnimationDialog 0x7f1200f7
int style Platform_AppCompat 0x7f1200f8
int style Platform_AppCompat_Light 0x7f1200f9
int style Platform_MaterialComponents 0x7f1200fa
int style Platform_MaterialComponents_Dialog 0x7f1200fb
int style Platform_MaterialComponents_Light 0x7f1200fc
int style Platform_MaterialComponents_Light_Dialog 0x7f1200fd
int style Platform_ThemeOverlay_AppCompat 0x7f1200fe
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f1200ff
int style Platform_ThemeOverlay_AppCompat_Light 0x7f120100
int style Platform_V21_AppCompat 0x7f120101
int style Platform_V21_AppCompat_Light 0x7f120102
int style Platform_V25_AppCompat 0x7f120103
int style Platform_V25_AppCompat_Light 0x7f120104
int style Platform_Widget_AppCompat_Spinner 0x7f120105
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f120106
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f120107
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f120108
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f120109
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f12010a
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f12010b
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f12010c
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f12010d
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f12010e
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f12010f
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f120110
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f120111
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f120112
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f120113
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f120114
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f120115
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f120116
int style ShapeAppearance_MaterialComponents 0x7f120117
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f120118
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f120119
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f12011a
int style ShapeAppearance_MaterialComponents_Test 0x7f12011b
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f12011c
int style ShapeAppearanceOverlay 0x7f12011d
int style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize 0x7f12011e
int style ShapeAppearanceOverlay_BottomRightCut 0x7f12011f
int style ShapeAppearanceOverlay_Cut 0x7f120120
int style ShapeAppearanceOverlay_DifferentCornerSize 0x7f120121
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f120122
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f120123
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f120124
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f120125
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f120126
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f120127
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f120128
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f120129
int style ShapeAppearanceOverlay_TopLeftCut 0x7f12012a
int style ShapeAppearanceOverlay_TopRightDifferentCornerSize 0x7f12012b
int style SpinnerDatePickerDialog 0x7f12012c
int style SpinnerDatePickerStyle 0x7f12012d
int style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f12012e
int style Test_Theme_MaterialComponents_MaterialCalendar 0x7f12012f
int style Test_Widget_MaterialComponents_MaterialCalendar 0x7f120130
int style Test_Widget_MaterialComponents_MaterialCalendar_Day 0x7f120131
int style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f120132
int style TestStyleWithLineHeight 0x7f120133
int style TestStyleWithLineHeightAppearance 0x7f120134
int style TestStyleWithThemeLineHeightAttribute 0x7f120135
int style TestStyleWithoutLineHeight 0x7f120136
int style TestThemeWithLineHeight 0x7f120137
int style TestThemeWithLineHeightDisabled 0x7f120138
int style TextAppearance_AppCompat 0x7f120139
int style TextAppearance_AppCompat_Body1 0x7f12013a
int style TextAppearance_AppCompat_Body2 0x7f12013b
int style TextAppearance_AppCompat_Button 0x7f12013c
int style TextAppearance_AppCompat_Caption 0x7f12013d
int style TextAppearance_AppCompat_Display1 0x7f12013e
int style TextAppearance_AppCompat_Display2 0x7f12013f
int style TextAppearance_AppCompat_Display3 0x7f120140
int style TextAppearance_AppCompat_Display4 0x7f120141
int style TextAppearance_AppCompat_Headline 0x7f120142
int style TextAppearance_AppCompat_Inverse 0x7f120143
int style TextAppearance_AppCompat_Large 0x7f120144
int style TextAppearance_AppCompat_Large_Inverse 0x7f120145
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f120146
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f120147
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f120148
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f120149
int style TextAppearance_AppCompat_Medium 0x7f12014a
int style TextAppearance_AppCompat_Medium_Inverse 0x7f12014b
int style TextAppearance_AppCompat_Menu 0x7f12014c
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f12014d
int style TextAppearance_AppCompat_SearchResult_Title 0x7f12014e
int style TextAppearance_AppCompat_Small 0x7f12014f
int style TextAppearance_AppCompat_Small_Inverse 0x7f120150
int style TextAppearance_AppCompat_Subhead 0x7f120151
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f120152
int style TextAppearance_AppCompat_Title 0x7f120153
int style TextAppearance_AppCompat_Title_Inverse 0x7f120154
int style TextAppearance_AppCompat_Tooltip 0x7f120155
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f120156
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f120157
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f120158
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f120159
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f12015a
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f12015b
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f12015c
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f12015d
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f12015e
int style TextAppearance_AppCompat_Widget_Button 0x7f12015f
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f120160
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f120161
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f120162
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f120163
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f120164
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f120165
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f120166
int style TextAppearance_AppCompat_Widget_Switch 0x7f120167
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f120168
int style TextAppearance_Compat_Notification 0x7f120169
int style TextAppearance_Compat_Notification_Info 0x7f12016a
int style TextAppearance_Compat_Notification_Info_Media 0x7f12016b
int style TextAppearance_Compat_Notification_Line2 0x7f12016c
int style TextAppearance_Compat_Notification_Line2_Media 0x7f12016d
int style TextAppearance_Compat_Notification_Media 0x7f12016e
int style TextAppearance_Compat_Notification_Time 0x7f12016f
int style TextAppearance_Compat_Notification_Time_Media 0x7f120170
int style TextAppearance_Compat_Notification_Title 0x7f120171
int style TextAppearance_Compat_Notification_Title_Media 0x7f120172
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f120173
int style TextAppearance_Design_Counter 0x7f120174
int style TextAppearance_Design_Counter_Overflow 0x7f120175
int style TextAppearance_Design_Error 0x7f120176
int style TextAppearance_Design_HelperText 0x7f120177
int style TextAppearance_Design_Hint 0x7f120178
int style TextAppearance_Design_Placeholder 0x7f120179
int style TextAppearance_Design_Prefix 0x7f12017a
int style TextAppearance_Design_Snackbar_Message 0x7f12017b
int style TextAppearance_Design_Suffix 0x7f12017c
int style TextAppearance_Design_Tab 0x7f12017d
int style TextAppearance_MaterialComponents_Badge 0x7f12017e
int style TextAppearance_MaterialComponents_Body1 0x7f12017f
int style TextAppearance_MaterialComponents_Body2 0x7f120180
int style TextAppearance_MaterialComponents_Button 0x7f120181
int style TextAppearance_MaterialComponents_Caption 0x7f120182
int style TextAppearance_MaterialComponents_Chip 0x7f120183
int style TextAppearance_MaterialComponents_Headline1 0x7f120184
int style TextAppearance_MaterialComponents_Headline2 0x7f120185
int style TextAppearance_MaterialComponents_Headline3 0x7f120186
int style TextAppearance_MaterialComponents_Headline4 0x7f120187
int style TextAppearance_MaterialComponents_Headline5 0x7f120188
int style TextAppearance_MaterialComponents_Headline6 0x7f120189
int style TextAppearance_MaterialComponents_Overline 0x7f12018a
int style TextAppearance_MaterialComponents_Subtitle1 0x7f12018b
int style TextAppearance_MaterialComponents_Subtitle2 0x7f12018c
int style TextAppearance_MaterialComponents_Tooltip 0x7f12018d
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f12018e
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f12018f
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f120190
int style Theme 0x7f120191
int style Theme_App_SplashScreen 0x7f120192
int style Theme_AppCompat 0x7f120193
int style Theme_AppCompat_CompactMenu 0x7f120194
int style Theme_AppCompat_DayNight 0x7f120195
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f120196
int style Theme_AppCompat_DayNight_Dialog 0x7f120197
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f120198
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f120199
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f12019a
int style Theme_AppCompat_DayNight_NoActionBar 0x7f12019b
int style Theme_AppCompat_Dialog 0x7f12019c
int style Theme_AppCompat_Dialog_Alert 0x7f12019d
int style Theme_AppCompat_Dialog_MinWidth 0x7f12019e
int style Theme_AppCompat_DialogWhenLarge 0x7f12019f
int style Theme_AppCompat_Empty 0x7f1201a0
int style Theme_AppCompat_Light 0x7f1201a1
int style Theme_AppCompat_Light_DarkActionBar 0x7f1201a2
int style Theme_AppCompat_Light_Dialog 0x7f1201a3
int style Theme_AppCompat_Light_Dialog_Alert 0x7f1201a4
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f1201a5
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f1201a6
int style Theme_AppCompat_Light_NoActionBar 0x7f1201a7
int style Theme_AppCompat_NoActionBar 0x7f1201a8
int style Theme_AutofillInlineSuggestion 0x7f1201a9
int style Theme_Catalyst 0x7f1201aa
int style Theme_Catalyst_LogBox 0x7f1201ab
int style Theme_Catalyst_RedBox 0x7f1201ac
int style Theme_Design 0x7f1201ad
int style Theme_Design_BottomSheetDialog 0x7f1201ae
int style Theme_Design_Light 0x7f1201af
int style Theme_Design_Light_BottomSheetDialog 0x7f1201b0
int style Theme_Design_Light_NoActionBar 0x7f1201b1
int style Theme_Design_NoActionBar 0x7f1201b2
int style Theme_DevLauncher_ErrorActivity 0x7f1201b3
int style Theme_FullScreenDialog 0x7f1201b4
int style Theme_FullScreenDialogAnimatedFade 0x7f1201b5
int style Theme_FullScreenDialogAnimatedSlide 0x7f1201b6
int style Theme_MaterialComponents 0x7f1201b7
int style Theme_MaterialComponents_BottomSheetDialog 0x7f1201b8
int style Theme_MaterialComponents_Bridge 0x7f1201b9
int style Theme_MaterialComponents_CompactMenu 0x7f1201ba
int style Theme_MaterialComponents_DayNight 0x7f1201bb
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f1201bc
int style Theme_MaterialComponents_DayNight_Bridge 0x7f1201bd
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f1201be
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f1201bf
int style Theme_MaterialComponents_DayNight_Dialog 0x7f1201c0
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f1201c1
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f1201c2
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f1201c3
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f1201c4
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f1201c5
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f1201c6
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f1201c7
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f1201c8
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f1201c9
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f1201ca
int style Theme_MaterialComponents_Dialog 0x7f1201cb
int style Theme_MaterialComponents_Dialog_Alert 0x7f1201cc
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f1201cd
int style Theme_MaterialComponents_Dialog_Bridge 0x7f1201ce
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f1201cf
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f1201d0
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f1201d1
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f1201d2
int style Theme_MaterialComponents_DialogWhenLarge 0x7f1201d3
int style Theme_MaterialComponents_Light 0x7f1201d4
int style Theme_MaterialComponents_Light_BarSize 0x7f1201d5
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f1201d6
int style Theme_MaterialComponents_Light_Bridge 0x7f1201d7
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f1201d8
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f1201d9
int style Theme_MaterialComponents_Light_Dialog 0x7f1201da
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f1201db
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f1201dc
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f1201dd
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f1201de
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f1201df
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f1201e0
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f1201e1
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f1201e2
int style Theme_MaterialComponents_Light_LargeTouch 0x7f1201e3
int style Theme_MaterialComponents_Light_NoActionBar 0x7f1201e4
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f1201e5
int style Theme_MaterialComponents_NoActionBar 0x7f1201e6
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f1201e7
int style Theme_ReactNative_AppCompat_Light 0x7f1201e8
int style Theme_ReactNative_AppCompat_Light_NoActionBar_FullScreen 0x7f1201e9
int style Theme_ReactNative_TextInput_DefaultBackground 0x7f1201ea
int style ThemeOverlay_AppCompat 0x7f1201eb
int style ThemeOverlay_AppCompat_ActionBar 0x7f1201ec
int style ThemeOverlay_AppCompat_Dark 0x7f1201ed
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f1201ee
int style ThemeOverlay_AppCompat_DayNight 0x7f1201ef
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f1201f0
int style ThemeOverlay_AppCompat_Dialog 0x7f1201f1
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f1201f2
int style ThemeOverlay_AppCompat_Light 0x7f1201f3
int style ThemeOverlay_Design_TextInputEditText 0x7f1201f4
int style ThemeOverlay_MaterialComponents 0x7f1201f5
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f1201f6
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f1201f7
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f1201f8
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f1201f9
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f1201fa
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f1201fb
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f1201fc
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f1201fd
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f1201fe
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f1201ff
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f120200
int style ThemeOverlay_MaterialComponents_Dark 0x7f120201
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f120202
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f120203
int style ThemeOverlay_MaterialComponents_Dialog 0x7f120204
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f120205
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f120206
int style ThemeOverlay_MaterialComponents_Light 0x7f120207
int style ThemeOverlay_MaterialComponents_Light_BottomSheetDialog 0x7f120208
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f120209
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f12020a
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f12020b
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f12020c
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f12020d
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f12020e
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f12020f
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f120210
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f120211
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f120212
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f120213
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f120214
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f120215
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f120216
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f120217
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f120218
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f120219
int style ThemeOverlayColorAccentRed 0x7f12021a
int style TranslucentDialog 0x7f12021b
int style Widget_AppCompat_ActionBar 0x7f12021c
int style Widget_AppCompat_ActionBar_Solid 0x7f12021d
int style Widget_AppCompat_ActionBar_TabBar 0x7f12021e
int style Widget_AppCompat_ActionBar_TabText 0x7f12021f
int style Widget_AppCompat_ActionBar_TabView 0x7f120220
int style Widget_AppCompat_ActionButton 0x7f120221
int style Widget_AppCompat_ActionButton_CloseMode 0x7f120222
int style Widget_AppCompat_ActionButton_Overflow 0x7f120223
int style Widget_AppCompat_ActionMode 0x7f120224
int style Widget_AppCompat_ActivityChooserView 0x7f120225
int style Widget_AppCompat_AutoCompleteTextView 0x7f120226
int style Widget_AppCompat_Button 0x7f120227
int style Widget_AppCompat_Button_Borderless 0x7f120228
int style Widget_AppCompat_Button_Borderless_Colored 0x7f120229
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f12022a
int style Widget_AppCompat_Button_Colored 0x7f12022b
int style Widget_AppCompat_Button_Small 0x7f12022c
int style Widget_AppCompat_ButtonBar 0x7f12022d
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f12022e
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f12022f
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f120230
int style Widget_AppCompat_CompoundButton_Switch 0x7f120231
int style Widget_AppCompat_DrawerArrowToggle 0x7f120232
int style Widget_AppCompat_DropDownItem_Spinner 0x7f120233
int style Widget_AppCompat_EditText 0x7f120234
int style Widget_AppCompat_ImageButton 0x7f120235
int style Widget_AppCompat_Light_ActionBar 0x7f120236
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f120237
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f120238
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f120239
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f12023a
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f12023b
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f12023c
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f12023d
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f12023e
int style Widget_AppCompat_Light_ActionButton 0x7f12023f
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f120240
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f120241
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f120242
int style Widget_AppCompat_Light_ActivityChooserView 0x7f120243
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f120244
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f120245
int style Widget_AppCompat_Light_ListPopupWindow 0x7f120246
int style Widget_AppCompat_Light_ListView_DropDown 0x7f120247
int style Widget_AppCompat_Light_PopupMenu 0x7f120248
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f120249
int style Widget_AppCompat_Light_SearchView 0x7f12024a
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f12024b
int style Widget_AppCompat_ListMenuView 0x7f12024c
int style Widget_AppCompat_ListPopupWindow 0x7f12024d
int style Widget_AppCompat_ListView 0x7f12024e
int style Widget_AppCompat_ListView_DropDown 0x7f12024f
int style Widget_AppCompat_ListView_Menu 0x7f120250
int style Widget_AppCompat_PopupMenu 0x7f120251
int style Widget_AppCompat_PopupMenu_Overflow 0x7f120252
int style Widget_AppCompat_PopupWindow 0x7f120253
int style Widget_AppCompat_ProgressBar 0x7f120254
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f120255
int style Widget_AppCompat_RatingBar 0x7f120256
int style Widget_AppCompat_RatingBar_Indicator 0x7f120257
int style Widget_AppCompat_RatingBar_Small 0x7f120258
int style Widget_AppCompat_SearchView 0x7f120259
int style Widget_AppCompat_SearchView_ActionBar 0x7f12025a
int style Widget_AppCompat_SeekBar 0x7f12025b
int style Widget_AppCompat_SeekBar_Discrete 0x7f12025c
int style Widget_AppCompat_Spinner 0x7f12025d
int style Widget_AppCompat_Spinner_DropDown 0x7f12025e
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f12025f
int style Widget_AppCompat_Spinner_Underlined 0x7f120260
int style Widget_AppCompat_TextView 0x7f120261
int style Widget_AppCompat_TextView_SpinnerItem 0x7f120262
int style Widget_AppCompat_Toolbar 0x7f120263
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f120264
int style Widget_Autofill 0x7f120265
int style Widget_Autofill_InlineSuggestionChip 0x7f120266
int style Widget_Autofill_InlineSuggestionEndIconStyle 0x7f120267
int style Widget_Autofill_InlineSuggestionStartIconStyle 0x7f120268
int style Widget_Autofill_InlineSuggestionSubtitle 0x7f120269
int style Widget_Autofill_InlineSuggestionTitle 0x7f12026a
int style Widget_Compat_NotificationActionContainer 0x7f12026b
int style Widget_Compat_NotificationActionText 0x7f12026c
int style Widget_Design_AppBarLayout 0x7f12026d
int style Widget_Design_BottomNavigationView 0x7f12026e
int style Widget_Design_BottomSheet_Modal 0x7f12026f
int style Widget_Design_CollapsingToolbar 0x7f120270
int style Widget_Design_FloatingActionButton 0x7f120271
int style Widget_Design_NavigationView 0x7f120272
int style Widget_Design_ScrimInsetsFrameLayout 0x7f120273
int style Widget_Design_Snackbar 0x7f120274
int style Widget_Design_TabLayout 0x7f120275
int style Widget_Design_TextInputEditText 0x7f120276
int style Widget_Design_TextInputLayout 0x7f120277
int style Widget_MaterialComponents_ActionBar_Primary 0x7f120278
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f120279
int style Widget_MaterialComponents_ActionBar_Solid 0x7f12027a
int style Widget_MaterialComponents_ActionBar_Surface 0x7f12027b
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f12027c
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f12027d
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f12027e
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f12027f
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f120280
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f120281
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f120282
int style Widget_MaterialComponents_Badge 0x7f120283
int style Widget_MaterialComponents_BottomAppBar 0x7f120284
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f120285
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f120286
int style Widget_MaterialComponents_BottomNavigationView 0x7f120287
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f120288
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f120289
int style Widget_MaterialComponents_BottomSheet 0x7f12028a
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f12028b
int style Widget_MaterialComponents_Button 0x7f12028c
int style Widget_MaterialComponents_Button_Icon 0x7f12028d
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f12028e
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f12028f
int style Widget_MaterialComponents_Button_TextButton 0x7f120290
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f120291
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f120292
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f120293
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f120294
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f120295
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f120296
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f120297
int style Widget_MaterialComponents_CardView 0x7f120298
int style Widget_MaterialComponents_CheckedTextView 0x7f120299
int style Widget_MaterialComponents_Chip_Action 0x7f12029a
int style Widget_MaterialComponents_Chip_Choice 0x7f12029b
int style Widget_MaterialComponents_Chip_Entry 0x7f12029c
int style Widget_MaterialComponents_Chip_Filter 0x7f12029d
int style Widget_MaterialComponents_ChipGroup 0x7f12029e
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f12029f
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f1202a0
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f1202a1
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f1202a2
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f1202a3
int style Widget_MaterialComponents_FloatingActionButton 0x7f1202a4
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f1202a5
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f1202a6
int style Widget_MaterialComponents_MaterialCalendar 0x7f1202a7
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f1202a8
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f1202a9
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f1202aa
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f1202ab
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f1202ac
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f1202ad
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f1202ae
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f1202af
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f1202b0
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f1202b1
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f1202b2
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f1202b3
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f1202b4
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f1202b5
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f1202b6
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f1202b7
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f1202b8
int style Widget_MaterialComponents_NavigationView 0x7f1202b9
int style Widget_MaterialComponents_PopupMenu 0x7f1202ba
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f1202bb
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f1202bc
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f1202bd
int style Widget_MaterialComponents_ShapeableImageView 0x7f1202be
int style Widget_MaterialComponents_Slider 0x7f1202bf
int style Widget_MaterialComponents_Snackbar 0x7f1202c0
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f1202c1
int style Widget_MaterialComponents_Snackbar_TextView 0x7f1202c2
int style Widget_MaterialComponents_TabLayout 0x7f1202c3
int style Widget_MaterialComponents_TabLayout_Colored 0x7f1202c4
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f1202c5
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f1202c6
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f1202c7
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f1202c8
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f1202c9
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f1202ca
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f1202cb
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f1202cc
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f1202cd
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f1202ce
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f1202cf
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f1202d0
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f1202d1
int style Widget_MaterialComponents_TextView 0x7f1202d2
int style Widget_MaterialComponents_Toolbar 0x7f1202d3
int style Widget_MaterialComponents_Toolbar_Primary 0x7f1202d4
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f1202d5
int style Widget_MaterialComponents_Toolbar_Surface 0x7f1202d6
int style Widget_MaterialComponents_Tooltip 0x7f1202d7
int style Widget_Support_CoordinatorLayout 0x7f1202d8
int style redboxButton 0x7f1202d9
int[] styleable ActionBar { 0x7f040040, 0x7f040048, 0x7f040049, 0x7f0400c7, 0x7f0400c8, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400cc, 0x7f0400e6, 0x7f0400f2, 0x7f0400f3, 0x7f040106, 0x7f04014d, 0x7f040153, 0x7f040159, 0x7f04015a, 0x7f04015d, 0x7f040169, 0x7f040177, 0x7f0401ad, 0x7f0401d0, 0x7f0401f3, 0x7f0401fe, 0x7f0401ff, 0x7f040255, 0x7f040258, 0x7f0402ac, 0x7f0402b6 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f040040, 0x7f040048, 0x7f0400a9, 0x7f04014d, 0x7f040258, 0x7f0402b6 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f04011c, 0x7f04016a }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f040000, 0x7f0400e7, 0x7f0400e8, 0x7f04027c }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AlertDialog { 0x010100f2, 0x7f040073, 0x7f040074, 0x7f0401a2, 0x7f0401a3, 0x7f0401cc, 0x7f040232, 0x7f040234 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f040106, 0x7f04011d, 0x7f04019a, 0x7f04019b, 0x7f04024f }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollTargetViewId 6
int styleable AppBarLayout_statusBarForeground 7
int[] styleable AppBarLayoutStates { 0x7f040249, 0x7f04024a, 0x7f04024c, 0x7f04024d }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f040198, 0x7f040199 }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f040240, 0x7f0402aa, 0x7f0402ab }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f040036, 0x7f040037, 0x7f040038, 0x7f040039, 0x7f04003a, 0x7f0400f7, 0x7f0400f8, 0x7f0400f9, 0x7f0400fa, 0x7f0400fc, 0x7f0400fd, 0x7f0400fe, 0x7f0400ff, 0x7f040109, 0x7f040136, 0x7f040139, 0x7f040143, 0x7f04018c, 0x7f04019c, 0x7f04027d, 0x7f040299 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040001, 0x7f040002, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000d, 0x7f04000f, 0x7f040010, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040020, 0x7f040021, 0x7f040025, 0x7f040029, 0x7f04002a, 0x7f04002b, 0x7f04002c, 0x7f040035, 0x7f04005c, 0x7f04006c, 0x7f04006d, 0x7f04006e, 0x7f04006f, 0x7f040070, 0x7f040076, 0x7f040077, 0x7f040085, 0x7f04008c, 0x7f0400af, 0x7f0400b0, 0x7f0400b1, 0x7f0400b2, 0x7f0400b3, 0x7f0400b4, 0x7f0400b5, 0x7f0400bc, 0x7f0400bd, 0x7f0400c4, 0x7f0400d3, 0x7f0400ef, 0x7f0400f0, 0x7f0400f1, 0x7f0400f4, 0x7f0400f6, 0x7f040101, 0x7f040102, 0x7f040103, 0x7f040104, 0x7f040105, 0x7f040159, 0x7f040168, 0x7f04019e, 0x7f04019f, 0x7f0401a0, 0x7f0401a1, 0x7f0401a4, 0x7f0401a5, 0x7f0401a6, 0x7f0401a7, 0x7f0401a8, 0x7f0401a9, 0x7f0401aa, 0x7f0401ab, 0x7f0401ac, 0x7f0401df, 0x7f0401e0, 0x7f0401e1, 0x7f0401f2, 0x7f0401f4, 0x7f040203, 0x7f040205, 0x7f040206, 0x7f040207, 0x7f040223, 0x7f040224, 0x7f040225, 0x7f040226, 0x7f04023d, 0x7f04023e, 0x7f040260, 0x7f040288, 0x7f04028a, 0x7f04028b, 0x7f04028c, 0x7f04028e, 0x7f04028f, 0x7f040290, 0x7f040291, 0x7f040294, 0x7f040295, 0x7f0402b8, 0x7f0402b9, 0x7f0402ba, 0x7f0402bb, 0x7f0402cd, 0x7f0402cf, 0x7f0402d0, 0x7f0402d1, 0x7f0402d2, 0x7f0402d3, 0x7f0402d4, 0x7f0402d5, 0x7f0402d6, 0x7f0402d7, 0x7f0402d8 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable Autofill_InlineSuggestion { 0x7f04003b, 0x7f04003c, 0x7f04003d, 0x7f04003e, 0x7f04003f, 0x7f04016c }
int styleable Autofill_InlineSuggestion_autofillInlineSuggestionChip 0
int styleable Autofill_InlineSuggestion_autofillInlineSuggestionEndIconStyle 1
int styleable Autofill_InlineSuggestion_autofillInlineSuggestionStartIconStyle 2
int styleable Autofill_InlineSuggestion_autofillInlineSuggestionSubtitle 3
int styleable Autofill_InlineSuggestion_autofillInlineSuggestionTitle 4
int styleable Autofill_InlineSuggestion_isAutofillInlineSuggestionTheme 5
int[] styleable Badge { 0x7f040041, 0x7f04004c, 0x7f04004e, 0x7f04015b, 0x7f0401c5, 0x7f0401d4, 0x7f0402cb }
int styleable Badge_backgroundColor 0
int styleable Badge_badgeGravity 1
int styleable Badge_badgeTextColor 2
int styleable Badge_horizontalOffset 3
int styleable Badge_maxCharacterCount 4
int styleable Badge_number 5
int styleable Badge_verticalOffset 6
int[] styleable BottomAppBar { 0x7f04004a, 0x7f040106, 0x7f040127, 0x7f040128, 0x7f040129, 0x7f04012a, 0x7f04012b, 0x7f040154, 0x7f0401d9, 0x7f0401db, 0x7f0401dc }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_elevation 1
int styleable BottomAppBar_fabAlignmentMode 2
int styleable BottomAppBar_fabAnimationMode 3
int styleable BottomAppBar_fabCradleMargin 4
int styleable BottomAppBar_fabCradleRoundedCornerRadius 5
int styleable BottomAppBar_fabCradleVerticalOffset 6
int styleable BottomAppBar_hideOnScroll 7
int styleable BottomAppBar_paddingBottomSystemWindowInsets 8
int styleable BottomAppBar_paddingLeftSystemWindowInsets 9
int styleable BottomAppBar_paddingRightSystemWindowInsets 10
int[] styleable BottomNavigationView { 0x7f04004a, 0x7f040106, 0x7f04016f, 0x7f040172, 0x7f040174, 0x7f040175, 0x7f040178, 0x7f040184, 0x7f040185, 0x7f040186, 0x7f04018b, 0x7f0401c9 }
int styleable BottomNavigationView_backgroundTint 0
int styleable BottomNavigationView_elevation 1
int styleable BottomNavigationView_itemBackground 2
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 3
int styleable BottomNavigationView_itemIconSize 4
int styleable BottomNavigationView_itemIconTint 5
int styleable BottomNavigationView_itemRippleColor 6
int styleable BottomNavigationView_itemTextAppearanceActive 7
int styleable BottomNavigationView_itemTextAppearanceInactive 8
int styleable BottomNavigationView_itemTextColor 9
int styleable BottomNavigationView_labelVisibilityMode 10
int styleable BottomNavigationView_menu 11
int[] styleable BottomSheetBehavior_Layout { 0x01010440, 0x7f04004a, 0x7f040052, 0x7f040053, 0x7f040054, 0x7f040055, 0x7f040056, 0x7f040058, 0x7f040059, 0x7f04005a, 0x7f040147, 0x7f040228, 0x7f04022b }
int styleable BottomSheetBehavior_Layout_android_elevation 0
int styleable BottomSheetBehavior_Layout_backgroundTint 1
int styleable BottomSheetBehavior_Layout_behavior_draggable 2
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 3
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 4
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 5
int styleable BottomSheetBehavior_Layout_behavior_hideable 6
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 7
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 8
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 9
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 10
int styleable BottomSheetBehavior_Layout_shapeAppearance 11
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 12
int[] styleable ButtonBarLayout { 0x7f04002d }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f040202, 0x7f04022d }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f04007a, 0x7f04007b, 0x7f04007c, 0x7f04007e, 0x7f04007f, 0x7f040080, 0x7f0400cd, 0x7f0400ce, 0x7f0400cf, 0x7f0400d0, 0x7f0400d1 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable CheckedTextView { 0x01010108, 0x7f040082, 0x7f040083, 0x7f040084 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x01010034, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f040088, 0x7f040089, 0x7f04008a, 0x7f04008b, 0x7f04008d, 0x7f04008e, 0x7f04008f, 0x7f040091, 0x7f040092, 0x7f040093, 0x7f040094, 0x7f040095, 0x7f040096, 0x7f040097, 0x7f04009c, 0x7f04009d, 0x7f04009e, 0x7f0400a0, 0x7f0400a2, 0x7f0400a3, 0x7f0400a4, 0x7f0400a5, 0x7f0400a6, 0x7f0400a7, 0x7f0400a8, 0x7f040112, 0x7f040152, 0x7f04015e, 0x7f040162, 0x7f04020d, 0x7f040228, 0x7f04022b, 0x7f040230, 0x7f040296, 0x7f04029a }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textColor 1
int styleable Chip_android_ellipsize 2
int styleable Chip_android_maxWidth 3
int styleable Chip_android_text 4
int styleable Chip_android_checkable 5
int styleable Chip_checkedIcon 6
int styleable Chip_checkedIconEnabled 7
int styleable Chip_checkedIconTint 8
int styleable Chip_checkedIconVisible 9
int styleable Chip_chipBackgroundColor 10
int styleable Chip_chipCornerRadius 11
int styleable Chip_chipEndPadding 12
int styleable Chip_chipIcon 13
int styleable Chip_chipIconEnabled 14
int styleable Chip_chipIconSize 15
int styleable Chip_chipIconTint 16
int styleable Chip_chipIconVisible 17
int styleable Chip_chipMinHeight 18
int styleable Chip_chipMinTouchTargetSize 19
int styleable Chip_chipStartPadding 20
int styleable Chip_chipStrokeColor 21
int styleable Chip_chipStrokeWidth 22
int styleable Chip_chipSurfaceColor 23
int styleable Chip_closeIcon 24
int styleable Chip_closeIconEnabled 25
int styleable Chip_closeIconEndPadding 26
int styleable Chip_closeIconSize 27
int styleable Chip_closeIconStartPadding 28
int styleable Chip_closeIconTint 29
int styleable Chip_closeIconVisible 30
int styleable Chip_ensureMinTouchTargetSize 31
int styleable Chip_hideMotionSpec 32
int styleable Chip_iconEndPadding 33
int styleable Chip_iconStartPadding 34
int styleable Chip_rippleColor 35
int styleable Chip_shapeAppearance 36
int styleable Chip_shapeAppearanceOverlay 37
int styleable Chip_showMotionSpec 38
int styleable Chip_textEndPadding 39
int styleable Chip_textStartPadding 40
int[] styleable ChipGroup { 0x7f040087, 0x7f040098, 0x7f040099, 0x7f04009a, 0x7f040227, 0x7f040235, 0x7f040236 }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CollapsingToolbarLayout { 0x7f0400ac, 0x7f0400ad, 0x7f0400d2, 0x7f04011e, 0x7f04011f, 0x7f040120, 0x7f040121, 0x7f040122, 0x7f040123, 0x7f040124, 0x7f0401c7, 0x7f04021e, 0x7f040220, 0x7f040250, 0x7f0402ac, 0x7f0402ad, 0x7f0402b7 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_maxLines 10
int styleable CollapsingToolbarLayout_scrimAnimationDuration 11
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 12
int styleable CollapsingToolbarLayout_statusBarScrim 13
int styleable CollapsingToolbarLayout_title 14
int styleable CollapsingToolbarLayout_titleEnabled 15
int styleable CollapsingToolbarLayout_toolbarId 16
int[] styleable CollapsingToolbarLayout_Layout { 0x7f040193, 0x7f040194 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f04002e, 0x7f040188 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f040071, 0x7f040078, 0x7f040079 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable CoordinatorLayout { 0x7f040187, 0x7f04024e }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f040190, 0x7f040191, 0x7f040192, 0x7f040195, 0x7f040196, 0x7f040197 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DrawerArrowToggle { 0x7f040033, 0x7f040034, 0x7f04004f, 0x7f0400ae, 0x7f0400fb, 0x7f040146, 0x7f04023c, 0x7f04029d }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable ExtendedFloatingActionButton { 0x7f040106, 0x7f040125, 0x7f040152, 0x7f040230, 0x7f040233 }
int styleable ExtendedFloatingActionButton_elevation 0
int styleable ExtendedFloatingActionButton_extendMotionSpec 1
int styleable ExtendedFloatingActionButton_hideMotionSpec 2
int styleable ExtendedFloatingActionButton_showMotionSpec 3
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 4
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f040050, 0x7f040051 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f04004a, 0x7f04004b, 0x7f04005b, 0x7f040106, 0x7f040112, 0x7f04012c, 0x7f04012d, 0x7f040152, 0x7f04015c, 0x7f0401c6, 0x7f0401fa, 0x7f04020d, 0x7f040228, 0x7f04022b, 0x7f040230, 0x7f0402c8 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f040050 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f040180, 0x7f04019d }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f04013a, 0x7f04013b, 0x7f04013c, 0x7f04013d, 0x7f04013e, 0x7f04013f, 0x7f040140, 0x7f040141 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f040138, 0x7f040142, 0x7f040143, 0x7f040144, 0x7f0402c6 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f040145 }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GenericDraweeHierarchy { 0x7f040027, 0x7f040042, 0x7f04012e, 0x7f04012f, 0x7f040130, 0x7f0401d7, 0x7f0401e7, 0x7f0401e8, 0x7f0401f9, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f04020a, 0x7f04020b, 0x7f04020e, 0x7f04020f, 0x7f040210, 0x7f040211, 0x7f040212, 0x7f040213, 0x7f040214, 0x7f040215, 0x7f040216, 0x7f040217, 0x7f040218, 0x7f040219, 0x7f04021a, 0x7f04021b, 0x7f0402cc }
int styleable GenericDraweeHierarchy_actualImageScaleType 0
int styleable GenericDraweeHierarchy_backgroundImage 1
int styleable GenericDraweeHierarchy_fadeDuration 2
int styleable GenericDraweeHierarchy_failureImage 3
int styleable GenericDraweeHierarchy_failureImageScaleType 4
int styleable GenericDraweeHierarchy_overlayImage 5
int styleable GenericDraweeHierarchy_placeholderImage 6
int styleable GenericDraweeHierarchy_placeholderImageScaleType 7
int styleable GenericDraweeHierarchy_pressedStateOverlayImage 8
int styleable GenericDraweeHierarchy_progressBarAutoRotateInterval 9
int styleable GenericDraweeHierarchy_progressBarImage 10
int styleable GenericDraweeHierarchy_progressBarImageScaleType 11
int styleable GenericDraweeHierarchy_retryImage 12
int styleable GenericDraweeHierarchy_retryImageScaleType 13
int styleable GenericDraweeHierarchy_roundAsCircle 14
int styleable GenericDraweeHierarchy_roundBottomEnd 15
int styleable GenericDraweeHierarchy_roundBottomLeft 16
int styleable GenericDraweeHierarchy_roundBottomRight 17
int styleable GenericDraweeHierarchy_roundBottomStart 18
int styleable GenericDraweeHierarchy_roundTopEnd 19
int styleable GenericDraweeHierarchy_roundTopLeft 20
int styleable GenericDraweeHierarchy_roundTopRight 21
int styleable GenericDraweeHierarchy_roundTopStart 22
int styleable GenericDraweeHierarchy_roundWithOverlayColor 23
int styleable GenericDraweeHierarchy_roundedCornerRadius 24
int styleable GenericDraweeHierarchy_roundingBorderColor 25
int styleable GenericDraweeHierarchy_roundingBorderPadding 26
int styleable GenericDraweeHierarchy_roundingBorderWidth 27
int styleable GenericDraweeHierarchy_viewAspectRatio 28
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable Insets { 0x7f0401d9, 0x7f0401db, 0x7f0401dc }
int styleable Insets_paddingBottomSystemWindowInsets 0
int styleable Insets_paddingLeftSystemWindowInsets 1
int styleable Insets_paddingRightSystemWindowInsets 2
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f0400f3, 0x7f0400f5, 0x7f0401c8, 0x7f04022f }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable LoadingImageView { 0x7f0400a1, 0x7f040166, 0x7f040167 }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MaterialAlertDialog { 0x7f040043, 0x7f040044, 0x7f040045, 0x7f040046 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int[] styleable MaterialAlertDialogTheme { 0x7f0401af, 0x7f0401b0, 0x7f0401b1, 0x7f0401b2, 0x7f0401b3 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 4
int[] styleable MaterialAutoCompleteTextView { 0x01010220 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f04004a, 0x7f04004b, 0x7f0400da, 0x7f040106, 0x7f04015d, 0x7f04015f, 0x7f040160, 0x7f040161, 0x7f040163, 0x7f040164, 0x7f04020d, 0x7f040228, 0x7f04022b, 0x7f040251, 0x7f040252 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int[] styleable MaterialButtonToggleGroup { 0x7f040086, 0x7f040227, 0x7f040236 }
int styleable MaterialButtonToggleGroup_checkedButton 0
int styleable MaterialButtonToggleGroup_selectionRequired 1
int styleable MaterialButtonToggleGroup_singleSelection 2
int[] styleable MaterialCalendar { 0x0101020d, 0x7f0400e9, 0x7f0400ea, 0x7f0400eb, 0x7f0400ec, 0x7f040204, 0x7f0402d9, 0x7f0402da, 0x7f0402db }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_dayInvalidStyle 1
int styleable MaterialCalendar_daySelectedStyle 2
int styleable MaterialCalendar_dayStyle 3
int styleable MaterialCalendar_dayTodayStyle 4
int styleable MaterialCalendar_rangeFillColor 5
int styleable MaterialCalendar_yearSelectedStyle 6
int styleable MaterialCalendar_yearStyle 7
int styleable MaterialCalendar_yearTodayStyle 8
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f040170, 0x7f040179, 0x7f04017a, 0x7f040181, 0x7f040182, 0x7f040186 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f04007d, 0x7f040088, 0x7f04008a, 0x7f04020d, 0x7f040228, 0x7f04022b, 0x7f04024b, 0x7f040251, 0x7f040252 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconTint 3
int styleable MaterialCardView_rippleColor 4
int styleable MaterialCardView_shapeAppearance 5
int styleable MaterialCardView_shapeAppearanceOverlay 6
int styleable MaterialCardView_state_dragged 7
int styleable MaterialCardView_strokeColor 8
int styleable MaterialCardView_strokeWidth 9
int[] styleable MaterialCheckBox { 0x7f040078, 0x7f0402c9 }
int styleable MaterialCheckBox_buttonTint 0
int styleable MaterialCheckBox_useMaterialThemeColors 1
int[] styleable MaterialRadioButton { 0x7f040078, 0x7f0402c9 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f040228, 0x7f04022b }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialTextAppearance { 0x0101057f, 0x7f04019c }
int styleable MaterialTextAppearance_android_lineHeight 0
int styleable MaterialTextAppearance_lineHeight 1
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f04019c }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f04000e, 0x7f040022, 0x7f040024, 0x7f04002f, 0x7f0400c6, 0x7f040163, 0x7f040164, 0x7f0401d5, 0x7f04022e, 0x7f0402bd }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0401f8, 0x7f040253 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable NavAction { 0x010100d0, 0x7f0400ee, 0x7f040113, 0x7f04011b, 0x7f04018d, 0x7f0401ec, 0x7f0401ed, 0x7f0401ee, 0x7f0401ef, 0x7f0401f0, 0x7f040209 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f040032, 0x7f0401d3 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f040000, 0x7f0401ca, 0x7f0402c7 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f040242 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f0401cd }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f040149 }
int styleable NavInclude_graph 0
int[] styleable NavigationView { 0x010100d4, 0x010100dd, 0x0101011f, 0x7f040106, 0x7f04014c, 0x7f04016f, 0x7f040171, 0x7f040173, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f040179, 0x7f04017a, 0x7f04017b, 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f04017f, 0x7f040183, 0x7f040186, 0x7f0401c9 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconSize 8
int styleable NavigationView_itemIconTint 9
int styleable NavigationView_itemMaxLines 10
int styleable NavigationView_itemShapeAppearance 11
int styleable NavigationView_itemShapeAppearanceOverlay 12
int styleable NavigationView_itemShapeFillColor 13
int styleable NavigationView_itemShapeInsetBottom 14
int styleable NavigationView_itemShapeInsetEnd 15
int styleable NavigationView_itemShapeInsetStart 16
int styleable NavigationView_itemShapeInsetTop 17
int styleable NavigationView_itemTextAppearance 18
int styleable NavigationView_itemTextColor 19
int styleable NavigationView_menu 20
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f04021c }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0401d6 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f040248 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RangeSlider { 0x7f0402ca }
int styleable RangeSlider_values 0
int[] styleable RecycleListView { 0x7f0401d8, 0x7f0401de }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f040131, 0x7f040132, 0x7f040133, 0x7f040134, 0x7f040135, 0x7f04018f, 0x7f04020c, 0x7f04023b, 0x7f040241 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f04016b }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f040057 }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f0400a2, 0x7f0400c5, 0x7f0400ed, 0x7f040148, 0x7f040165, 0x7f04018e, 0x7f040200, 0x7f040201, 0x7f040221, 0x7f040222, 0x7f040254, 0x7f04025c, 0x7f0402ce }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable ShapeAppearance { 0x7f0400d5, 0x7f0400d6, 0x7f0400d7, 0x7f0400d8, 0x7f0400d9, 0x7f0400db, 0x7f0400dc, 0x7f0400dd, 0x7f0400de, 0x7f0400df }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f040228, 0x7f04022b, 0x7f040251, 0x7f040252 }
int styleable ShapeableImageView_shapeAppearance 0
int styleable ShapeableImageView_shapeAppearanceOverlay 1
int styleable ShapeableImageView_strokeColor 2
int styleable ShapeableImageView_strokeWidth 3
int[] styleable SignInButton { 0x7f040075, 0x7f0400c0, 0x7f04021d }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable SimpleDraweeView { 0x7f040026, 0x7f040028, 0x7f040042, 0x7f04012e, 0x7f04012f, 0x7f040130, 0x7f0401d7, 0x7f0401e7, 0x7f0401e8, 0x7f0401f9, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f04020a, 0x7f04020b, 0x7f04020e, 0x7f04020f, 0x7f040210, 0x7f040211, 0x7f040212, 0x7f040213, 0x7f040214, 0x7f040215, 0x7f040216, 0x7f040217, 0x7f040218, 0x7f040219, 0x7f04021a, 0x7f04021b, 0x7f0402cc }
int styleable SimpleDraweeView_actualImageResource 0
int styleable SimpleDraweeView_actualImageUri 1
int styleable SimpleDraweeView_backgroundImage 2
int styleable SimpleDraweeView_fadeDuration 3
int styleable SimpleDraweeView_failureImage 4
int styleable SimpleDraweeView_failureImageScaleType 5
int styleable SimpleDraweeView_overlayImage 6
int styleable SimpleDraweeView_placeholderImage 7
int styleable SimpleDraweeView_placeholderImageScaleType 8
int styleable SimpleDraweeView_pressedStateOverlayImage 9
int styleable SimpleDraweeView_progressBarAutoRotateInterval 10
int styleable SimpleDraweeView_progressBarImage 11
int styleable SimpleDraweeView_progressBarImageScaleType 12
int styleable SimpleDraweeView_retryImage 13
int styleable SimpleDraweeView_retryImageScaleType 14
int styleable SimpleDraweeView_roundAsCircle 15
int styleable SimpleDraweeView_roundBottomEnd 16
int styleable SimpleDraweeView_roundBottomLeft 17
int styleable SimpleDraweeView_roundBottomRight 18
int styleable SimpleDraweeView_roundBottomStart 19
int styleable SimpleDraweeView_roundTopEnd 20
int styleable SimpleDraweeView_roundTopLeft 21
int styleable SimpleDraweeView_roundTopRight 22
int styleable SimpleDraweeView_roundTopStart 23
int styleable SimpleDraweeView_roundWithOverlayColor 24
int styleable SimpleDraweeView_roundedCornerRadius 25
int styleable SimpleDraweeView_roundingBorderColor 26
int styleable SimpleDraweeView_roundingBorderPadding 27
int styleable SimpleDraweeView_roundingBorderWidth 28
int styleable SimpleDraweeView_viewAspectRatio 29
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f04014a, 0x7f04014b, 0x7f040189, 0x7f04018a, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2 }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_thumbColor 9
int styleable Slider_thumbElevation 10
int styleable Slider_thumbRadius 11
int styleable Slider_tickColor 12
int styleable Slider_tickColorActive 13
int styleable Slider_tickColorInactive 14
int styleable Slider_trackColor 15
int styleable Slider_trackColorActive 16
int styleable Slider_trackColorInactive 17
int styleable Slider_trackHeight 18
int[] styleable Snackbar { 0x7f040238, 0x7f040239, 0x7f04023a }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f040023, 0x7f040030, 0x7f040047, 0x7f04004a, 0x7f04004b, 0x7f040106, 0x7f0401c3 }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0401f3 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwipeRefreshLayout { 0x7f04025d }
int styleable SwipeRefreshLayout_swipeRefreshLayoutProgressSpinnerBackgroundColor 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f040231, 0x7f04023f, 0x7f04025e, 0x7f04025f, 0x7f040261, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402be, 0x7f0402c3, 0x7f0402c4 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f0402c9 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f040262, 0x7f040263, 0x7f040264, 0x7f040265, 0x7f040266, 0x7f040267, 0x7f040268, 0x7f040269, 0x7f04026a, 0x7f04026b, 0x7f04026c, 0x7f04026d, 0x7f04026e, 0x7f04026f, 0x7f040270, 0x7f040271, 0x7f040272, 0x7f040273, 0x7f040274, 0x7f040275, 0x7f040276, 0x7f040277, 0x7f040279, 0x7f04027a, 0x7f04027b }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorColor 7
int styleable TabLayout_tabIndicatorFullWidth 8
int styleable TabLayout_tabIndicatorGravity 9
int styleable TabLayout_tabIndicatorHeight 10
int styleable TabLayout_tabInlineLabel 11
int styleable TabLayout_tabMaxWidth 12
int styleable TabLayout_tabMinWidth 13
int styleable TabLayout_tabMode 14
int styleable TabLayout_tabPadding 15
int styleable TabLayout_tabPaddingBottom 16
int styleable TabLayout_tabPaddingEnd 17
int styleable TabLayout_tabPaddingStart 18
int styleable TabLayout_tabPaddingTop 19
int styleable TabLayout_tabRippleColor 20
int styleable TabLayout_tabSelectedTextColor 21
int styleable TabLayout_tabTextAppearance 22
int styleable TabLayout_tabTextColor 23
int styleable TabLayout_tabUnboundedRipple 24
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f040139, 0x7f040143, 0x7f04027d, 0x7f040299 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextInputEditText { 0x7f040297 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x01010150, 0x7f040061, 0x7f040062, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f040066, 0x7f040067, 0x7f040068, 0x7f040069, 0x7f04006a, 0x7f04006b, 0x7f0400e0, 0x7f0400e1, 0x7f0400e2, 0x7f0400e3, 0x7f0400e4, 0x7f0400e5, 0x7f04010a, 0x7f04010b, 0x7f04010c, 0x7f04010d, 0x7f04010e, 0x7f04010f, 0x7f040114, 0x7f040115, 0x7f040116, 0x7f040117, 0x7f040118, 0x7f040119, 0x7f04011a, 0x7f04014e, 0x7f04014f, 0x7f040150, 0x7f040151, 0x7f040155, 0x7f040156, 0x7f040157, 0x7f040158, 0x7f0401e2, 0x7f0401e3, 0x7f0401e4, 0x7f0401e5, 0x7f0401e6, 0x7f0401e9, 0x7f0401ea, 0x7f0401eb, 0x7f0401f5, 0x7f0401f6, 0x7f0401f7, 0x7f040228, 0x7f04022b, 0x7f040243, 0x7f040244, 0x7f040245, 0x7f040246, 0x7f040247, 0x7f040259, 0x7f04025a, 0x7f04025b }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_hint 2
int styleable TextInputLayout_boxBackgroundColor 3
int styleable TextInputLayout_boxBackgroundMode 4
int styleable TextInputLayout_boxCollapsedPaddingTop 5
int styleable TextInputLayout_boxCornerRadiusBottomEnd 6
int styleable TextInputLayout_boxCornerRadiusBottomStart 7
int styleable TextInputLayout_boxCornerRadiusTopEnd 8
int styleable TextInputLayout_boxCornerRadiusTopStart 9
int styleable TextInputLayout_boxStrokeColor 10
int styleable TextInputLayout_boxStrokeErrorColor 11
int styleable TextInputLayout_boxStrokeWidth 12
int styleable TextInputLayout_boxStrokeWidthFocused 13
int styleable TextInputLayout_counterEnabled 14
int styleable TextInputLayout_counterMaxLength 15
int styleable TextInputLayout_counterOverflowTextAppearance 16
int styleable TextInputLayout_counterOverflowTextColor 17
int styleable TextInputLayout_counterTextAppearance 18
int styleable TextInputLayout_counterTextColor 19
int styleable TextInputLayout_endIconCheckable 20
int styleable TextInputLayout_endIconContentDescription 21
int styleable TextInputLayout_endIconDrawable 22
int styleable TextInputLayout_endIconMode 23
int styleable TextInputLayout_endIconTint 24
int styleable TextInputLayout_endIconTintMode 25
int styleable TextInputLayout_errorContentDescription 26
int styleable TextInputLayout_errorEnabled 27
int styleable TextInputLayout_errorIconDrawable 28
int styleable TextInputLayout_errorIconTint 29
int styleable TextInputLayout_errorIconTintMode 30
int styleable TextInputLayout_errorTextAppearance 31
int styleable TextInputLayout_errorTextColor 32
int styleable TextInputLayout_helperText 33
int styleable TextInputLayout_helperTextEnabled 34
int styleable TextInputLayout_helperTextTextAppearance 35
int styleable TextInputLayout_helperTextTextColor 36
int styleable TextInputLayout_hintAnimationEnabled 37
int styleable TextInputLayout_hintEnabled 38
int styleable TextInputLayout_hintTextAppearance 39
int styleable TextInputLayout_hintTextColor 40
int styleable TextInputLayout_passwordToggleContentDescription 41
int styleable TextInputLayout_passwordToggleDrawable 42
int styleable TextInputLayout_passwordToggleEnabled 43
int styleable TextInputLayout_passwordToggleTint 44
int styleable TextInputLayout_passwordToggleTintMode 45
int styleable TextInputLayout_placeholderText 46
int styleable TextInputLayout_placeholderTextAppearance 47
int styleable TextInputLayout_placeholderTextColor 48
int styleable TextInputLayout_prefixText 49
int styleable TextInputLayout_prefixTextAppearance 50
int styleable TextInputLayout_prefixTextColor 51
int styleable TextInputLayout_shapeAppearance 52
int styleable TextInputLayout_shapeAppearanceOverlay 53
int styleable TextInputLayout_startIconCheckable 54
int styleable TextInputLayout_startIconContentDescription 55
int styleable TextInputLayout_startIconDrawable 56
int styleable TextInputLayout_startIconTint 57
int styleable TextInputLayout_startIconTintMode 58
int styleable TextInputLayout_suffixText 59
int styleable TextInputLayout_suffixTextAppearance 60
int styleable TextInputLayout_suffixTextColor 61
int[] styleable ThemeEnforcement { 0x01010034, 0x7f040110, 0x7f040111 }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f040072, 0x7f0400aa, 0x7f0400ab, 0x7f0400c7, 0x7f0400c8, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400cc, 0x7f0401ad, 0x7f0401ae, 0x7f0401c4, 0x7f0401c9, 0x7f0401ce, 0x7f0401cf, 0x7f0401f3, 0x7f040255, 0x7f040256, 0x7f040257, 0x7f0402ac, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f04004a }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_padding 1
int styleable Tooltip_android_layout_margin 2
int styleable Tooltip_android_minWidth 3
int styleable Tooltip_android_minHeight 4
int styleable Tooltip_android_text 5
int styleable Tooltip_backgroundTint 6
int[] styleable View { 0x01010000, 0x010100da, 0x7f0401da, 0x7f0401dd, 0x7f04029b }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f04004a, 0x7f04004b }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int xml file_system_provider_paths 0x7f140000
int xml image_share_filepaths 0x7f140001
int xml rn_dev_preferences 0x7f140002
int xml secure_store_backup_rules 0x7f140003
int xml secure_store_data_extraction_rules 0x7f140004
int xml standalone_badge 0x7f140005
int xml standalone_badge_gravity_bottom_end 0x7f140006
int xml standalone_badge_gravity_bottom_start 0x7f140007
int xml standalone_badge_gravity_top_start 0x7f140008
int xml standalone_badge_offset 0x7f140009
