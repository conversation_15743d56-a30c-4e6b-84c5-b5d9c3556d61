{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,10792", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,10867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,201,272,342,425,496,563,640,720,805,885,955,1038,1123,1198,1283,1369,1446,1520,1591,1678,1748,1827,1902", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "119,196,267,337,420,491,558,635,715,800,880,950,1033,1118,1193,1278,1364,1441,1515,1586,1673,1743,1822,1897,1974"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2797,4152,9307,9378,9448,9714,9785,9852,9929,10181,10266,10514,10709,11027,11112,11187,11346,11432,11584,11730,11801,11989,12205,12402,12477", "endColumns": "68,76,70,69,82,70,66,76,79,84,79,69,82,84,74,84,85,76,73,70,86,69,78,74,76", "endOffsets": "2861,4224,9373,9443,9526,9780,9847,9924,10004,10261,10341,10579,10787,11107,11182,11267,11427,11504,11653,11796,11883,12054,12279,12472,12549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3262,3359,3461,3559,3656,3758,3864,11888", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3354,3456,3554,3651,3753,3859,3970,11984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4229,4339,4494,4630,4735,4882,5012,5139,5392,5564,5671,5828,5962,6107,6274,6336,6400", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "4334,4489,4625,4730,4877,5007,5134,5240,5559,5666,5823,5957,6102,6269,6331,6395,6475"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6586,7315,7421,7528", "endColumns": "99,105,106,105", "endOffsets": "6681,7416,7523,7629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,229,366,472,564,709,822,935,1054,1183,1308,1452,1569,1712,1807,1966,2092,2216,2357,2495,2620,2715,2843,2934,3058,3156,3287", "endColumns": "173,136,105,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "224,361,467,559,704,817,930,1049,1178,1303,1447,1564,1707,1802,1961,2087,2211,2352,2490,2615,2710,2838,2929,3053,3151,3282,3382"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2951,3125,6480,6781,7057,7202,7634,7747,7866,7995,8120,8264,8381,8524,8619,8778,8904,9028,9169,10584,12725,12820,12948,13039,13163,13261,13392", "endColumns": "173,136,105,91,144,112,112,118,128,124,143,116,142,94,158,125,123,140,137,124,94,127,90,123,97,130,99", "endOffsets": "3120,3257,6581,6868,7197,7310,7742,7861,7990,8115,8259,8376,8519,8614,8773,8899,9023,9164,9302,10704,12815,12943,13034,13158,13256,13387,13487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,283,367,462,562,646,729,829,917,1001,1081,1169,1240,1324,1398,1473,1545,1623,1691", "endColumns": "84,92,83,94,99,83,82,99,87,83,79,87,70,83,73,74,71,77,67,117", "endOffsets": "185,278,362,457,557,641,724,824,912,996,1076,1164,1235,1319,1393,1468,1540,1618,1686,1804"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,3975,4068,6686,6873,6973,9531,9614,10009,10097,10346,10426,10872,10943,11272,11509,11658,12059,12137,12284", "endColumns": "84,92,83,94,99,83,82,99,87,83,79,87,70,83,73,74,71,77,67,117", "endOffsets": "2946,4063,4147,6776,6968,7052,9609,9709,10092,10176,10421,10509,10938,11022,11341,11579,11725,12132,12200,12397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "139,223", "endColumns": "83,86", "endOffsets": "218,305"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "12554,12638", "endColumns": "83,86", "endOffsets": "12633,12720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5245", "endColumns": "146", "endOffsets": "5387"}}]}]}