{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,211,335,441,540,681,787,896,1006,1140,1263,1400,1519,1646,1741,1895,2023,2161,2292,2411,2536,2631,2758,2838,2950,3043,3168", "endColumns": "155,123,105,98,140,105,108,109,133,122,136,118,126,94,153,127,137,130,118,124,94,126,79,111,92,124,95", "endOffsets": "206,330,436,535,676,782,891,1001,1135,1258,1395,1514,1641,1736,1890,2018,2156,2287,2406,2531,2626,2753,2833,2945,3038,3163,3259"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,94,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2860,3016,6343,6656,6942,7083,7502,7611,7721,7855,7978,8115,8234,8361,8456,8610,8738,8876,9007,9924,11203,11298,11425,11505,11617,11710,11835", "endColumns": "155,123,105,98,140,105,108,109,133,122,136,118,126,94,153,127,137,130,118,124,94,126,79,111,92,124,95", "endOffsets": "3011,3135,6444,6750,7078,7184,7606,7716,7850,7973,8110,8229,8356,8451,8605,8733,8871,9002,9121,10044,11293,11420,11500,11612,11705,11830,11926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6449,7189,7287,7402", "endColumns": "97,97,114,99", "endOffsets": "6542,7282,7397,7497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "142,228", "endColumns": "85,84", "endOffsets": "223,308"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11032,11118", "endColumns": "85,84", "endOffsets": "11113,11198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "32,33,34,35,36,37,38,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3140,3236,3339,3438,3536,3637,3735,10666", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3231,3334,3433,3531,3632,3730,3841,10762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,10049", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,10126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4089,4197,4367,4497,4604,4756,4881,4989,5237,5389,5494,5660,5789,5960,6121,6189,6258", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "4192,4362,4492,4599,4751,4876,4984,5100,5384,5489,5655,5784,5955,6116,6184,6253,6338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,131,201,283,350,417,488", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "126,196,278,345,412,483,554"}, "to": {"startLines": "41,84,85,88,89,100,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4013,9126,9196,9457,9524,10449,10595", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "4084,9191,9273,9519,9586,10515,10661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,193,283,360,469,567,656,745,835,921,1004,1084,1168,1242,1330,1411,1486,1561,1639,1705", "endColumns": "87,89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "188,278,355,464,562,651,740,830,916,999,1079,1163,1237,1325,1406,1481,1556,1634,1700,1821"}, "to": {"startLines": "29,39,40,62,64,65,86,87,90,91,92,93,96,97,98,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2772,3846,3936,6547,6755,6853,9278,9367,9591,9677,9760,9840,10131,10205,10293,10374,10520,10767,10845,10911", "endColumns": "87,89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "2855,3931,4008,6651,6848,6937,9362,9452,9672,9755,9835,9919,10200,10288,10369,10444,10590,10840,10906,11027"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5105", "endColumns": "131", "endOffsets": "5232"}}]}]}