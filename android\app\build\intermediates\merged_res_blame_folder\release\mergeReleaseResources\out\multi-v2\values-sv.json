{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,339,448,534,679,792,909,1030,1169,1299,1421,1550,1686,1791,1949,2075,2217,2359,2486,2604,2698,2831,2924,3049,3155,3293", "endColumns": "157,125,108,85,144,112,116,120,138,129,121,128,135,104,157,125,141,141,126,117,93,132,92,124,105,137,97", "endOffsets": "208,334,443,529,674,787,904,1025,1164,1294,1416,1545,1681,1786,1944,2070,2212,2354,2481,2599,2693,2826,2919,3044,3150,3288,3386"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,126,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2925,3083,6407,6712,6985,7130,7554,7671,7792,7931,8061,8183,8312,8448,8553,8711,8837,8979,9121,10493,12544,12638,12771,12864,12989,13095,13233", "endColumns": "157,125,108,85,144,112,116,120,138,129,121,128,135,104,157,125,141,141,126,117,93,132,92,124,105,137,97", "endOffsets": "3078,3204,6511,6793,7125,7238,7666,7787,7926,8056,8178,8307,8443,8548,8706,8832,8974,9116,9243,10606,12633,12766,12859,12984,13090,13228,13326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6516,7243,7343,7456", "endColumns": "99,99,112,97", "endOffsets": "6611,7338,7451,7549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5195", "endColumns": "147", "endOffsets": "5338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "130,220", "endColumns": "89,88", "endOffsets": "215,304"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "12365,12455", "endColumns": "89,88", "endOffsets": "12450,12539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3209,3304,3406,3504,3603,3711,3816,11766", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3299,3401,3499,3598,3706,3811,3932,11862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,10693", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,10768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,274,362,458,557,645,721,809,898,979,1065,1155,1224,1310,1384,1455,1525,1603,1670", "endColumns": "75,92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "176,269,357,453,552,640,716,804,893,974,1060,1150,1219,1305,1379,1450,1520,1598,1665,1785"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2849,3937,4030,6616,6798,6897,9468,9544,9917,10006,10248,10334,10773,10842,11171,11401,11542,11947,12025,12167", "endColumns": "75,92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "2920,4025,4113,6707,6892,6980,9539,9627,10001,10082,10329,10419,10837,10923,11240,11467,11607,12020,12087,12282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4201,4308,4465,4592,4702,4843,4968,5091,5343,5491,5599,5761,5889,6043,6199,6265,6328", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "4303,4460,4587,4697,4838,4963,5086,5190,5486,5594,5756,5884,6038,6194,6260,6323,6402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,282,350,431,498,565,639,716,798,877,946,1028,1111,1188,1271,1350,1427,1497,1566,1651,1731,1806", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "123,206,277,345,426,493,560,634,711,793,872,941,1023,1106,1183,1266,1345,1422,1492,1561,1646,1726,1801,1879"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2776,4118,9248,9319,9387,9632,9699,9766,9840,10087,10169,10424,10611,10928,11011,11088,11245,11324,11472,11612,11681,11867,12092,12287", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "2844,4196,9314,9382,9463,9694,9761,9835,9912,10164,10243,10488,10688,11006,11083,11166,11319,11396,11537,11676,11761,11942,12162,12360"}}]}]}