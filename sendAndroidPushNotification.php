<?php

// ✅ VERSÃO CORRIGIDA PARA EXPO PUSH NOTIFICATIONS
// Inclui configuração FCM e fallback para notificações locais

define('_HOST_EXPO_PUSH_', 'https://exp.host/--/api/v2/push/send');
define('_FCM_SERVER_KEY_', 'AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk'); // SUBSTITUA PELA SUA CHAVE FCM

class sendAndroidPushNotification {
    private $registrationIds = array();
    private $titulo   = null;
    private $mensagem = null;
    private $dados    = array();
    public $result    = null;
    public $success   = false;
    public $errors    = array();
    private $invalidTokens = array();

    public function __construct($titulo, $mensagem, $registrationIds, $dados = array()) {
        $this->titulo   = $this->normalizaTexto($titulo);
        $this->mensagem = $this->normalizaTexto($mensagem);
        $this->registrationIds = is_array($registrationIds) ? $registrationIds : array($registrationIds);
        $this->dados = $dados;
        
        error_log("🚀 Push Notification - Título: " . $this->titulo);
        error_log("📱 Push Notification - Tokens: " . count($this->registrationIds));
    }

    public function send() {
        try {
            if (empty($this->registrationIds)) {
                throw new Exception("Nenhum token de registro fornecido");
            }

            // Separar tokens Expo dos FCM
            $expoTokens = array();
            $fcmTokens = array();

            foreach ($this->registrationIds as $token) {
                if (strpos($token, 'ExponentPushToken[') === 0) {
                    $expoTokens[] = $token;
                } else {
                    $fcmTokens[] = $token;
                }
            }

            $success = false;

            // Tentar enviar via Expo primeiro
            if (!empty($expoTokens)) {
                error_log("📤 Enviando via Expo Push API para " . count($expoTokens) . " tokens");
                $success = $this->sendExpoNotifications($expoTokens) || $success;
            }

            // Se falhar, tentar FCM direto
            if (!empty($fcmTokens) || (!$success && !empty($expoTokens))) {
                error_log("📤 Tentando FCM direto para " . count($this->registrationIds) . " tokens");
                $success = $this->sendFCMNotifications($this->registrationIds) || $success;
            }

            $this->success = $success;

        } catch (Exception $e) {
            $this->success = false;
            $this->errors[] = $e->getMessage();
            error_log("❌ Erro ao enviar push: " . $e->getMessage());
        }

        return $this->success;
    }

    private function sendExpoNotifications($tokens) {
        try {
            $messages = array();

            foreach ($tokens as $token) {
                $messages[] = array(
                    "to" => $token,
                    "title" => $this->titulo,
                    "body" => $this->mensagem,
                    "data" => array_merge(array(
                        "timestamp" => time(),
                        "app" => "tecbiz"
                    ), $this->dados),
                    "sound" => "default",
                    "badge" => 1,
                    "priority" => "high",
                    "channelId" => "default"
                );
            }

            error_log("📨 Expo Messages: " . json_encode($messages, JSON_PRETTY_PRINT));

            // Headers com autenticação
            $headers = array(
                'Accept: application/json',
                'Accept-encoding: gzip, deflate',
                'Content-Type: application/json'
            );

            // Se tiver chave FCM, adicionar
            if (defined('_FCM_SERVER_KEY_') && _FCM_SERVER_KEY_ !== 'AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk') {
                $headers[] = 'Authorization: key=' . _FCM_SERVER_KEY_;
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, _HOST_EXPO_PUSH_);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messages));
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_error($ch)) {
                throw new Exception("Erro cURL: " . curl_error($ch));
            }
            
            curl_close($ch);

            $this->result = json_decode($response, true);
            error_log("📥 Expo Response: " . $response);

            if ($httpCode == 200 && isset($this->result['data'])) {
                $successCount = 0;
                
                foreach ($this->result['data'] as $index => $result) {
                    if (isset($result['status']) && $result['status'] === 'ok') {
                        $successCount++;
                        error_log("✅ Token $index enviado com sucesso");
                    } else {
                        if (isset($result['message'])) {
                            $this->errors[] = "Token $index: " . $result['message'];
                            error_log("❌ Erro token $index: " . $result['message']);
                        }
                    }
                }

                if ($successCount > 0) {
                    error_log("🎉 Expo: $successCount notificações enviadas");
                    return true;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("❌ Erro Expo: " . $e->getMessage());
            return false;
        }
    }

    private function sendFCMNotifications($tokens) {
        try {
            error_log("🔄 Tentando FCM direto...");

            if (!defined('_FCM_SERVER_KEY_') || _FCM_SERVER_KEY_ === 'AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk') {
                error_log("⚠️ Chave FCM não configurada");
                return false;
            }

            $notification = array(
                'title' => $this->titulo,
                'body' => $this->mensagem,
                'sound' => 'default',
                'badge' => 1
            );

            $data = array_merge(array(
                'timestamp' => time(),
                'app' => 'tecbiz'
            ), $this->dados);

            $fields = array(
                'registration_ids' => $tokens,
                'notification' => $notification,
                'data' => $data,
                'priority' => 'high'
            );

            $headers = array(
                'Authorization: key=' . _FCM_SERVER_KEY_,
                'Content-Type: application/json'
            );

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $result = json_decode($response, true);
            error_log("📥 FCM Response: " . $response);

            if ($httpCode == 200 && isset($result['success']) && $result['success'] > 0) {
                error_log("🎉 FCM: " . $result['success'] . " notificações enviadas");
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("❌ Erro FCM: " . $e->getMessage());
            return false;
        }
    }

    private function normalizaTexto($texto) {
        if (empty($texto)) return '';
        
        // Remover emojis problemáticos e normalizar
        $texto = preg_replace('/[\x{1F600}-\x{1F64F}]/u', '', $texto); // Emoticons
        $texto = preg_replace('/[\x{1F300}-\x{1F5FF}]/u', '', $texto); // Símbolos
        $texto = preg_replace('/[\x{1F680}-\x{1F6FF}]/u', '', $texto); // Transporte
        $texto = preg_replace('/[\x{2600}-\x{26FF}]/u', '', $texto);   // Símbolos diversos
        
        return trim($texto);
    }

    public function getResult() {
        return array(
            'success' => $this->success,
            'errors' => $this->errors,
            'raw_result' => $this->result
        );
    }

    public function getInvalidTokens() {
        return $this->invalidTokens;
    }
}
?>
