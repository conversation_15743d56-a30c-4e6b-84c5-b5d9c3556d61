{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,273,353,448,547,629,706,795,884,966,1047,1131,1201,1292,1366,1438,1509,1587,1654", "endColumns": "75,91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "176,268,348,443,542,624,701,790,879,961,1042,1126,1196,1287,1361,1433,1504,1582,1649,1769"}, "to": {"startLines": "30,40,41,63,65,66,88,89,92,93,96,97,102,103,107,110,112,117,118,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2833,3898,3990,6577,6760,6859,9451,9528,9768,9857,10100,10181,10613,10683,11009,11240,11383,11782,11860,12009", "endColumns": "75,91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "2904,3985,4065,6667,6854,6936,9523,9612,9852,9934,10176,10260,10678,10769,11078,11307,11449,11855,11922,12124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4153,4259,4419,4546,4655,4798,4923,5043,5275,5431,5537,5699,5826,5971,6149,6215,6277", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4254,4414,4541,4650,4793,4918,5038,5143,5426,5532,5694,5821,5966,6144,6210,6272,6350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "130,220", "endColumns": "89,86", "endOffsets": "215,302"}, "to": {"startLines": "122,123", "startColumns": "4,4", "startOffsets": "12208,12298", "endColumns": "89,86", "endOffsets": "12293,12380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "33,34,35,36,37,38,39,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3171,3267,3369,3466,3564,3671,3780,11607", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3262,3364,3461,3559,3666,3775,3893,11703"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6465,7195,7294,7401", "endColumns": "111,98,106,96", "endOffsets": "6572,7289,7396,7493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,10533", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,10608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,203,317,427,515,658,769,885,1008,1149,1281,1408,1544,1687,1786,1944,2068,2217,2367,2493,2614,2706,2828,2913,3030,3128,3256", "endColumns": "147,113,109,87,142,110,115,122,140,131,126,135,142,98,157,123,148,149,125,120,91,121,84,116,97,127,93", "endOffsets": "198,312,422,510,653,764,880,1003,1144,1276,1403,1539,1682,1781,1939,2063,2212,2362,2488,2609,2701,2823,2908,3025,3123,3251,3345"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,99,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2909,3057,6355,6672,6941,7084,7498,7614,7737,7878,8010,8137,8273,8416,8515,8673,8797,8946,9096,10334,12385,12477,12599,12684,12801,12899,13027", "endColumns": "147,113,109,87,142,110,115,122,140,131,126,135,142,98,157,123,148,149,125,120,91,121,84,116,97,127,93", "endOffsets": "3052,3166,6460,6755,7079,7190,7609,7732,7873,8005,8132,8268,8411,8510,8668,8792,8941,9091,9217,10450,12472,12594,12679,12796,12894,13022,13116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5148", "endColumns": "126", "endOffsets": "5270"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,289,360,443,518,594,675,755,824,902,981,1057,1137,1217,1294,1365,1435,1518,1592,1674", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "126,209,284,355,438,513,589,670,750,819,897,976,1052,1132,1212,1289,1360,1430,1513,1587,1669,1748"}, "to": {"startLines": "29,42,85,86,87,90,91,94,95,98,100,104,105,106,108,109,111,113,114,116,119,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2757,4070,9222,9297,9368,9617,9692,9939,10020,10265,10455,10774,10853,10929,11083,11163,11312,11454,11524,11708,11927,12129", "endColumns": "75,82,74,70,82,74,75,80,79,68,77,78,75,79,79,76,70,69,82,73,81,78", "endOffsets": "2828,4148,9292,9363,9446,9687,9763,10015,10095,10329,10528,10848,10924,11004,11158,11235,11378,11519,11602,11777,12004,12203"}}]}]}