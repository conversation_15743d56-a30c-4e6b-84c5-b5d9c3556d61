{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,279,377,458,577,668,767,866,969,1071,1169,1270,1371,1461,1585,1685,1797,1903,2000,2102,2187,2296,2374,2478,2563,2672", "endColumns": "123,99,97,80,118,90,98,98,102,101,97,100,100,89,123,99,111,105,96,101,84,108,77,103,84,108,90", "endOffsets": "174,274,372,453,572,663,762,861,964,1066,1164,1265,1366,1456,1580,1680,1792,1898,1995,2097,2182,2291,2369,2473,2558,2667,2758"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2800,2924,5725,5991,6240,6359,6736,6835,6934,7037,7139,7237,7338,7439,7529,7653,7753,7865,7971,9211,11204,11289,11398,11476,11580,11665,11774", "endColumns": "123,99,97,80,118,90,98,98,102,101,97,100,100,89,123,99,111,105,96,101,84,108,77,103,84,108,90", "endOffsets": "2919,3019,5818,6067,6354,6445,6830,6929,7032,7134,7232,7333,7434,7524,7648,7748,7860,7966,8063,9308,11284,11393,11471,11575,11660,11769,11860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "5823,6450,6542,6643", "endColumns": "82,91,100,92", "endOffsets": "5901,6537,6638,6731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,9384", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,9458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "126,207", "endColumns": "80,76", "endOffsets": "202,279"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "11046,11127", "endColumns": "80,76", "endOffsets": "11122,11199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,191,258,324,399,464,529,598,669,742,814,882,953,1026,1098,1175,1251,1323,1393,1462,1540,1608,1679,1746", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "116,186,253,319,394,459,524,593,664,737,809,877,948,1021,1093,1170,1246,1318,1388,1457,1535,1603,1674,1741,1810"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2662,3841,8068,8135,8201,8427,8492,8557,8626,8850,8923,9143,9313,9612,9685,9757,9907,9983,10127,10268,10337,10516,10723,10910,10977", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "2723,3906,8130,8196,8271,8487,8552,8621,8692,8918,8990,9206,9379,9680,9752,9829,9978,10050,10192,10332,10410,10579,10789,10972,11041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3024,3116,3217,3311,3405,3498,3592,10415", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3111,3212,3306,3400,3493,3587,3683,10511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "4773", "endColumns": "98", "endOffsets": "4867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3911,4012,4141,4256,4358,4463,4579,4681,4872,4980,5081,5211,5326,5430,5538,5594,5651", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4007,4136,4251,4353,4458,4574,4676,4768,4975,5076,5206,5321,5425,5533,5589,5646,5720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,177,254,330,415,506,583,657,734,812,887,960,1035,1103,1184,1257,1329,1400,1473,1539", "endColumns": "71,76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "172,249,325,410,501,578,652,729,807,882,955,1030,1098,1179,1252,1324,1395,1468,1534,1650"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2728,3688,3765,5906,6072,6163,8276,8350,8697,8775,8995,9068,9463,9531,9834,10055,10197,10584,10657,10794", "endColumns": "71,76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "2795,3760,3836,5986,6158,6235,8345,8422,8770,8845,9063,9138,9526,9607,9902,10122,10263,10652,10718,10905"}}]}]}