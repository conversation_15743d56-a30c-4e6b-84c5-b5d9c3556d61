{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "141,236", "endColumns": "94,99", "endOffsets": "231,331"}, "to": {"startLines": "123,124", "startColumns": "4,4", "startOffsets": "12586,12681", "endColumns": "94,99", "endOffsets": "12676,12776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,289,373,470,572,660,738,825,916,998,1086,1176,1249,1334,1408,1487,1562,1639,1706", "endColumns": "86,96,83,96,101,87,77,86,90,81,87,89,72,84,73,78,74,76,66,114", "endOffsets": "187,284,368,465,567,655,733,820,911,993,1081,1171,1244,1329,1403,1482,1557,1634,1701,1816"}, "to": {"startLines": "30,40,41,63,65,66,88,89,93,94,97,98,103,104,108,111,113,118,119,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2910,4024,4121,6762,6954,7056,9678,9756,10071,10162,10419,10507,10965,11038,11370,11601,11758,12166,12243,12386", "endColumns": "86,96,83,96,101,87,77,86,90,81,87,89,72,84,73,78,74,76,66,114", "endOffsets": "2992,4116,4200,6854,7051,7139,9751,9838,10157,10239,10502,10592,11033,11118,11439,11675,11828,12238,12305,12496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4284,4392,4552,4682,4792,4943,5073,5196,5449,5611,5722,5881,6014,6160,6326,6395,6463", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "4387,4547,4677,4787,4938,5068,5191,5300,5606,5717,5876,6009,6155,6321,6390,6458,6541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5305", "endColumns": "143", "endOffsets": "5444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6655,7411,7513,7626", "endColumns": "106,101,112,102", "endOffsets": "6757,7508,7621,7724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,10883", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,10960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,355,464,559,712,826,940,1060,1201,1335,1471,1606,1755,1859,2018,2139,2280,2419,2550,2683,2791,2937,3026,3165,3263,3399", "endColumns": "168,130,108,94,152,113,113,119,140,133,135,134,148,103,158,120,140,138,130,132,107,145,88,138,97,135,107", "endOffsets": "219,350,459,554,707,821,935,1055,1196,1330,1466,1601,1750,1854,2013,2134,2275,2414,2545,2678,2786,2932,3021,3160,3258,3394,3502"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,100,125,126,127,128,129,130,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2997,3166,6546,6859,7144,7297,7729,7843,7963,8104,8238,8374,8509,8658,8762,8921,9042,9183,9322,10666,12781,12889,13035,13124,13263,13361,13497", "endColumns": "168,130,108,94,152,113,113,119,140,133,135,134,148,103,158,120,140,138,130,132,107,145,88,138,97,135,107", "endOffsets": "3161,3292,6650,6949,7292,7406,7838,7958,8099,8233,8369,8504,8653,8757,8916,9037,9178,9317,9448,10794,12884,13030,13119,13258,13356,13492,13600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "33,34,35,36,37,38,39,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3297,3395,3497,3597,3696,3798,3907,11992", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3390,3492,3592,3691,3793,3902,4019,12088"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,274,345,429,497,573,657,742,832,901,985,1075,1150,1232,1311,1389,1467,1541,1626,1699,1775", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "120,199,269,340,424,492,568,652,737,827,896,980,1070,1145,1227,1306,1384,1462,1536,1621,1694,1770,1855"}, "to": {"startLines": "29,42,85,86,87,90,91,92,95,96,99,101,105,106,107,109,110,112,114,115,117,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2840,4205,9453,9523,9594,9843,9911,9987,10244,10329,10597,10799,11123,11213,11288,11444,11523,11680,11833,11907,12093,12310,12501", "endColumns": "69,78,69,70,83,67,75,83,84,89,68,83,89,74,81,78,77,77,73,84,72,75,84", "endOffsets": "2905,4279,9518,9589,9673,9906,9982,10066,10324,10414,10661,10878,11208,11283,11365,11518,11596,11753,11902,11987,12161,12381,12581"}}]}]}