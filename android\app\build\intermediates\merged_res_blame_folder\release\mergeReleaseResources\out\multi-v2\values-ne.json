{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,179,278,368,462,559,645,727,823,910,996,1086,1179,1256,1340,1415,1488,1560,1641,1709", "endColumns": "73,98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "174,273,363,457,554,640,722,818,905,991,1081,1174,1251,1335,1410,1483,1555,1636,1704,1824"}, "to": {"startLines": "29,39,40,62,64,65,86,87,90,91,92,93,96,97,98,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2872,3975,4074,6787,6971,7068,9665,9747,9978,10065,10151,10241,10555,10632,10716,10791,10934,11176,11257,11325", "endColumns": "73,98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "2941,4069,4159,6876,7063,7149,9742,9838,10060,10146,10236,10329,10627,10711,10786,10859,11001,11252,11320,11440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "128,213", "endColumns": "84,90", "endOffsets": "208,299"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11445,11530", "endColumns": "84,90", "endOffsets": "11525,11616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4241,4352,4510,4644,4761,4932,5068,5178,5447,5627,5741,5905,6038,6186,6338,6404,6476", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4347,4505,4639,4756,4927,5063,5173,5278,5622,5736,5900,6033,6181,6333,6399,6471,6563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,132,200,279,347,414,484", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "127,195,274,342,409,479,548"}, "to": {"startLines": "41,84,85,88,89,100,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4164,9518,9586,9843,9911,10864,11006", "endColumns": "76,67,78,67,66,69,68", "endOffsets": "4236,9581,9660,9906,9973,10929,11070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5283", "endColumns": "163", "endOffsets": "5442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6686,7437,7549,7663", "endColumns": "100,111,113,107", "endOffsets": "6782,7544,7658,7766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,10475", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,10550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,364,482,572,728,855,974,1096,1224,1356,1501,1633,1781,1877,2049,2192,2332,2471,2602,2743,2863,3012,3117,3251,3372,3522", "endColumns": "168,139,117,89,155,126,118,121,127,131,144,131,147,95,171,142,139,138,130,140,119,148,104,133,120,149,118", "endOffsets": "219,359,477,567,723,850,969,1091,1219,1351,1496,1628,1776,1872,2044,2187,2327,2466,2597,2738,2858,3007,3112,3246,3367,3517,3636"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,94,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2946,3115,6568,6881,7154,7310,7771,7890,8012,8140,8272,8417,8549,8697,8793,8965,9108,9248,9387,10334,11621,11741,11890,11995,12129,12250,12400", "endColumns": "168,139,117,89,155,126,118,121,127,131,144,131,147,95,171,142,139,138,130,140,119,148,104,133,120,149,118", "endOffsets": "3110,3250,6681,6966,7305,7432,7885,8007,8135,8267,8412,8544,8692,8788,8960,9103,9243,9382,9513,10470,11736,11885,11990,12124,12245,12395,12514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "32,33,34,35,36,37,38,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3255,3358,3461,3563,3669,3767,3867,11075", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3353,3456,3558,3664,3762,3862,3970,11171"}}]}]}