{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "33,34,35,36,37,38,39,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3291,3387,3489,3588,3687,3793,3897,12001", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3382,3484,3583,3682,3788,3892,4010,12097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,10886", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,10963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5207", "endColumns": "142", "endOffsets": "5345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4197,4302,4451,4579,4689,4843,4977,5099,5350,5523,5631,5786,5914,6075,6214,6280,6341", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "4297,4446,4574,4684,4838,4972,5094,5202,5518,5626,5781,5909,6070,6209,6275,6336,6412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6529,7294,7397,7507", "endColumns": "105,102,109,104", "endOffsets": "6630,7392,7502,7607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,377,489,580,740,856,973,1094,1227,1366,1508,1641,1779,1883,2060,2193,2345,2495,2616,2741,2850,2994,3083,3207,3313,3454", "endColumns": "182,138,111,90,159,115,116,120,132,138,141,132,137,103,176,132,151,149,120,124,108,143,88,123,105,140,107", "endOffsets": "233,372,484,575,735,851,968,1089,1222,1361,1503,1636,1774,1878,2055,2188,2340,2490,2611,2736,2845,2989,3078,3202,3308,3449,3557"}, "to": {"startLines": "31,32,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,100,126,127,128,129,130,131,132", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2969,3152,6417,6734,7018,7178,7612,7729,7850,7983,8122,8264,8397,8535,8639,8816,8949,9101,9251,10673,12859,12968,13112,13201,13325,13431,13572", "endColumns": "182,138,111,90,159,115,116,120,132,138,141,132,137,103,176,132,151,149,120,124,108,143,88,123,105,140,107", "endOffsets": "3147,3286,6524,6820,7173,7289,7724,7845,7978,8117,8259,8392,8530,8634,8811,8944,9096,9246,9367,10793,12963,13107,13196,13320,13426,13567,13675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,287,373,472,575,665,745,841,931,1018,1107,1198,1270,1360,1438,1516,1591,1670,1740", "endColumns": "85,95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "186,282,368,467,570,660,740,836,926,1013,1102,1193,1265,1355,1433,1511,1586,1665,1735,1856"}, "to": {"startLines": "30,40,41,62,64,65,87,88,93,94,97,98,103,104,108,111,113,118,119,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2883,4015,4111,6635,6825,6928,9610,9690,10080,10170,10421,10510,10968,11040,11377,11612,11768,12176,12255,12405", "endColumns": "85,95,85,98,102,89,79,95,89,86,88,90,71,89,77,77,74,78,69,120", "endOffsets": "2964,4106,4192,6729,6923,7013,9685,9781,10165,10252,10505,10596,11035,11125,11450,11685,11838,12250,12320,12521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,278,368,436,504,581,662,746,826,898,986,1073,1152,1233,1313,1390,1468,1542,1626,1700,1780,1851", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,197,273,363,431,499,576,657,741,821,893,981,1068,1147,1228,1308,1385,1463,1537,1621,1695,1775,1846,1929"}, "to": {"startLines": "29,84,85,86,89,90,91,92,95,96,99,101,105,106,107,109,110,112,114,115,117,120,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2808,9372,9444,9520,9786,9854,9922,9999,10257,10341,10601,10798,11130,11217,11296,11455,11535,11690,11843,11917,12102,12325,12526,12597", "endColumns": "74,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "2878,9439,9515,9605,9849,9917,9994,10075,10336,10416,10668,10881,11212,11291,11372,11530,11607,11763,11912,11996,12171,12400,12592,12675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "140,228", "endColumns": "87,90", "endOffsets": "223,314"}, "to": {"startLines": "124,125", "startColumns": "4,4", "startOffsets": "12680,12768", "endColumns": "87,90", "endOffsets": "12763,12854"}}]}]}