<libraries>
  <library
      name="com.facebook.react:react-android:0.81.4:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6997bfbb77c616381810af9ce1fe1f5\transformed\react-android-0.81.4-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.81.4"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6997bfbb77c616381810af9ce1fe1f5\transformed\react-android-0.81.4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7791bdf654876e6ba13c0757ee9ffed2\transformed\fresco-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7791bdf654876e6ba13c0757ee9ffed2\transformed\fresco-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f4487991d53ea2ffbe2e4604920703d6\transformed\imagepipeline-okhttp3-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f4487991d53ea2ffbe2e4604920703d6\transformed\imagepipeline-okhttp3-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e502c92cd4162352bba0eb4051bb287a\transformed\middleware-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e502c92cd4162352bba0eb4051bb287a\transformed\middleware-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cec4ec87a0c48a94d459216f8dda060d\transformed\ui-common-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cec4ec87a0c48a94d459216f8dda060d\transformed\ui-common-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\466eaef63f00fb2206464c98386fa111\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\466eaef63f00fb2206464c98386fa111\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo:54.0.2"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\466eaef63f00fb2206464c98386fa111\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-modules-core::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c76ce124203f35d40ff24a14761eb266\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c76ce124203f35d40ff24a14761eb266\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-modules-core:3.0.15"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c76ce124203f35d40ff24a14761eb266\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-launcher::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\31791c52d86b358545a22493e2d506bf\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\31791c52d86b358545a22493e2d506bf\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-launcher:6.0.11"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\31791c52d86b358545a22493e2d506bf\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-menu::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ae9cf1eff009e2a14d95d535ad7b4ee\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ae9cf1eff009e2a14d95d535ad7b4ee\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-menu:7.0.11"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3ae9cf1eff009e2a14d95d535ad7b4ee\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\93b576e42733cef862a954c928112e8c\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\93b576e42733cef862a954c928112e8c\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0a2a6591dced4295b60b85002f8c8409\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0a2a6591dced4295b60b85002f8c8409\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.device:8.0.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac72a032a8878ebad40e33f6147ffdb8\transformed\expo.modules.device-8.0.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.device:8.0.7"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac72a032a8878ebad40e33f6147ffdb8\transformed\expo.modules.device-8.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.filesystem:19.0.12@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0e053a568b0c2a0f58626bf908c71f52\transformed\expo.modules.filesystem-19.0.12\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.filesystem:19.0.12"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0e053a568b0c2a0f58626bf908c71f52\transformed\expo.modules.filesystem-19.0.12"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ea15573963922ccc058638b436218bd\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ea15573963922ccc058638b436218bd\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.securestore:15.0.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19e2d333207faf7bb3b972a2b59942f0\transformed\expo.modules.securestore-15.0.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.securestore:15.0.7"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19e2d333207faf7bb3b972a2b59942f0\transformed\expo.modules.securestore-15.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.2.0-alpha04@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.2.0-alpha04"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\762f4dc9aebf37bfc66de375dc543aba\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\762f4dc9aebf37bfc66de375dc543aba\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b4f01381b71eb2d1673f09999325396c\transformed\activity-1.9.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b4f01381b71eb2d1673f09999325396c\transformed\activity-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\861839787bd11f700d8ba4a2bc58d182\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\861839787bd11f700d8ba4a2bc58d182\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfd1ac0f786923d01f25f12719618681\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfd1ac0f786923d01f25f12719618681\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7ae61c3b313c8db9701f97884214dd26\transformed\browser-1.6.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7ae61c3b313c8db9701f97884214dd26\transformed\browser-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab4e70f01f1243dec7d1ff49d1e2575b\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab4e70f01f1243dec7d1ff49d1e2575b\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ec819bb4f8b327e6e4eae3361ec6e50\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ec819bb4f8b327e6e4eae3361ec6e50\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10558616e97b62882262ebd437660138\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10558616e97b62882262ebd437660138\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f30fdc996636fc4d1e5f4da5f787459\transformed\lifecycle-viewmodel-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f30fdc996636fc4d1e5f4da5f787459\transformed\lifecycle-viewmodel-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\341d65447503e294f53c11a98be04379\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\341d65447503e294f53c11a98be04379\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87c62ead478518fad06a6c791292b9fc\transformed\lifecycle-livedata-core-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87c62ead478518fad06a6c791292b9fc\transformed\lifecycle-livedata-core-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c46ca3c85a79013ee8f827a1bfd1c979\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c46ca3c85a79013ee8f827a1bfd1c979\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.15.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5165c706d0552969a898bd541f832e5\transformed\core-ktx-1.15.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.15.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5165c706d0552969a898bd541f832e5\transformed\core-ktx-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7824f5704a8615d900e1bbc06f70d683\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7824f5704a8615d900e1bbc06f70d683\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a07e4dd9491bb3b255df6a59e2483943\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a07e4dd9491bb3b255df6a59e2483943\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3925f987960bcdf2b8a2477ce730ea1b\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3925f987960bcdf2b8a2477ce730ea1b\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da1abaface34e688008971d372cf60e\transformed\media-1.0.0\jars\classes.jar"
      resolved="androidx.media:media:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da1abaface34e688008971d372cf60e\transformed\media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d97cddcb237db80b430148e2000c049e\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d97cddcb237db80b430148e2000c049e\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c8b3ac0d0a1c4de61c4a8165bf1b104\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c8b3ac0d0a1c4de61c4a8165bf1b104\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b0df1f54e47f937b3a436e7eaa6be195\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b0df1f54e47f937b3a436e7eaa6be195\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ef1d6332463d11dacfeb288b03c0bf38\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ef1d6332463d11dacfeb288b03c0bf38\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6dd6e18c95dcc846d8cd2cc4971042d\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6dd6e18c95dcc846d8cd2cc4971042d\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.15.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\jars\classes.jar"
      resolved="androidx.core:core:1.15.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e17dac39dfada490e31bdb8abe4c3c1\transformed\lifecycle-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e17dac39dfada490e31bdb8abe4c3c1\transformed\lifecycle-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bba1d21875594204cebffa52ee9faa9a\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bba1d21875594204cebffa52ee9faa9a\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f3d7d25699208affda5e3d4c44d54862\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f3d7d25699208affda5e3d4c44d54862\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c764c1d3666dc7fb74b57b77f5d8a34\transformed\lifecycle-service-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c764c1d3666dc7fb74b57b77f5d8a34\transformed\lifecycle-service-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9900611c7f0496db5ba0e24c27e34642\transformed\lifecycle-livedata-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9900611c7f0496db5ba0e24c27e34642\transformed\lifecycle-livedata-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.9.0\9beade4c1c1569e4f36cbd2c37e02e3e41502601\kotlinx-coroutines-core-jvm-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.9.0\6cf3b8df5a2ce8c6b0dffe55657a827c405ee2fe\kotlinx-coroutines-android-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0"/>
  <library
      name="com.facebook.fresco:fbcore:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bd79fe3d971c00a9c69a6fc30124a89a\transformed\fbcore-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bd79fe3d971c00a9c69a6fc30124a89a\transformed\fbcore-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2617a59ab69d3cbb4d67d12e7172f9c8\transformed\drawee-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2617a59ab69d3cbb4d67d12e7172f9c8\transformed\drawee-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="io.insert-koin:koin-core-jvm:3.5.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-jvm\3.5.6\71c101744c62708690796cdb48ed8522a74687c7\koin-core-jvm-3.5.6.jar"
      resolved="io.insert-koin:koin-core-jvm:3.5.6"/>
  <library
      name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrent-collections-jvm\2.0.6\fb80df9c69dd0e154c346ee5510601e2d148e23d\stately-concurrent-collections-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-concurrent-collections-jvm:2.0.6"/>
  <library
      name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrency-jvm\2.0.6\14dcbce3fc3d80a5a07f9df33dd2dc54e437e8d0\stately-concurrency-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-concurrency-jvm:2.0.6"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="com.squareup.okio:okio-jvm:3.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.9.0\eaa4f7858a1e80908b1a2e861e662edd7c6cbbb5\okio-jvm-3.9.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.9.0"/>
  <library
      name=":@@:expo-constants::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\79ca87931216b983d3e75ddb3c7e8d92\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\79ca87931216b983d3e75ddb3c7e8d92\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-constants:18.0.8"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-constants\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\79ca87931216b983d3e75ddb3c7e8d92\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-client::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3141586a6059dd5167229867b36bd438\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3141586a6059dd5167229867b36bd438\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-client:6.0.12"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-client\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3141586a6059dd5167229867b36bd438\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:viewbinding:8.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b675be355ba59956683d034f19582563\transformed\viewbinding-8.11.0\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.11.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b675be355ba59956683d034f19582563\transformed\viewbinding-8.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\07a6b3cdcdd40ead0f7a3f729962584b\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\07a6b3cdcdd40ead0f7a3f729962584b\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3b97eec65a39280db013b98577adad14\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3b97eec65a39280db013b98577adad14\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f11fdf5ee99868dcbb95bb7170064472\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f11fdf5ee99868dcbb95bb7170064472\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfa7797f2c61aec7af3c5e9231aaf369\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfa7797f2c61aec7af3c5e9231aaf369\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\78ed5ce977e413a298fbda2b68b37ae3\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\78ed5ce977e413a298fbda2b68b37ae3\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.5.0\7ba2c69414d46ebc2dd76598bdd0a75c54281a57\collection-jvm-1.5.0.jar"
      resolved="androidx.collection:collection-jvm:1.5.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="io.github.lukmccall:radix-ui-colors-android:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82cb00a830756bccb4f7e540c06f3138\transformed\library-release\jars\classes.jar"
      resolved="io.github.lukmccall:radix-ui-colors-android:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82cb00a830756bccb4f7e540c06f3138\transformed\library-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2951e3f7b53ce4974f38201822e09993\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2951e3f7b53ce4974f38201822e09993\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-core:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2b33c66a03d9da1c6efc549e5037b53\transformed\ui-core-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-core:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2b33c66a03d9da1c6efc549e5037b53\transformed\ui-core-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ba46862941f89ac7149e9bbf2afa70ea\transformed\imagepipeline-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ba46862941f89ac7149e9bbf2afa70ea\transformed\imagepipeline-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18e88ed8c92f5699bc86941876f9b82\transformed\imagepipeline-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18e88ed8c92f5699bc86941876f9b82\transformed\imagepipeline-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.1.20\72672df5e83231744c00550955b10acb71ed49d6\kotlin-stdlib-jdk7-2.1.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.21\97a0975aa19d925e109537af60eb46902920015c\kotlin-stdlib-2.1.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.21"/>
  <library
      name="com.facebook.fresco:animated-gif:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a029fe8b8605891436e34332cff561\transformed\animated-gif-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a029fe8b8605891436e34332cff561\transformed\animated-gif-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6873cdaa6bfa0f911242290759fcfae\transformed\webpsupport-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6873cdaa6bfa0f911242290759fcfae\transformed\webpsupport-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.81.4:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5c6f2f9e4b267c66b1b7d248bb2806\transformed\hermes-android-0.81.4-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.81.4"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5c6f2f9e4b267c66b1b7d248bb2806\transformed\hermes-android-0.81.4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.documentfile:documentfile:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4f4761fcd58deeb85cf76c2e6dff1ca\transformed\documentfile-1.1.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4f4761fcd58deeb85cf76c2e6dff1ca\transformed\documentfile-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9762a49ed3bf5dde0fb03455ec9600db\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9762a49ed3bf5dde0fb03455ec9600db\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb6f6e5d4b0be6ab8ca06969f4ad2e47\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb6f6e5d4b0be6ab8ca06969f4ad2e47\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19f4c919a8c81ff433987fd7c8d9709b\transformed\fbjni-0.7.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19f4c919a8c81ff433987fd7c8d9709b\transformed\fbjni-0.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1be95b060ff0de27c9abd6913a2ec533\transformed\imagepipeline-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1be95b060ff0de27c9abd6913a2ec533\transformed\imagepipeline-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2a8fb07010759952bf90ce0e80cd124\transformed\memory-type-ashmem-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2a8fb07010759952bf90ce0e80cd124\transformed\memory-type-ashmem-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a62a50e9904e8d9d87c500591ee64cec\transformed\memory-type-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a62a50e9904e8d9d87c500591ee64cec\transformed\memory-type-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11b30660fec58d2511abd54b1f5bfa02\transformed\memory-type-java-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11b30660fec58d2511abd54b1f5bfa02\transformed\memory-type-java-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c45874b1b2dd68247585d37c44b265c5\transformed\nativeimagefilters-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c45874b1b2dd68247585d37c44b265c5\transformed\nativeimagefilters-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32d3663f7533af91348424e01ec0d719\transformed\nativeimagetranscoder-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32d3663f7533af91348424e01ec0d719\transformed\nativeimagetranscoder-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="host.exp.exponent:expo.modules.application:7.0.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a7cfe2550a19549038dcf98bd3d86b4\transformed\expo.modules.application-7.0.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.application:7.0.7"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a7cfe2550a19549038dcf98bd3d86b4\transformed\expo.modules.application-7.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="expo.modules.asset:expo.modules.asset:12.0.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99ffc4e96bf1e2f63cba4f352fb21434\transformed\expo.modules.asset-12.0.8\jars\classes.jar"
      resolved="expo.modules.asset:expo.modules.asset:12.0.8"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99ffc4e96bf1e2f63cba4f352fb21434\transformed\expo.modules.asset-12.0.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.device.yearclass\yearclass\2.1.0\ef7d013a0140137b4a948dd65b46a08205d21020\yearclass-2.1.0.jar"
      resolved="com.facebook.device.yearclass:yearclass:2.1.0"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="host.exp.exponent:expo.modules.font:14.0.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0199161e8ab7230eb657afbe5cc08a3b\transformed\expo.modules.font-14.0.8\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.font:14.0.8"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0199161e8ab7230eb657afbe5cc08a3b\transformed\expo.modules.font-14.0.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.keepawake:15.0.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\38d14400a2d5e4ddf13e945710f1944c\transformed\expo.modules.keepawake-15.0.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.keepawake:15.0.7"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\38d14400a2d5e4ddf13e945710f1944c\transformed\expo.modules.keepawake-15.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.localauthentication:17.0.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.localauthentication:17.0.7"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.notifications:0.32.11@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.notifications:0.32.11"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.screenorientation:9.0.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b09029df9b9fdf2fef002b8635560cef\transformed\expo.modules.screenorientation-9.0.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.screenorientation:9.0.7"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b09029df9b9fdf2fef002b8635560cef\transformed\expo.modules.screenorientation-9.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-menu-interface::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6aac138e2645da4565a8d4b6b349f5bf\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6aac138e2645da4565a8d4b6b349f5bf\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-dev-menu-interface:2.0.0"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu-interface\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\6aac138e2645da4565a8d4b6b349f5bf\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-json-utils::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d0d95e5c239975afd0913ac9104da53\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d0d95e5c239975afd0913ac9104da53\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-json-utils:0.15.0"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-json-utils\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d0d95e5c239975afd0913ac9104da53\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-manifests::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f2bc962c81f1a3f67e45fcc97b7131a\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f2bc962c81f1a3f67e45fcc97b7131a\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-manifests:1.0.8"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-manifests\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f2bc962c81f1a3f67e45fcc97b7131a\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-updates-interface::release"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4b22f3f2c30c36b83afd0959f8a6696\transformed\out\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4b22f3f2c30c36b83afd0959f8a6696\transformed\out\jars\libs\R.jar"
      resolved="host.exp.exponent:expo-updates-interface:2.0.0"
      partialResultsDir="D:\Projetos\TecBizExpoApp\node_modules\expo-updates-interface\android\build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4b22f3f2c30c36b83afd0959f8a6696\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:24.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:24.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.apollographql.apollo:apollo-runtime-android:4.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\422e91ae3fee7377d30b3b992f112273\transformed\apollo-runtime-release\jars\classes.jar"
      resolved="com.apollographql.apollo:apollo-runtime-android:4.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\422e91ae3fee7377d30b3b992f112273\transformed\apollo-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="co.touchlab:stately-strict-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-strict-jvm\2.0.6\fdbcb1fc1c9219aa5a5f2b1c9084a4ed8b2a8f8b\stately-strict-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-strict-jvm:2.0.6"/>
  <library
      name="com.google.android.material:material:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1\jars\classes.jar"
      resolved="com.google.android.material:material:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-code-scanner:16.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-code-scanner:16.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning:17.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning:17.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7b66ccce72f4e6fb908076962b89f8c\transformed\barcode-scanning-common-17.0.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning-common:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7b66ccce72f4e6fb908076962b89f8c\transformed\barcode-scanning-common-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-common:17.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-common:17.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:common:18.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\jars\classes.jar"
      resolved="com.google.mlkit:common:18.11.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\948281eefeb2ac423daef145cd8a3d63\transformed\animated-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\948281eefeb2ac423daef145cd8a3d63\transformed\animated-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\deb9b1b93c9197dbf065a8c2a0c4b6f8\transformed\animated-drawable-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\deb9b1b93c9197dbf065a8c2a0c4b6f8\transformed\animated-drawable-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\630e002fb78825169bcbedfd3becd945\transformed\vito-options-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-options:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\630e002fb78825169bcbedfd3becd945\transformed\vito-options-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:urimod:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3be4fb4599aa69685df676e23c1d435e\transformed\urimod-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:urimod:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3be4fb4599aa69685df676e23c1d435e\transformed\urimod-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-source:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7f734b4776f656b30f9f564abafee82a\transformed\vito-source-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-source:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7f734b4776f656b30f9f564abafee82a\transformed\vito-source-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e73162b71c728692a0e73647a17ce97d\transformed\soloader-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e73162b71c728692a0e73647a17ce97d\transformed\soloader-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:2.1.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\2.1.20\3c1003045c4f2a72f987a147abac8e7058be1183\kotlin-reflect-2.1.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:2.1.20"/>
  <library
      name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\497b53a2103d9dab55d7751334c2a0b7\transformed\navigation-runtime-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\497b53a2103d9dab55d7751334c2a0b7\transformed\navigation-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcb549dbc855619e30027cf9153600f3\transformed\navigation-common-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcb549dbc855619e30027cf9153600f3\transformed\navigation-common-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c011fb53f7a2aa28824c31e90f656387\transformed\navigation-compose-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c011fb53f7a2aa28824c31e90f656387\transformed\navigation-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7749b0e07ced936880eb5ff4a52d87c6\transformed\material-ripple-1.0.0\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7749b0e07ced936880eb5ff4a52d87c6\transformed\material-ripple-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.composables:core-android:1.37.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df8bcecc49fccce54e9e67dd4295a710\transformed\core-release\jars\classes.jar"
      resolved="com.composables:core-android:1.37.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df8bcecc49fccce54e9e67dd4295a710\transformed\core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac9f14a47da32b84e694d05981f0518f\transformed\foundation-layout\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac9f14a47da32b84e694d05981f0518f\transformed\foundation-layout"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2359395d430481f036982ea0f5c8dae4\transformed\animation-core\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2359395d430481f036982ea0f5c8dae4\transformed\animation-core"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92cb98e81fed3d3b50619a35279019d0\transformed\animation\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92cb98e81fed3d3b50619a35279019d0\transformed\animation"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bb83775a718fa1878d483da48b985d9\transformed\ui-tooling-preview\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bb83775a718fa1878d483da48b985d9\transformed\ui-tooling-preview"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df45c221466a4106dc9374b4735bc533\transformed\ui-tooling-data\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df45c221466a4106dc9374b4735bc533\transformed\ui-tooling-data"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5de9532f0a4fae909792cf997622c2\transformed\ui-unit\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5de9532f0a4fae909792cf997622c2\transformed\ui-unit"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11fd102c6c432d0a7e1f8a37852e02b8\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11fd102c6c432d0a7e1f8a37852e02b8\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\499ccb90ba39bfb04188e42fb6dde75b\transformed\ui-geometry\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\499ccb90ba39bfb04188e42fb6dde75b\transformed\ui-geometry"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8575d88d0c1fcbcbf2d0788d3102a584\transformed\ui-util\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8575d88d0c1fcbcbf2d0788d3102a584\transformed\ui-util"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b513094464c80e93b7db2e5bc221f53e\transformed\ui-text\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b513094464c80e93b7db2e5bc221f53e\transformed\ui-text"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:21.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-interfaces:16.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30c9c3522d01250988a84182945182d7\transformed\vision-interfaces-16.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-interfaces:16.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30c9c3522d01250988a84182945182d7\transformed\vision-interfaces-16.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24f75fe43a1dfc2cd0e4ee1767fb7a34\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24f75fe43a1dfc2cd0e4ee1767fb7a34\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-annotation-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d1bc41ea89e21615eddf9863861a32d\transformed\runtime-annotation\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-annotation-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d1bc41ea89e21615eddf9863861a32d\transformed\runtime-annotation"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8195b6be121f81eccab7be9c7ac45f83\transformed\runtime-saveable\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8195b6be121f81eccab7be9c7ac45f83\transformed\runtime-saveable"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7eac873f9e9e9d0ef17272d1cafd1664\transformed\emoji2-views-helper-1.4.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7eac873f9e9e9d0ef17272d1cafd1664\transformed\emoji2-views-helper-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35c10ae8011164342dcf8561fd64efea\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35c10ae8011164342dcf8561fd64efea\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a02035e21744ea944cae4c099374c9d0\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a02035e21744ea944cae4c099374c9d0\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\17a21103ff574ee4212c28297f5d53b4\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\17a21103ff574ee4212c28297f5d53b4\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aab617897d5efee02f2a2b67997cb35e\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aab617897d5efee02f2a2b67997cb35e\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.graphics:graphics-path:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\348019051576c774dcdb2baa24a765ff\transformed\graphics-path-1.0.1\jars\classes.jar"
      resolved="androidx.graphics:graphics-path:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\348019051576c774dcdb2baa24a765ff\transformed\graphics-path-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dad18988ecdcbd5cc410865e9eb67f40\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dad18988ecdcbd5cc410865e9eb67f40\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3a49a4c8a73dfd19eb58dfd431d233d0\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3a49a4c8a73dfd19eb58dfd431d233d0\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.9.0\86a8e6b9d0ba0c964fdc7223a38b6ce74bcb24dd\lifecycle-common-java8-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5a94f85a85d6bc95199dd305b8be44c4\transformed\lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5a94f85a85d6bc95199dd305b8be44c4\transformed\lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a2fd120a9584085d2612537f4b414003\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a2fd120a9584085d2612537f4b414003\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\326cd173aa233264a91dd5bbf2598610\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\326cd173aa233264a91dd5bbf2598610\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c3976769562be0e4763bbc0f21712e6\transformed\savedstate-compose-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-compose-android:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c3976769562be0e4763bbc0f21712e6\transformed\savedstate-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85a1f7086b7960f77f97b4af68a04f6d\transformed\runtime\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85a1f7086b7960f77f97b4af68a04f6d\transformed\runtime"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.9.0\9d715295c29dddf8c381565c513fb2d9923f9f41\kotlinx-coroutines-play-services-1.9.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3db2f0b3509c4b3d08a81d0a67364ce\transformed\activity-ktx-1.9.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3db2f0b3509c4b3d08a81d0a67364ce\transformed\activity-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ada150d16c90f6fd21ac4f985429d84f\transformed\activity-compose-1.9.0\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ada150d16c90f6fd21ac4f985429d84f\transformed\activity-compose-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ab80b900a609c386bd54943bf883268\transformed\material-icons-core-1.0.0\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ab80b900a609c386bd54943bf883268\transformed\material-icons-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c4d48eeb7f4b99a4a4e7d71de689573a\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c4d48eeb7f4b99a4a4e7d71de689573a\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182c514307c9cd7fb6130c217b650409\transformed\material-1.0.0\jars\classes.jar"
      resolved="androidx.compose.material:material:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182c514307c9cd7fb6130c217b650409\transformed\material-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f868bcdea222abfc5b941e0bc81954be\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f868bcdea222abfc5b941e0bc81954be\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8b23e8b30b401c4dd9168814489a0810\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8b23e8b30b401c4dd9168814489a0810\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.1.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\2.1.20\ba29f2425f154492d8309d76f4b8b0cbd7a677d2\kotlin-parcelize-runtime-2.1.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.1.20"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-datetime-jvm\0.7.1\81c2e0246bd28cc2dcbdf0ab53628322e66a9de9\kotlinx-datetime-jvm-0.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.1.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\2.1.20\ec77767ee41cee5f7fd31ae93c0c56985d481a56\kotlin-android-extensions-runtime-2.1.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.1.20"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2fcc8f3df6680f0cc0691d14a8b859ae\transformed\vito-renderer-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2fcc8f3df6680f0cc0691d14a8b859ae\transformed\vito-renderer-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.5.0\27c78a926a16a1bf792b2285cf2834e8caae4a07\collection-ktx-1.5.0.jar"
      resolved="androidx.collection:collection-ktx:1.5.0"/>
  <library
      name="com.apollographql.apollo:apollo-api-jvm:4.3.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.apollographql.apollo\apollo-api-jvm\4.3.1\8442365b6c30d4b2965fa64b703399257e3d0bf\apollo-api-jvm-4.3.1.jar"
      resolved="com.apollographql.apollo:apollo-api-jvm:4.3.1"/>
  <library
      name="com.benasher44:uuid-jvm:0.8.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.benasher44\uuid-jvm\0.8.2\f9a2eba1cc4cb9efa3b86407c9bb9b1b7d8522a6\uuid-jvm-0.8.2.jar"
      resolved="com.benasher44:uuid-jvm:0.8.2"/>
  <library
      name="org.jetbrains.kotlinx:atomicfu-jvm:0.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\atomicfu-jvm\0.26.0\8886a0c1867542c419c6f8ce149a8af13b7bb09\atomicfu-jvm-0.26.0.jar"
      resolved="org.jetbrains.kotlinx:atomicfu-jvm:0.26.0"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f33a32db8d06dc21097da2718df72823\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f33a32db8d06dc21097da2718df72823\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\56e7383d55b65ca83dd3ec0f83f1270c\transformed\firebase-components-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\56e7383d55b65ca83dd3ec0f83f1270c\transformed\firebase-components-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfc839453757451d65f1d7d2b231aeb7\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfc839453757451d65f1d7d2b231aeb7\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.9"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f532a9ff664b5c763585b87d177a407a\transformed\transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f532a9ff664b5c763585b87d177a407a\transformed\transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d4e0b17f83e0c03ee07b999bc13b478\transformed\exifinterface-1.0.0\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d4e0b17f83e0c03ee07b999bc13b478\transformed\exifinterface-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cd54390103da05e9f0b933f516c6d0cd\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cd54390103da05e9f0b933f516c6d0cd\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.apollographql.apollo:apollo-mpp-utils-jvm:4.3.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.apollographql.apollo\apollo-mpp-utils-jvm\4.3.1\62a89397620e837b08e6c42ac81fc76e00062215\apollo-mpp-utils-jvm-4.3.1.jar"
      resolved="com.apollographql.apollo:apollo-mpp-utils-jvm:4.3.1"/>
  <library
      name="com.apollographql.apollo:apollo-annotations-jvm:4.3.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.apollographql.apollo\apollo-annotations-jvm\4.3.1\bed5da24198c0fcd5cadfbc018805a08b8aa7410\apollo-annotations-jvm-4.3.1.jar"
      resolved="com.apollographql.apollo:apollo-annotations-jvm:4.3.1"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.code.gson:gson:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.6\9180733b7df8542621dc12e21e87557e8c99b8cb\gson-2.8.6.jar"
      resolved="com.google.code.gson:gson:2.8.6"/>
  <library
      name="com.android.installreferrer:installreferrer:2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\jars\classes.jar"
      resolved="com.android.installreferrer:installreferrer:2.2"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="me.leolin:ShortcutBadger:1.1.22@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\jars\classes.jar"
      resolved="me.leolin:ShortcutBadger:1.1.22"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.26.0\c513866fd91bb46587500440a80fa943e95d12d9\error_prone_annotations-2.26.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.26.0"/>
  <library
      name="com.google.android.odml:image:1.0.0-beta1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1\jars\classes.jar"
      resolved="com.google.android.odml:image:1.0.0-beta1"
      folder="C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
