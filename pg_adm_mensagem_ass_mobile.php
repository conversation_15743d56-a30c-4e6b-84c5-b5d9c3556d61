<?php

define('_QTD_CARACTERES_', 500);
define('_QTD_CARACTERES_TITULO_', 50);
define('_METHOD_', 'POST');

include_once "./interface/web/paginaPadraoAdm.php";
include_once "../tecbiz/bd/bd.php";
include_once "../tecbiz/bd/sql.php";
include_once "../tecbiz/interface/web/HTMLger/form_select.php";
include_once "../tecbiz/interface/web/HTMLger/form_select_entidades.php";
include_once "../tecbiz/logica/log/registro_log_geral.php";

class admMensagemAssMobile extends PaginaPadraoAdm {
	private $bd = null, $sql = null, $codent = null, $modo = null;
	private $tituloMensagem = null, $mensagem = null;

	function __construct($VARS) {
		$this->inicializa_pagina_adm("mensagem_ass_mobile");
		$this->titulo = "Mensagem Mobile para Associados";

		$this->bd = new BD();
		$this->sql = new SQL();

		$this->modo = isset($_REQUEST['modo']) ? $_REQUEST['modo'] : null;

		$this->codent = isset($_REQUEST['codent']) ? $_REQUEST['codent'] : 0;
		$this->codent = $this->codent > 0 ? $_REQUEST['codent'] : null;

		$this->tituloMensagem = isset($_REQUEST['titulo']) ? $_REQUEST['titulo'] : null;
		$this->mensagem = isset($_REQUEST['msgAss']) ? $_REQUEST['msgAss'] : null;

		if ($this->modo == '1') {
			$this->getTokens();
		}

		$this->montaPagina();
		$this->finaliza_pagina();
		$this->imprime_pagina();
	}

	private function montaPagina() {
		$this->template->associa('**METHOD**', _METHOD_);
		$this->template->associa('**MODO**', 0);
		$this->template->associa('**QTD_CARACTERES**', _QTD_CARACTERES_);
		$this->template->associa('**QTD_CARACTERES_TITULO**', _QTD_CARACTERES_TITULO_);
		$this->template->associa('**C2**', '0/'._QTD_CARACTERES_);
		$this->template->associa('**SELECT_ETD**', $this->geraSelectEntidade($this->codent));
	}

	private function geraSelectEntidade($selected = null) {
		$selected = new FormSelectEntidades('codent', $selected, true, null, null, false, true, false, null);
		return $selected->gera();
	}

	private function getTokens() {
		$this->bd->consulta($this->sql->getTokensMobileAssociado($this->codent));

		while ($registro = $this->bd->proximo_registro()) {
			$tokens[] = $registro['token'];
		}
		$tokens = array_chunk($tokens, 100);

		include_once('../tecbiz/logica/elementos/sendAndroidPushNotification.php');
		$c = 1;

		$erros = array();
		foreach ($tokens as $tok) {
			if (is_array($tok)) {
				$push = new sendAndroidPushNotification($this->tituloMensagem, $this->mensagem, $tok);
				$push->send();
				$erros = array();
				if ($push->result->success == 0) {
					$erros[] = "Erro na ".$c. " tentativa.<br>";
				}
				$c++;
			}
		}

		if (count($erros) == 0) {
			$entidade = (!$this->codent) ? 'Todas' : $this->codent;
			$extras = 'ETD: '.$entidade.', Tipo: Mobile, Titulo: '.str_replace("'", "�", $this->tituloMensagem).', Mensagem: ' .str_replace("'", "�", $this->mensagem);
			$log = new RegistroLogGeral();
			$log->registra_operacao('mensagem_ass', 100, null, null, $this->sessao->get('operador_cod'), $extras);
			echo "<script>alert('**ATEN��O**\\n\\nMensagem enviada com sucesso! ($c grupos de 100)'); window.open('./tecbiz.php?a=b3ca29', '_self');</script>";
		} else {
			echo (join($erros));
		}
	}
}

new admMensagemAssMobile($_REQUEST);