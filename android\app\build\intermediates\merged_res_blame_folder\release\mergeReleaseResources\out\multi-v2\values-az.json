{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "135,224", "endColumns": "88,93", "endOffsets": "219,313"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11192,11281", "endColumns": "88,93", "endOffsets": "11276,11370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,186,286,374,471,572,663,744,832,924,1006,1087,1176,1248,1338,1412,1488,1561,1642,1708", "endColumns": "80,99,87,96,100,90,80,87,91,81,80,88,71,89,73,75,72,80,65,116", "endOffsets": "181,281,369,466,567,658,739,827,919,1001,1082,1171,1243,1333,1407,1483,1556,1637,1703,1820"}, "to": {"startLines": "29,39,40,62,64,65,86,87,90,91,92,93,96,97,98,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2813,3938,4038,6662,6848,6949,9429,9510,9734,9826,9908,9989,10292,10364,10454,10528,10679,10928,11009,11075", "endColumns": "80,99,87,96,100,90,80,87,91,81,80,88,71,89,73,75,72,80,65,116", "endOffsets": "2889,4033,4121,6754,6944,7035,9505,9593,9821,9903,9984,10073,10359,10449,10523,10599,10747,11004,11070,11187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6566,7311,7415,7523", "endColumns": "95,103,107,102", "endOffsets": "6657,7410,7518,7621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,10208", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,10287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,230,372,485,574,726,845,958,1079,1209,1337,1458,1578,1725,1819,1986,2117,2247,2377,2497,2627,2743,2897,2990,3118,3227,3370", "endColumns": "174,141,112,88,151,118,112,120,129,127,120,119,146,93,166,130,129,129,119,129,115,153,92,127,108,142,106", "endOffsets": "225,367,480,569,721,840,953,1074,1204,1332,1453,1573,1720,1814,1981,2112,2242,2372,2492,2622,2738,2892,2985,3113,3222,3365,3472"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,94,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2894,3069,6453,6759,7040,7192,7626,7739,7860,7990,8118,8239,8359,8506,8600,8767,8898,9028,9158,10078,11375,11491,11645,11738,11866,11975,12118", "endColumns": "174,141,112,88,151,118,112,120,129,127,120,119,146,93,166,130,129,129,119,129,115,153,92,127,108,142,106", "endOffsets": "3064,3206,6561,6843,7187,7306,7734,7855,7985,8113,8234,8354,8501,8595,8762,8893,9023,9153,9273,10203,11486,11640,11733,11861,11970,12113,12220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4207,4316,4471,4604,4714,4860,4993,5113,5381,5547,5657,5806,5943,6087,6243,6307,6372", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "4311,4466,4599,4709,4855,4988,5108,5214,5542,5652,5801,5938,6082,6238,6302,6367,6448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "32,33,34,35,36,37,38,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3211,3312,3414,3517,3621,3722,3827,10827", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3307,3409,3512,3616,3717,3822,3933,10923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,205,287,355,423,498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "131,200,282,350,418,493,568"}, "to": {"startLines": "41,84,85,88,89,100,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4126,9278,9347,9598,9666,10604,10752", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "4202,9342,9424,9661,9729,10674,10822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5219", "endColumns": "161", "endOffsets": "5376"}}]}]}