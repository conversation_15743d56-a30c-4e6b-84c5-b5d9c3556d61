{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4275,4380,4531,4656,4764,4922,5050,5170,5410,5567,5674,5828,5955,6111,6292,6359,6420", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "4375,4526,4651,4759,4917,5045,5165,5269,5562,5669,5823,5950,6106,6287,6354,6415,6492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,10752", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,10829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,352,463,558,715,825,940,1053,1188,1334,1490,1620,1778,1880,2047,2167,2304,2455,2580,2712,2812,2947,3033,3154,3250,3381", "endColumns": "171,124,110,94,156,109,114,112,134,145,155,129,157,101,166,119,136,150,124,131,99,134,85,120,95,130,101", "endOffsets": "222,347,458,553,710,820,935,1048,1183,1329,1485,1615,1773,1875,2042,2162,2299,2450,2575,2707,2807,2942,3028,3149,3245,3376,3478"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,98,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2960,3132,6497,6806,7088,7245,7666,7781,7894,8029,8175,8331,8461,8619,8721,8888,9008,9145,9296,10532,12657,12757,12892,12978,13099,13195,13326", "endColumns": "171,124,110,94,156,109,114,112,134,145,155,129,157,101,166,119,136,150,124,131,99,134,85,120,95,130,101", "endOffsets": "3127,3252,6603,6896,7240,7350,7776,7889,8024,8170,8326,8456,8614,8716,8883,9003,9140,9291,9416,10659,12752,12887,12973,13094,13190,13321,13423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "145,243", "endColumns": "97,98", "endOffsets": "238,337"}, "to": {"startLines": "121,122", "startColumns": "4,4", "startOffsets": "12460,12558", "endColumns": "97,98", "endOffsets": "12553,12652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "33,34,35,36,37,38,39,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3257,3355,3457,3556,3658,3767,3874,11848", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3350,3452,3551,3653,3762,3869,3999,11944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,280,352,440,520,604,694,775,863,949,1026,1106,1185,1260,1330,1399,1489,1564,1645", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "120,204,275,347,435,515,599,689,770,858,944,1021,1101,1180,1255,1325,1394,1484,1559,1640,1727"}, "to": {"startLines": "29,42,85,86,87,90,91,94,95,99,103,104,105,107,108,110,112,113,115,118,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2799,4191,9421,9492,9564,9837,9917,10189,10279,10664,11004,11090,11167,11323,11402,11549,11689,11758,11949,12171,12373", "endColumns": "69,83,70,71,87,79,83,89,80,87,85,76,79,78,74,69,68,89,74,80,86", "endOffsets": "2864,4270,9487,9559,9647,9912,9996,10274,10355,10747,11085,11162,11242,11397,11472,11614,11753,11843,12019,12247,12455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5274", "endColumns": "135", "endOffsets": "5405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6608,7355,7453,7563", "endColumns": "99,97,109,102", "endOffsets": "6703,7448,7558,7661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,296,383,481,581,668,747,853,946,1041,1125,1213,1298,1383,1459,1531,1601,1679,1748", "endColumns": "90,99,86,97,99,86,78,105,92,94,83,87,84,84,75,71,69,77,68,120", "endOffsets": "191,291,378,476,576,663,742,848,941,1036,1120,1208,1293,1378,1454,1526,1596,1674,1743,1864"}, "to": {"startLines": "30,40,41,63,65,66,88,89,92,93,96,97,101,102,106,109,111,116,117,119", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2869,4004,4104,6708,6901,7001,9652,9731,10001,10094,10360,10444,10834,10919,11247,11477,11619,12024,12102,12252", "endColumns": "90,99,86,97,99,86,78,105,92,94,83,87,84,84,75,71,69,77,68,120", "endOffsets": "2955,4099,4186,6801,6996,7083,9726,9832,10089,10184,10439,10527,10914,10999,11318,11544,11684,12097,12166,12368"}}]}]}