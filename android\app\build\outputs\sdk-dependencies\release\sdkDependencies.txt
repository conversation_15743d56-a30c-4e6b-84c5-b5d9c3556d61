# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.1.21"
  }
  digests {
    sha256: "&;\334g\236\037b\001-\267\260\221yby\266\327\034\363oG\227\251\217\361\254\340X5\362\001\310"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.1.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "2.1.20"
  }
  digests {
    sha256: "SC\277yW\203\223._\2458\260\274\240Z\370\020\273\a!>\333\233\252w\fx@h\365xg"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.6"
  }
  digests {
    sha256: "\370w\323\004f\n\302\241B\363\206[\255\374\227\035\354~\327<t|\177\215]/Q9\312se\023"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.11.0"
  }
  digests {
    sha256: "\316\223\322\207\334\315\254\317s\033\2117\033\v\255\345\333\206y\375y\265Y3/\332\364EN\2637\033"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.0"
  }
  digests {
    sha256: "F\374\216\204-\232N\003\r\375\236\020\214;\300\203\020\371\"\275t!\362\237g\334\252\244\255\2547d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.5.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.5.0"
  }
  digests {
    sha256: "p\263Y$\344\272\274\337\3727\320\345u\356\003\234V\242\331q#4&$\304\213`23pCA"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.5.0"
  }
  digests {
    sha256: "\223\233J\206\227d\016w\330>N\213LJ\235\030\370\023\001\230\305\226\021\224\257\215\331\333\235\307\303S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\024+\215\"\351>Jo8q\220\024\223\302n+\337\341\325\332\037\210\362\330\003\035F\354kl\301+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.9.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.9.0"
  }
  digests {
    sha256: "\255\211\302\211\"5\346p\362\"\330\031\313=\201\030\201C\313\031\240[Y\337\230\211\256Bi\365\307\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.9.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\275x:\315/\2278\204]X8\017F\364[\\\336\225\320\313\003\002}VrS8\262k\374Mr"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.9.0"
  }
  digests {
    sha256: "L\266\2662\035\361fK\231\376\347V\336c\366\257e\314\264\366\2116\362l\367l!\035\352\033#\256"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.9.0"
  }
  digests {
    sha256: "I}\330M\351\362\375F2O%\371\243\257\003-:#\320\207\350z\365\025\304\261\2245\366\326\246Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.9.0"
  }
  digests {
    sha256: "F8-\337\2724\215\271xF\317\250\035g\327#\004\334\025k\271b$5\213B\237\347H\274j\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\362e\246e\201\236Q2+>\352*o\362\253\220\030l\255\361xz\201\335\213\357\f\351<J`\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "_h\274\352\f\361\332\254S \242\375W\025r7\343\tE\317~e\206q\177\274qK5\263.o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.9.0"
  }
  digests {
    sha256: "jp6\240{\3267x\245\r\"G\023\245\003\321\036:zB%\233\\T\250\244m\240\235.^\327"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-annotation"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-annotation-android"
    version: "1.9.0"
  }
  digests {
    sha256: "?\272\235k\3450}\231\204\003\003\217m\372\243\376\335\222LV\030\313E\244\237V\245\277b\202P\235"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.9.0"
  }
  digests {
    sha256: "&\357\245EvSBq\326\\[\337\223\303N\351\310\235\276\3124Xss\v\217\032\311#\332?\252"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-compose"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-compose-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\253qV,\023\200\031\255`\204\334\217\306\220\257l\2715\233\376\'C\247\220b\006[(\275\357|n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.9.0"
  }
  digests {
    sha256: "\307\205\200\3128\315\024\344t28a\353\350\"\320L\004#\034\267\345\233$\336\200\024\006\245\0217j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  digests {
    sha256: "\312\264\004\260]_\256\322\t\020J*\357\024\300\313\263\026\253\237\253\344\250\3607\260C\345\322\36445"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "}\177\374\f\307l\225\201A<\250\251\256?ginI\354\250\v6C\313\255\222B\207\221\037N\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\023\223\310 \351\256\006\033X\252\335\2604\000?\251\240\340\027\206\222\212\306(\242\341-(&\373\336C"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: ")\a\230\270\214\342L\211wG\361\\\322Q\002\000Z\224\032,\364\323^\374\270\316/\026,!\365\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.9.0"
  }
  digests {
    sha256: "\372\261\306\362\026\304@\203\335\267\r\220\342H\3534PO\204kPZ\243\307\25022[ky\r\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.1.0"
  }
  digests {
    sha256: "g\316\270&|\'EW\303WX\353\276D\211\n :Uy\275+\bM\363Mpv<r\370\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.9.0"
  }
  digests {
    sha256: "A\305\372B\311\017\302KMen\203\177\203\316\376\004\020\254\307\321\024\255x\306+\254E\t\212\306\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\223v\313\321\335\360e^q\005\210f7\352\024QF\037\317\323\243\316\356\317\376\314\352\356\273SA\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\320\025\315\240l\333\003\253Tq\2469`\344$\224\036_!~o\207\3439\253\327\316\3569) \232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\333\355\241\252\352\003\273\322\332\037M\361\001\2004n2\313\350\245\2029\242O\233\213\214\262B\002\375\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.9.0"
  }
  digests {
    sha256: "&\005\342@f%\377\0038\310\210\223}\242,Cz\366S\242\364*\372\332\312\255!tq\177^\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.4.0"
  }
  digests {
    sha256: "C?\353\323CJEfqv\307jd\363\362\005\312c5\246\265D\305\265\325\177%\243\2127RB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.4.0"
  }
  digests {
    sha256: "\355]>\327r\245\373\360\325p\367RoX\\\326\032\030\016`\3717%\204\303(\246\216,\3773u"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-android"
    version: "1.9.0"
  }
  digests {
    sha256: "~Y\\\242\305\306x\260f \307.nm\371Y>]\277\204,\3709q\323\360\025`\214\037$\331"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.9.0"
  }
  digests {
    sha256: "x\216\352\300\001Y\252\355\331\177hPg\317,\207\313\361\024\302\027Fs&S\227\0160U#\023\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.9.0"
  }
  digests {
    sha256: "_\220\372c\247\317Rm\336\016\323~;e\275\030gI\211\000nK\353m8\005l\2056D\001\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\223T\264\025\024\210@\314\272\362\026\2651a\377,U\314\276\311\n\224\323Fn^W;KA\363N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.9.0"
  }
  digests {
    sha256: "i\033\244c\321\340\245\263-\224\360D\215i%r\372\255\367\241\n\234t`\257A\262\3040\263g\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material"
    version: "1.0.0"
  }
  digests {
    sha256: "[\036\373\2021\020\230A\216\237a\305\323\252\225\330\344O\303c\031\322:)\001#/\276\177\024}\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.0.0"
  }
  digests {
    sha256: "n\004\224\222\354/\360\246\241\026jY\205\251$\317M\3404e\321tbYB\204\370\036\257\341\251\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.0.0"
  }
  digests {
    sha256: "\r:\302L\201_\272\025\305~\034\240?@c\231\315i\254\036A\343\220\366\022X\302*2&\365P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-data"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-data-android"
    version: "1.9.0"
  }
  digests {
    sha256: "k_\201<\334j\347*\251 *\v\002\275\356\327\323\020Nm8^Hy3w\247lF\202\374\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\312G(\035\221\201#\nx\370\243\035~WE\242\225\313\315{}\2549|\323\3251\230\315\221\305\246"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-extensions"
    version: "2.2.0"
  }
  digests {
    sha256: "d\214\215\341\321\v\002]RJ.F\254\231O\303\366\277\030h&\300\236\301\246-%\v\361\270w\256"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-bom"
    version: "3.5.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core"
    version: "3.5.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.insert-koin"
    artifactId: "koin-core-jvm"
    version: "3.5.6"
  }
  digests {
    sha256: "\202;\2331X\217\371\002\025\301\374\352\343\r\372\024\035%(\343\324\344,(C\352@\3105+\337i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrency"
    version: "2.0.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrency-jvm"
    version: "2.0.6"
  }
  digests {
    sha256: "\305|\333n\025\330\202F\033d\302\323[\356GJ\325\366\273i\366\367cX\260\003\033\216\227b|\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-strict"
    version: "2.0.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-strict-jvm"
    version: "2.0.6"
  }
  digests {
    sha256: "p\317\324kE\000\361\236\237\027\270Z\221@\253@/\\\361-Q\002\322\325l\023O\247k\355\244|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrent-collections"
    version: "2.0.6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "co.touchlab"
    artifactId: "stately-concurrent-collections-jvm"
    version: "2.0.6"
  }
  digests {
    sha256: "z&s\3474\273\351\27079ue\303\351o\001\2235V\266H\244\033\362\347\362\307\210\315\n&\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "react-android"
    version: "0.81.4"
  }
  digests {
    sha256: "J\346\037@\377\315\213\030U\236u\332\017C\3643\327\355\262\332o\'.t\226\225b{\255l/\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.1.0"
  }
  digests {
    sha256: ",\347\220l\321\336\240Z\354\201\227]\262-T8#Y\300Z!\262Rz\330H\274`\366\262r\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.facebook.fbjni"
    artifactId: "fbjni"
    version: "0.7.0"
  }
  digests {
    sha256: "~1\232\341\020\254^^\361\211\004\027\n\352\\>u>\221]\031f\231\327\3759\323l\216\035\3766"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "nativeloader"
    version: "0.12.1"
  }
  digests {
    sha256: "\227\035\355\206\000\234\n\305o\262\'^\022\217\205.}\313\304\032@\315y\226\210\370\241\256\275\345\031]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fresco"
    version: "3.6.0"
  }
  digests {
    sha256: "\333\202\022\214\v\r \233/H\257\330B\222\305\365K;\376@{(\3722\272\277\336\225\221K\003\371"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "soloader"
    version: "3.6.0"
  }
  digests {
    sha256: "\373\276\n7\331\033\206\3242\r\a(\004\f2dY\232\224\263\032\035\344\037\222\242^\024\177\1775\357"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fbcore"
    version: "3.6.0"
  }
  digests {
    sha256: "o\331\214+W7\356V\251\311\017\0344\330}$\345\3232O\327\374,\312mnt\261\300\264<\351"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "soloader"
    version: "0.12.1"
  }
  digests {
    sha256: "[\306\341]q/\220\240\243\211\r\"\333\016\357_\350\236\363\374\263\256\212\241\370\366\246\331\225G\310\222"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "annotation"
    version: "0.12.1"
  }
  digests {
    sha256: "\366\335\325Rh\277\030$\"]\367\243^\301\270\263<\371\210U\246\311\301S\321\211\211\236^\364\244\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-common"
    version: "3.6.0"
  }
  digests {
    sha256: "\000\0361\002d\310\272\316y$=\026\254\003\373`\272\347\255\374,J\300\306k\000\006\302I \217\360"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-core"
    version: "3.6.0"
  }
  digests {
    sha256: "\265\025\306\233\241 \306\036\241W\342Ym\337:\222&3\234\016j\317\311kP\277\226aI\261\301A"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "middleware"
    version: "3.6.0"
  }
  digests {
    sha256: "\226a\364|\261\025j\nrF\201\230\002\311\260\027\272Qp\366\256z\276\3371\375,\366b\343\334\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "drawee"
    version: "3.6.0"
  }
  digests {
    sha256: "`CU\031\313v\316\347\2079\331(\304j\255\251\030\367l\271\223\267Z\371*\261~\235tk\002w"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline"
    version: "3.6.0"
  }
  digests {
    sha256: "W\360A\225\345\327\'6q\316F}\016#\304\207\263\302\310tj\306%\177*\'S\314\aq\366\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.parse.bolts"
    artifactId: "bolts-tasks"
    version: "1.4.0"
  }
  digests {
    sha256: "\233\305\036>\312\205:\235\362\373\254b\3021}\262\311\352a\233\225\265S\0340D\002C\332\254\316\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "urimod"
    version: "3.6.0"
  }
  digests {
    sha256: "T\274\b\206\345y\353\b3)t\222\267\330\234\264\306\023\312\225\303y\272\314A\255\376)S\232\221\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-source"
    version: "3.6.0"
  }
  digests {
    sha256: "\265\303\305\300\207\332\261\231\254\212\376\203\227\372\211\314\363\206\337{)\250\333w\263a%Wj\362\344\221"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-base"
    version: "3.6.0"
  }
  digests {
    sha256: "i\312\255\a\3767\000\264\217;Vf%\004\371\301#]\242\351w\270\352\315\235\362Q2\036\226I\306"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-native"
    version: "3.6.0"
  }
  digests {
    sha256: "\\\276\257\260\367\331\034\a\216%k\261\257)\201I?7\262\243\206\337CY1\t\326\253\357w\222\331"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-ashmem"
    version: "3.6.0"
  }
  digests {
    sha256: "\v+\032\215}nX\0061,K{F\241U7\343\255\022\247\252\237\303\254&$e00\202\271F"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-native"
    version: "3.6.0"
  }
  digests {
    sha256: "\315\300\314\341\b\274\253i\332+v\036\3558\236G\222\371\224\260;RK\330&1l)2\353\f\370"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-java"
    version: "3.6.0"
  }
  digests {
    sha256: "E\004\375\305c\346\231t\2130\017>5\'\233\340\221\214\004\230\036~\366\037\221\226c\333\326\236h?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagefilters"
    version: "3.6.0"
  }
  digests {
    sha256: "PDt^\205\3735\027~\216+\200\337\202\300*\226\212m\270\212\377\347\021\344\373\0008\222M2\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagetranscoder"
    version: "3.6.0"
  }
  digests {
    sha256: "\r\217\205Ya|\210v=\307^`\005\374`\343{\375k\262\017\345\271z\030|\253\357\302\261\032|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-okhttp3"
    version: "3.6.0"
  }
  digests {
    sha256: "\372\240\036\3209J^\310l\376\234\006\341\006\243!y\344\373\203x\347\256\357\336X2\025\224\272e\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.9.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.9.0"
  }
  digests {
    sha256: "\335\303\206\377\024\275%\325\3114\026q\226\352\364[\030\336O(\341\305ZM\263z\345\224\313\3757\344"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.yoga"
    artifactId: "proguard-annotations"
    version: "1.19.0"
  }
  digests {
    sha256: "\373e\367\006\356\240[V@\373k\223\322\035lA\371\342\264\347\246\346\331\362\214\351\322\245tGI\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.9.2"
  }
  digests {
    sha256: "\016\0349\252\211f\217\226%\311 z\315J\207\204\334\206\212\320\325\242\001\035\236\025\340gO;}\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.lukmccall"
    artifactId: "radix-ui-colors"
    version: "1.0.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.lukmccall"
    artifactId: "radix-ui-colors-android"
    version: "1.0.1"
  }
  digests {
    sha256: "\341\273\3012\212\252vk\323u\354\2553/\200t\261\344\2330\032\275\246\341[G\274\357\300\225\233\332"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.6.0"
  }
  digests {
    sha256: "\215\200\217\335\346\'\224\237\3651\241\021[.\357^\t>\323\022\2765h\306\311\316\006\210\301\205H\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.2.1"
  }
  digests {
    sha256: "\323\320\314wo#A\332\216W%\206\307\323\220\245\263V\3169\240\336\262v\200q\334@\263d\254\200"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.6"
  }
  digests {
    sha256: "\310\373H9\005M(\v03\370\000\321\365\251}\342\360(\353\213\242\353E\212\322\207\3456\363\362_"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.composables"
    artifactId: "core"
    version: "1.37.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.composables"
    artifactId: "core-android"
    version: "1.37.0"
  }
  digests {
    sha256: "\220k.\306\234N\255\234\316.w\272\267,P\3456\312\327\263\352\312j-|\306u\034\374`\256\035"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.foundation"
    artifactId: "foundation"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.animation"
    artifactId: "animation"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.animation"
    artifactId: "animation-core"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.annotation-internal"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.collection-internal"
    artifactId: "collection"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.runtime"
    artifactId: "runtime"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.8.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-geometry"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-util"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-graphics"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-unit"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.ui"
    artifactId: "ui-text"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-reflect"
    version: "2.1.20"
  }
  digests {
    sha256: "A\227x\362\267\250&\272\326\356,\241\344\275\353\3469\2530m)\004\372>\362\243\b\354\372\2538\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "W\204\334\220\2229\371\230\202\361\036\272e?#\364\206)\342\022\b(\314\233F\256\211&\273\262\"Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-android"
    version: "2.9.0"
  }
  digests {
    sha256: "]\257U\342\a\005\240G\036v\332\022\210p\30315\034Z\022<\316\364\244\262G~\271-7\331{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\367o\004\270O\310\233t\000\023\272\201\313\f\035\351\024\005=\325\\\362\330\360\306i\304\326\362\270\353\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-code-scanner"
    version: "16.1.0"
  }
  digests {
    sha256: "\222\343t\351\257T\301\215L\246\352\230\275}\305\241\220p\206Xav\234CJ\353\252\333\032\2457S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "barcode-scanning-common"
    version: "17.0.0"
  }
  digests {
    sha256: "\315\016\236q\2704\a\215J\212\177\t\347-}|:\"\342X\351`-\247\254\206\024fw\004?\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-common"
    version: "17.3.0"
  }
  digests {
    sha256: "\300\b\\\245\373\240\017\021R\234\n\203\261(Q\352lY\233\274\310\257\375\222~\221K)\247CE\237"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.0.0"
  }
  digests {
    sha256: "\356H\276\020\252\270\365N\377\364\301Kw\321\036\020\271\356\356Cy\325\357k\362\227\242\222<U\314\021"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.odml"
    artifactId: "image"
    version: "1.0.0-beta1"
  }
  digests {
    sha256: ".q\2521\370:\224\025\'\177\021\235\346q\225ro\a\321v\016\225B\301\021w\2142\016:\241\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "common"
    version: "18.11.0"
  }
  digests {
    sha256: "f@\212\b\333\262FV\207\377]\320\310\366A\vVT\234\343\354\364I\331\to7\022(!1\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "barcode-scanning"
    version: "17.3.0"
  }
  digests {
    sha256: "d\305\317H\273\224\266\305O,x\\f\006@\247\321g\257\366\255r\274,\222\a\006\334A\207\267\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-barcode-scanning"
    version: "18.3.1"
  }
  digests {
    sha256: "\365\200\226\271\210gAzi\215\265]\337\327\310\355\003\203\262\021\177\250\363i/\247\001\326\023/\024b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-interfaces"
    version: "16.3.0"
  }
  digests {
    sha256: "\217\001\222\242m\001\267(\240\032\025\246 \330)\377\367\tX\"\202&\224C\212\223\022|\004\313\026\373"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-datetime"
    version: "0.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-datetime-jvm"
    version: "0.7.1"
  }
  digests {
    sha256: ";\213\230e|\232\377;\347\366\264\265u\351\363\373\232:\037E*\200\035\240\3648\220\0274\254\332\336"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-runtime"
    version: "4.3.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-runtime-android"
    version: "4.3.1"
  }
  digests {
    sha256: "\367\t* \252D\232v.\316\213q\264\022k\315\017\303\237\233K\373YU\335+\302\347o\276\034\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-api"
    version: "4.3.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-api-jvm"
    version: "4.3.1"
  }
  digests {
    sha256: "\246\347\203\033\263,f\264\343g\350\271K{Zr\217,\260\303\v\t\347\265O)\335\317I\335\347\036"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.benasher44"
    artifactId: "uuid"
    version: "0.8.2"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.benasher44"
    artifactId: "uuid-jvm"
    version: "0.8.2"
  }
  digests {
    sha256: "E \377\340\374\314\337\216r\310\316g\331\352eF\343\211F\273\341\034\302\226?\030d\276.\262\305\343"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-annotations"
    version: "4.3.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-annotations-jvm"
    version: "4.3.1"
  }
  digests {
    sha256: "\233\277F\344&\276\232t5\310\036\037q\241,\342\323\a\016e\351v\220O^\264P\3414\036p\214"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-mpp-utils"
    version: "4.3.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.apollographql.apollo"
    artifactId: "apollo-mpp-utils-jvm"
    version: "4.3.1"
  }
  digests {
    sha256: "\260\340\227\'\370Zb\227{\177\220\346/\210\277\245u\264h\vH\360j\a\367I\023\220nQ\345e"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "atomicfu"
    version: "0.26.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "atomicfu-jvm"
    version: "0.26.0"
  }
  digests {
    sha256: "\223|\"o\322\366*\3248N\322q\016\2567u\r\351P?ra+\214\317\255\266\330\377c\373\362"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.application"
    version: "7.0.7"
  }
  digests {
    sha256: "=\331\275x\254\003\320\206+\342C\b\000\210\037\343\207=\315\373\v\226\246\256`\314\323\251\372Xu\350"
  }
}
library {
  maven_library {
    groupId: "com.android.installreferrer"
    artifactId: "installreferrer"
    version: "2.2"
  }
  digests {
    sha256: "q\022\345\222\304)\224\227\207\301h\305\254br\021D\247;d\220PhEW\375\275\272]\323\266j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "expo.modules.asset"
    artifactId: "expo.modules.asset"
    version: "12.0.8"
  }
  digests {
    sha256: "^\220~\302&>\f!\370\356<&\304\230cR\345\330A\305\216\024]\303\331\357F\342\366\315\373\\"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.device"
    version: "8.0.7"
  }
  digests {
    sha256: "\332\001\210\233o\006\232\f\266\301\363\303\333>a\016\027\'\326\312ua\t\354\235\v\212\207]\241\n\216"
  }
}
library {
  maven_library {
    groupId: "com.facebook.device.yearclass"
    artifactId: "yearclass"
    version: "2.1.0"
  }
  digests {
    sha256: "\353\030\227*\vy\265\320\020\205\214\256\335\323\327\3566\212\231\202g\211g\030\004G\300c\202\352qs"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-v4"
    version: "1.0.0"
  }
  digests {
    sha256: "x\376\301H_\0178\212GI\002-\325\024\026\205q\'\315%D\256\034?\320\261e\211\005T\200\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.0.0"
  }
  digests {
    sha256: "\262;R{+\254\207\fJtQ\346\230-q2\344\023\350\215\177\'\333\353\037\307d\nr\f\331\356"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.1.0"
  }
  digests {
    sha256: "hN\017\374\262\032f\f\223\235\033\270\024R\037B\304\275t\201oJ\021r\221\376j\020\034\363\250\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.filesystem"
    version: "19.0.12"
  }
  digests {
    sha256: "$\354\267\321\"<\036\327\261\022\b\367\272\220\266\210\266\217i1kUz\245?\244\205\005`\357\363G"
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.10"
  }
  digests {
    sha256: "BA\337\251Nq\035C_)\244`J>-\345\304\252<\026^#\275\006k\346\374\037\3040\225i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.font"
    version: "14.0.8"
  }
  digests {
    sha256: "\260-\177\373\035Sx\325\250\'\305RL\325\311N\b5\f\031\214k\260\241\246\034\245\313\255\236F\023"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.keepawake"
    version: "15.0.7"
  }
  digests {
    sha256: "\247\253\213\326\265\262/?\244|\001B\253!\332L\n\266\334\245{\026N\373\301-\255>E8\016\016"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.localauthentication"
    version: "17.0.7"
  }
  digests {
    sha256: "\202\350\341 \306F\265\017\357\234\325\261oE\201\305c\262\377\315E\027\214\342J\235\265\301\247\257z\371"
  }
}
library {
  maven_library {
    groupId: "androidx.biometric"
    artifactId: "biometric"
    version: "1.2.0-alpha04"
  }
  digests {
    sha256: "\353\315t\033\336\322\317\304\333\261*\240\v\177\325u\357D\016`G\335A\300\002\272+\366\004\353p\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.notifications"
    version: "0.32.11"
  }
  digests {
    sha256: "\270\215\"\217\v\022\324\3246\217\375\rp;\003,\v8O\323\027s\364$\253(\027\354\212`\230x"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "2.1.20"
  }
  digests {
    sha256: "\215_\266\361\362\352\253w\231\227\320\222\203\303\017\367\353WA\352KT\335>\363-3r\245Z\377F"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "2.1.20"
  }
  digests {
    sha256: "\311\377\302\266\277\315\330\316z\321\336w\022\213\3606\217,\260o\337 \275\024\025\252D9\361\016\"\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.0.1"
  }
  digests {
    sha256: "\022q\304\240\247\347\265\305\236\026w3 \\]R< \b\337v\374X\235\265q\217\032o\355+p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "17.2.0"
  }
  digests {
    sha256: "\336\001\036j;ya\336c\217\027/,\266k\243\004\352\251\211f*\236+\017H\337\367t\314\301f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "me.leolin"
    artifactId: "ShortcutBadger"
    version: "1.1.22"
  }
  digests {
    sha256: "\315\026\020\334H\305i\222)P!\207\375\303\265\305C8p\325\252\261Y3!!\261\370\301\332\330\303"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.screenorientation"
    version: "9.0.7"
  }
  digests {
    sha256: "O}\362\340_e\277c\002K\323{6\334\233\025A\377\221,\320\311\253\2148\004\325\037rrh\315"
  }
}
library {
  maven_library {
    groupId: "host.exp.exponent"
    artifactId: "expo.modules.securestore"
    version: "15.0.7"
  }
  digests {
    sha256: "\302\301\262V\242\022L1K 6\317\005%\307\353\267\232E+\244\270\362\237\245\030\232\3170\204JE"
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-gif"
    version: "3.6.0"
  }
  digests {
    sha256: "\307\t\275\177\017\316x\002\230ma\002of\313\261: D\357\240\316\351\232\260P\0170\361\212 \327"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-base"
    version: "3.6.0"
  }
  digests {
    sha256: "\215\251\217\036\367\257I\350\210];r\026Z5+\243\341\222V\3463\361\247\251\213\371\352dru\255"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-options"
    version: "3.6.0"
  }
  digests {
    sha256: "VP=Y\311D.\204\032^\226\355\376~\365\t>gn\261\'\267w\334:\333\204\371[!Ct"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "animated-drawable"
    version: "3.6.0"
  }
  digests {
    sha256: ":\276\332\316\327\315{\217\250\3559\355\214\373\367\354\300\336*\v@>\nOcU\276\314q\000\vy"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-renderer"
    version: "3.6.0"
  }
  digests {
    sha256: "\r\243\022\312\264\352\020\0352+\266!\225GS\374\255\374+\217\323\353\235\\\314U\366$\276\016K\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "webpsupport"
    version: "3.6.0"
  }
  digests {
    sha256: "4\a\254HN\365[\263\242y\355\233\335\310_$\246\v\267w3\200\a\226q\223\304\004\004\372\217\317"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "hermes-android"
    version: "0.81.4"
  }
  digests {
    sha256: "\310\203\305\342K\023a\323\n\f/!\314@\276A)\026H\361\225\230\367\017*\001\216\221\r\226\260A"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 111
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 114
  library_dep_index: 115
  library_dep_index: 85
  library_dep_index: 86
  library_dep_index: 33
  library_dep_index: 19
  library_dep_index: 63
  library_dep_index: 106
  library_dep_index: 116
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 111
}
library_dependencies {
  library_index: 10
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 63
  library_dep_index: 71
  library_dep_index: 106
  library_dep_index: 54
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 73
  library_dep_index: 69
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 5
  library_dep_index: 0
  library_dep_index: 0
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 11
  library_dep_index: 11
}
library_dependencies {
  library_index: 14
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 42
  library_dep_index: 110
  library_dep_index: 0
  library_dep_index: 34
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 5
  library_dep_index: 17
}
library_dependencies {
  library_index: 18
  library_dep_index: 5
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 106
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 36
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 21
  library_dep_index: 5
}
library_dependencies {
  library_index: 22
  library_dep_index: 5
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 5
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 39
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 28
  library_dep_index: 0
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 30
}
library_dependencies {
  library_index: 29
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 0
}
library_dependencies {
  library_index: 30
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 0
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 10
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 63
  library_dep_index: 71
  library_dep_index: 107
  library_dep_index: 54
  library_dep_index: 108
  library_dep_index: 0
}
library_dependencies {
  library_index: 34
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 14
}
library_dependencies {
  library_index: 35
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 36
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 39
}
library_dependencies {
  library_index: 37
  library_dep_index: 5
  library_dep_index: 24
  library_dep_index: 24
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 39
}
library_dependencies {
  library_index: 38
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 36
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 39
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 40
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 41
  library_dep_index: 5
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 5
  library_dep_index: 43
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 42
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 5
  library_dep_index: 46
  library_dep_index: 19
  library_dep_index: 60
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 39
}
library_dependencies {
  library_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 29
  library_dep_index: 26
  library_dep_index: 48
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 50
  library_dep_index: 0
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
}
library_dependencies {
  library_index: 51
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 46
  library_dep_index: 44
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 46
  library_dep_index: 48
  library_dep_index: 0
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 5
  library_dep_index: 46
  library_dep_index: 34
  library_dep_index: 54
  library_dep_index: 54
  library_dep_index: 59
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 5
  library_dep_index: 5
  library_dep_index: 34
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 56
  library_dep_index: 52
  library_dep_index: 59
  library_dep_index: 0
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
  library_dep_index: 0
}
library_dependencies {
  library_index: 58
  library_dep_index: 57
  library_dep_index: 56
}
library_dependencies {
  library_index: 59
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 54
  library_dep_index: 52
  library_dep_index: 0
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 5
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 62
  library_dep_index: 19
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 5
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 62
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 39
}
library_dependencies {
  library_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 66
  library_dep_index: 5
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 24
  library_dep_index: 63
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 56
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 0
  library_dep_index: 39
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 74
  library_dep_index: 11
  library_dep_index: 11
  library_dep_index: 46
  library_dep_index: 50
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 105
  library_dep_index: 85
  library_dep_index: 44
  library_dep_index: 63
  library_dep_index: 70
  library_dep_index: 106
  library_dep_index: 52
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 26
  library_dep_index: 36
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 95
}
library_dependencies {
  library_index: 69
  library_dep_index: 10
  library_dep_index: 34
  library_dep_index: 60
  library_dep_index: 70
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 10
  library_dep_index: 73
}
library_dependencies {
  library_index: 70
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 60
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 71
  library_dep_index: 0
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 5
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 63
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 26
  library_dep_index: 56
  library_dep_index: 24
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 35
  library_dep_index: 40
  library_dep_index: 19
  library_dep_index: 44
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 70
  library_dep_index: 0
  library_dep_index: 60
  library_dep_index: 39
}
library_dependencies {
  library_index: 73
  library_dep_index: 69
  library_dep_index: 46
  library_dep_index: 50
  library_dep_index: 67
  library_dep_index: 63
  library_dep_index: 0
  library_dep_index: 10
  library_dep_index: 69
}
library_dependencies {
  library_index: 74
  library_dep_index: 14
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 5
  library_dep_index: 46
  library_dep_index: 77
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 0
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 46
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 104
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 46
  library_dep_index: 75
  library_dep_index: 77
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 46
  library_dep_index: 50
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 85
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 87
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 85
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 85
  library_dep_index: 85
}
library_dependencies {
  library_index: 87
  library_dep_index: 88
}
library_dependencies {
  library_index: 88
  library_dep_index: 73
  library_dep_index: 5
  library_dep_index: 89
  library_dep_index: 97
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 24
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 100
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 89
  library_dep_index: 90
}
library_dependencies {
  library_index: 90
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 77
  library_dep_index: 0
  library_dep_index: 91
  library_dep_index: 0
}
library_dependencies {
  library_index: 91
  library_dep_index: 92
}
library_dependencies {
  library_index: 92
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 79
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 89
  library_dep_index: 0
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 94
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 91
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 0
  library_dep_index: 95
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 14
  library_dep_index: 85
  library_dep_index: 0
  library_dep_index: 93
  library_dep_index: 0
}
library_dependencies {
  library_index: 97
  library_dep_index: 91
  library_dep_index: 95
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 83
  library_dep_index: 2
  library_dep_index: 89
  library_dep_index: 93
  library_dep_index: 77
}
library_dependencies {
  library_index: 98
  library_dep_index: 67
  library_dep_index: 0
}
library_dependencies {
  library_index: 99
  library_dep_index: 95
  library_dep_index: 46
  library_dep_index: 2
  library_dep_index: 89
  library_dep_index: 77
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
}
library_dependencies {
  library_index: 101
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 46
  library_dep_index: 67
  library_dep_index: 0
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
}
library_dependencies {
  library_index: 103
  library_dep_index: 5
  library_dep_index: 46
  library_dep_index: 2
  library_dep_index: 67
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 87
  library_dep_index: 100
  library_dep_index: 81
  library_dep_index: 77
  library_dep_index: 2
}
library_dependencies {
  library_index: 104
  library_dep_index: 14
  library_dep_index: 0
}
library_dependencies {
  library_index: 105
  library_dep_index: 34
  library_dep_index: 0
}
library_dependencies {
  library_index: 106
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 41
  library_dep_index: 17
}
library_dependencies {
  library_index: 107
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 38
  library_dep_index: 63
}
library_dependencies {
  library_index: 108
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 109
}
library_dependencies {
  library_index: 109
  library_dep_index: 5
  library_dep_index: 14
}
library_dependencies {
  library_index: 110
  library_dep_index: 5
  library_dep_index: 11
}
library_dependencies {
  library_index: 111
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 9
}
library_dependencies {
  library_index: 112
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 113
  library_dep_index: 112
  library_dep_index: 18
  library_dep_index: 11
}
library_dependencies {
  library_index: 114
  library_dep_index: 5
}
library_dependencies {
  library_index: 115
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 109
}
library_dependencies {
  library_index: 116
  library_dep_index: 5
}
library_dependencies {
  library_index: 117
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 24
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 118
  library_dep_index: 119
}
library_dependencies {
  library_index: 119
  library_dep_index: 120
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
  library_dep_index: 125
  library_dep_index: 0
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
}
library_dependencies {
  library_index: 122
  library_dep_index: 4
  library_dep_index: 2
  library_dep_index: 123
}
library_dependencies {
  library_index: 123
  library_dep_index: 124
}
library_dependencies {
  library_index: 124
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 126
  library_dep_index: 4
  library_dep_index: 121
  library_dep_index: 2
}
library_dependencies {
  library_index: 127
  library_dep_index: 9
  library_dep_index: 111
  library_dep_index: 74
  library_dep_index: 128
  library_dep_index: 42
  library_dep_index: 129
  library_dep_index: 131
  library_dep_index: 154
  library_dep_index: 138
  library_dep_index: 136
  library_dep_index: 145
  library_dep_index: 134
  library_dep_index: 158
  library_dep_index: 146
  library_dep_index: 159
  library_dep_index: 155
  library_dep_index: 156
  library_dep_index: 160
  library_dep_index: 0
}
library_dependencies {
  library_index: 128
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 18
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
}
library_dependencies {
  library_index: 131
  library_dep_index: 132
  library_dep_index: 130
  library_dep_index: 136
  library_dep_index: 137
  library_dep_index: 138
  library_dep_index: 133
  library_dep_index: 139
  library_dep_index: 140
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 152
  library_dep_index: 153
  library_dep_index: 0
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
  library_dep_index: 134
}
library_dependencies {
  library_index: 133
  library_dep_index: 14
  library_dep_index: 0
}
library_dependencies {
  library_index: 134
  library_dep_index: 135
  library_dep_index: 130
}
library_dependencies {
  library_index: 136
  library_dep_index: 133
  library_dep_index: 0
}
library_dependencies {
  library_index: 137
  library_dep_index: 0
}
library_dependencies {
  library_index: 138
  library_dep_index: 133
  library_dep_index: 136
  library_dep_index: 0
}
library_dependencies {
  library_index: 139
  library_dep_index: 133
  library_dep_index: 140
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 136
  library_dep_index: 138
  library_dep_index: 137
  library_dep_index: 0
}
library_dependencies {
  library_index: 140
  library_dep_index: 130
  library_dep_index: 135
  library_dep_index: 141
  library_dep_index: 133
  library_dep_index: 138
  library_dep_index: 142
  library_dep_index: 0
  library_dep_index: 144
}
library_dependencies {
  library_index: 142
  library_dep_index: 133
  library_dep_index: 137
  library_dep_index: 143
  library_dep_index: 0
}
library_dependencies {
  library_index: 143
  library_dep_index: 138
  library_dep_index: 0
}
library_dependencies {
  library_index: 144
  library_dep_index: 145
  library_dep_index: 135
  library_dep_index: 141
  library_dep_index: 133
  library_dep_index: 138
  library_dep_index: 0
}
library_dependencies {
  library_index: 145
  library_dep_index: 146
  library_dep_index: 147
}
library_dependencies {
  library_index: 148
  library_dep_index: 140
  library_dep_index: 133
  library_dep_index: 134
}
library_dependencies {
  library_index: 149
  library_dep_index: 133
  library_dep_index: 140
}
library_dependencies {
  library_index: 150
  library_dep_index: 133
  library_dep_index: 140
  library_dep_index: 148
  library_dep_index: 130
}
library_dependencies {
  library_index: 151
  library_dep_index: 133
  library_dep_index: 140
  library_dep_index: 148
}
library_dependencies {
  library_index: 152
  library_dep_index: 140
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 130
  library_dep_index: 141
  library_dep_index: 133
}
library_dependencies {
  library_index: 153
  library_dep_index: 144
  library_dep_index: 130
  library_dep_index: 141
  library_dep_index: 133
}
library_dependencies {
  library_index: 154
  library_dep_index: 133
  library_dep_index: 140
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 138
  library_dep_index: 155
  library_dep_index: 0
}
library_dependencies {
  library_index: 155
  library_dep_index: 156
  library_dep_index: 4
}
library_dependencies {
  library_index: 156
  library_dep_index: 157
}
library_dependencies {
  library_index: 157
  library_dep_index: 0
}
library_dependencies {
  library_index: 159
  library_dep_index: 155
  library_dep_index: 4
}
library_dependencies {
  library_index: 161
  library_dep_index: 162
}
library_dependencies {
  library_index: 162
  library_dep_index: 0
}
library_dependencies {
  library_index: 163
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 18
  library_dep_index: 17
}
library_dependencies {
  library_index: 164
  library_dep_index: 11
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 109
}
library_dependencies {
  library_index: 165
  library_dep_index: 5
  library_dep_index: 9
  library_dep_index: 166
  library_dep_index: 164
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 33
  library_dep_index: 19
  library_dep_index: 167
  library_dep_index: 168
  library_dep_index: 112
  library_dep_index: 169
}
library_dependencies {
  library_index: 166
  library_dep_index: 5
}
library_dependencies {
  library_index: 167
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 109
  library_dep_index: 11
}
library_dependencies {
  library_index: 168
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 169
  library_dep_index: 5
  library_dep_index: 33
  library_dep_index: 167
  library_dep_index: 14
  library_dep_index: 11
}
library_dependencies {
  library_index: 171
  library_dep_index: 172
}
library_dependencies {
  library_index: 172
  library_dep_index: 73
  library_dep_index: 0
  library_dep_index: 173
}
library_dependencies {
  library_index: 173
  library_dep_index: 95
  library_dep_index: 174
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 190
  library_dep_index: 178
  library_dep_index: 179
  library_dep_index: 189
  library_dep_index: 186
  library_dep_index: 0
}
library_dependencies {
  library_index: 174
  library_dep_index: 89
  library_dep_index: 175
  library_dep_index: 177
  library_dep_index: 190
  library_dep_index: 178
  library_dep_index: 179
  library_dep_index: 185
  library_dep_index: 186
  library_dep_index: 0
}
library_dependencies {
  library_index: 175
  library_dep_index: 91
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 179
  library_dep_index: 188
  library_dep_index: 186
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 176
  library_dep_index: 5
}
library_dependencies {
  library_index: 177
  library_dep_index: 11
  library_dep_index: 0
}
library_dependencies {
  library_index: 178
  library_dep_index: 46
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 179
  library_dep_index: 67
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 182
  library_dep_index: 183
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 184
  library_dep_index: 185
  library_dep_index: 187
  library_dep_index: 189
  library_dep_index: 188
  library_dep_index: 186
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 180
  library_dep_index: 24
  library_dep_index: 176
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 181
  library_dep_index: 21
  library_dep_index: 19
  library_dep_index: 180
  library_dep_index: 176
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 182
  library_dep_index: 44
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 176
  library_dep_index: 178
  library_dep_index: 0
}
library_dependencies {
  library_index: 183
  library_dep_index: 63
  library_dep_index: 176
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 184
  library_dep_index: 50
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 0
}
library_dependencies {
  library_index: 185
  library_dep_index: 75
  library_dep_index: 178
  library_dep_index: 186
  library_dep_index: 0
}
library_dependencies {
  library_index: 186
  library_dep_index: 77
  library_dep_index: 177
  library_dep_index: 0
}
library_dependencies {
  library_index: 187
  library_dep_index: 79
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 185
  library_dep_index: 188
  library_dep_index: 186
  library_dep_index: 0
}
library_dependencies {
  library_index: 188
  library_dep_index: 81
  library_dep_index: 176
  library_dep_index: 178
  library_dep_index: 185
  library_dep_index: 186
  library_dep_index: 0
}
library_dependencies {
  library_index: 189
  library_dep_index: 83
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 184
  library_dep_index: 185
  library_dep_index: 187
  library_dep_index: 188
  library_dep_index: 186
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 190
  library_dep_index: 93
  library_dep_index: 176
  library_dep_index: 177
  library_dep_index: 178
  library_dep_index: 179
  library_dep_index: 186
  library_dep_index: 0
}
library_dependencies {
  library_index: 191
  library_dep_index: 0
}
library_dependencies {
  library_index: 192
  library_dep_index: 193
}
library_dependencies {
  library_index: 193
  library_dep_index: 10
  library_dep_index: 73
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 89
  library_dep_index: 91
  library_dep_index: 93
  library_dep_index: 46
  library_dep_index: 50
  library_dep_index: 67
  library_dep_index: 24
  library_dep_index: 44
  library_dep_index: 63
  library_dep_index: 65
  library_dep_index: 65
  library_dep_index: 71
  library_dep_index: 194
  library_dep_index: 196
  library_dep_index: 54
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 56
  library_dep_index: 194
  library_dep_index: 196
  library_dep_index: 0
}
library_dependencies {
  library_index: 194
  library_dep_index: 195
}
library_dependencies {
  library_index: 195
  library_dep_index: 5
  library_dep_index: 11
  library_dep_index: 34
  library_dep_index: 24
  library_dep_index: 19
  library_dep_index: 63
  library_dep_index: 71
  library_dep_index: 106
  library_dep_index: 54
  library_dep_index: 59
  library_dep_index: 0
  library_dep_index: 56
  library_dep_index: 192
  library_dep_index: 196
  library_dep_index: 0
}
library_dependencies {
  library_index: 196
  library_dep_index: 197
}
library_dependencies {
  library_index: 197
  library_dep_index: 69
  library_dep_index: 15
  library_dep_index: 11
  library_dep_index: 34
  library_dep_index: 24
  library_dep_index: 19
  library_dep_index: 60
  library_dep_index: 63
  library_dep_index: 70
  library_dep_index: 194
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 56
  library_dep_index: 194
  library_dep_index: 192
  library_dep_index: 0
}
library_dependencies {
  library_index: 198
  library_dep_index: 10
  library_dep_index: 199
  library_dep_index: 200
  library_dep_index: 201
  library_dep_index: 205
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 206
  library_dep_index: 202
  library_dep_index: 204
  library_dep_index: 209
  library_dep_index: 213
}
library_dependencies {
  library_index: 199
  library_dep_index: 5
}
library_dependencies {
  library_index: 200
  library_dep_index: 199
  library_dep_index: 201
  library_dep_index: 202
  library_dep_index: 204
  library_dep_index: 5
}
library_dependencies {
  library_index: 201
  library_dep_index: 199
  library_dep_index: 5
  library_dep_index: 160
  library_dep_index: 202
  library_dep_index: 203
}
library_dependencies {
  library_index: 202
  library_dep_index: 5
}
library_dependencies {
  library_index: 203
  library_dep_index: 5
  library_dep_index: 202
}
library_dependencies {
  library_index: 204
  library_dep_index: 5
  library_dep_index: 202
}
library_dependencies {
  library_index: 205
  library_dep_index: 11
  library_dep_index: 14
  library_dep_index: 33
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 206
  library_dep_index: 207
  library_dep_index: 5
  library_dep_index: 208
}
library_dependencies {
  library_index: 207
  library_dep_index: 160
}
library_dependencies {
  library_index: 209
  library_dep_index: 32
  library_dep_index: 210
}
library_dependencies {
  library_index: 210
  library_dep_index: 211
  library_dep_index: 199
  library_dep_index: 200
  library_dep_index: 201
  library_dep_index: 205
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 212
  library_dep_index: 206
  library_dep_index: 202
  library_dep_index: 204
  library_dep_index: 213
}
library_dependencies {
  library_index: 211
  library_dep_index: 5
}
library_dependencies {
  library_index: 213
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 199
  library_dep_index: 200
  library_dep_index: 201
  library_dep_index: 205
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 206
  library_dep_index: 202
  library_dep_index: 204
}
library_dependencies {
  library_index: 214
  library_dep_index: 32
  library_dep_index: 215
  library_dep_index: 209
  library_dep_index: 213
  library_dep_index: 210
}
library_dependencies {
  library_index: 215
  library_dep_index: 199
  library_dep_index: 200
  library_dep_index: 201
  library_dep_index: 205
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 212
  library_dep_index: 206
  library_dep_index: 202
  library_dep_index: 204
  library_dep_index: 209
  library_dep_index: 213
  library_dep_index: 210
  library_dep_index: 216
}
library_dependencies {
  library_index: 216
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 217
  library_dep_index: 218
}
library_dependencies {
  library_index: 218
  library_dep_index: 0
}
library_dependencies {
  library_index: 219
  library_dep_index: 220
}
library_dependencies {
  library_index: 220
  library_dep_index: 5
  library_dep_index: 34
  library_dep_index: 155
  library_dep_index: 221
  library_dep_index: 227
  library_dep_index: 156
  library_dep_index: 223
  library_dep_index: 26
  library_dep_index: 229
}
library_dependencies {
  library_index: 221
  library_dep_index: 222
}
library_dependencies {
  library_index: 222
  library_dep_index: 156
  library_dep_index: 223
  library_dep_index: 225
}
library_dependencies {
  library_index: 223
  library_dep_index: 224
}
library_dependencies {
  library_index: 224
  library_dep_index: 0
}
library_dependencies {
  library_index: 225
  library_dep_index: 226
}
library_dependencies {
  library_index: 226
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 227
  library_dep_index: 228
}
library_dependencies {
  library_index: 228
  library_dep_index: 225
}
library_dependencies {
  library_index: 229
  library_dep_index: 230
}
library_dependencies {
  library_index: 230
  library_dep_index: 0
}
library_dependencies {
  library_index: 231
  library_dep_index: 3
  library_dep_index: 232
}
library_dependencies {
  library_index: 233
  library_dep_index: 3
}
library_dependencies {
  library_index: 234
  library_dep_index: 3
  library_dep_index: 235
  library_dep_index: 236
}
library_dependencies {
  library_index: 236
  library_dep_index: 14
  library_dep_index: 237
  library_dep_index: 238
  library_dep_index: 242
  library_dep_index: 33
}
library_dependencies {
  library_index: 237
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 110
}
library_dependencies {
  library_index: 238
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 239
  library_dep_index: 107
  library_dep_index: 240
  library_dep_index: 241
}
library_dependencies {
  library_index: 239
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 36
}
library_dependencies {
  library_index: 240
  library_dep_index: 5
}
library_dependencies {
  library_index: 241
  library_dep_index: 5
}
library_dependencies {
  library_index: 242
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 238
  library_dep_index: 109
  library_dep_index: 108
  library_dep_index: 164
  library_dep_index: 115
  library_dep_index: 243
  library_dep_index: 18
  library_dep_index: 128
  library_dep_index: 244
  library_dep_index: 114
}
library_dependencies {
  library_index: 243
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 109
}
library_dependencies {
  library_index: 244
  library_dep_index: 5
  library_dep_index: 14
}
library_dependencies {
  library_index: 245
  library_dep_index: 3
  library_dep_index: 246
  library_dep_index: 7
  library_dep_index: 155
  library_dep_index: 159
  library_dep_index: 156
  library_dep_index: 236
  library_dep_index: 239
}
library_dependencies {
  library_index: 247
  library_dep_index: 3
  library_dep_index: 127
}
library_dependencies {
  library_index: 248
  library_dep_index: 3
}
library_dependencies {
  library_index: 249
  library_dep_index: 3
  library_dep_index: 250
}
library_dependencies {
  library_index: 250
  library_dep_index: 10
  library_dep_index: 9
  library_dep_index: 35
  library_dep_index: 63
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 14
  library_dep_index: 33
}
library_dependencies {
  library_index: 251
  library_dep_index: 252
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 40
  library_dep_index: 37
  library_dep_index: 29
  library_dep_index: 254
  library_dep_index: 264
}
library_dependencies {
  library_index: 252
  library_dep_index: 0
  library_dep_index: 253
}
library_dependencies {
  library_index: 253
  library_dep_index: 0
}
library_dependencies {
  library_index: 254
  library_dep_index: 255
  library_dep_index: 256
  library_dep_index: 206
  library_dep_index: 257
  library_dep_index: 202
  library_dep_index: 204
  library_dep_index: 203
  library_dep_index: 258
  library_dep_index: 259
  library_dep_index: 260
  library_dep_index: 261
  library_dep_index: 5
  library_dep_index: 199
  library_dep_index: 200
  library_dep_index: 201
  library_dep_index: 205
  library_dep_index: 32
  library_dep_index: 262
  library_dep_index: 263
  library_dep_index: 31
  library_dep_index: 208
  library_dep_index: 0
}
library_dependencies {
  library_index: 255
  library_dep_index: 30
  library_dep_index: 206
  library_dep_index: 207
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 256
  library_dep_index: 255
  library_dep_index: 4
  library_dep_index: 206
  library_dep_index: 207
}
library_dependencies {
  library_index: 257
  library_dep_index: 199
  library_dep_index: 201
  library_dep_index: 200
  library_dep_index: 5
}
library_dependencies {
  library_index: 258
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 259
  library_dep_index: 260
  library_dep_index: 0
  library_dep_index: 31
  library_dep_index: 207
  library_dep_index: 255
  library_dep_index: 256
  library_dep_index: 206
}
library_dependencies {
  library_index: 260
  library_dep_index: 31
  library_dep_index: 207
}
library_dependencies {
  library_index: 261
  library_dep_index: 32
  library_dep_index: 207
}
library_dependencies {
  library_index: 262
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 263
  library_dep_index: 238
  library_dep_index: 32
}
library_dependencies {
  library_index: 265
  library_dep_index: 3
}
library_dependencies {
  library_index: 266
  library_dep_index: 3
  library_dep_index: 250
}
library_dependencies {
  library_index: 267
  library_dep_index: 141
  library_dep_index: 130
  library_dep_index: 133
  library_dep_index: 268
}
library_dependencies {
  library_index: 268
  library_dep_index: 141
  library_dep_index: 269
  library_dep_index: 138
  library_dep_index: 133
  library_dep_index: 144
  library_dep_index: 140
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 270
  library_dep_index: 0
}
library_dependencies {
  library_index: 269
  library_dep_index: 139
  library_dep_index: 133
  library_dep_index: 140
  library_dep_index: 0
}
library_dependencies {
  library_index: 270
  library_dep_index: 144
  library_dep_index: 139
  library_dep_index: 133
  library_dep_index: 271
  library_dep_index: 269
  library_dep_index: 0
}
library_dependencies {
  library_index: 271
  library_dep_index: 0
}
library_dependencies {
  library_index: 272
  library_dep_index: 130
  library_dep_index: 141
  library_dep_index: 133
  library_dep_index: 144
  library_dep_index: 138
}
library_dependencies {
  library_index: 273
  library_dep_index: 129
  library_dep_index: 158
  library_dep_index: 5
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 127
  dependency_index: 267
  dependency_index: 272
  dependency_index: 273
  dependency_index: 231
  dependency_index: 233
  dependency_index: 234
  dependency_index: 245
  dependency_index: 247
  dependency_index: 248
  dependency_index: 249
  dependency_index: 251
  dependency_index: 265
  dependency_index: 266
  dependency_index: 3
  dependency_index: 5
  dependency_index: 7
  dependency_index: 8
  dependency_index: 9
  dependency_index: 117
  dependency_index: 118
  dependency_index: 119
  dependency_index: 134
  dependency_index: 155
  dependency_index: 170
  dependency_index: 26
  dependency_index: 29
  dependency_index: 191
  dependency_index: 96
  dependency_index: 67
  dependency_index: 87
  dependency_index: 192
  dependency_index: 198
  dependency_index: 214
  dependency_index: 217
  dependency_index: 219
  dependency_index: 171
  dependency_index: 161
  dependency_index: 163
  dependency_index: 164
  dependency_index: 165
  dependency_index: 168
  dependency_index: 34
  dependency_index: 43
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://central.sonatype.com/repository/maven-snapshots/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
