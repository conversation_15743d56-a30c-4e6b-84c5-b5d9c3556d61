-- Merging decision tree log ---
manifest
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:1:1-44:12
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:1:1-44:12
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:1:1-44:12
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:1:1-44:12
MERGED from [:expo] D:\Projetos\TecBizExpoApp\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-13:12
MERGED from [:expo-dev-menu] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu-interface] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.font:14.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0199161e8ab7230eb657afbe5cc08a3b\transformed\expo.modules.font-14.0.8\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6997bfbb77c616381810af9ce1fe1f5\transformed\react-android-0.81.4-release\AndroidManifest.xml:2:1-12:12
MERGED from [:expo-constants] D:\Projetos\TecBizExpoApp\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] D:\Projetos\TecBizExpoApp\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] D:\Projetos\TecBizExpoApp\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] D:\Projetos\TecBizExpoApp\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:7.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a7cfe2550a19549038dcf98bd3d86b4\transformed\expo.modules.application-7.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:12.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99ffc4e96bf1e2f63cba4f352fb21434\transformed\expo.modules.asset-12.0.8\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.device:8.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac72a032a8878ebad40e33f6147ffdb8\transformed\expo.modules.device-8.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:19.0.12] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0e053a568b0c2a0f58626bf908c71f52\transformed\expo.modules.filesystem-19.0.12\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.keepawake:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\38d14400a2d5e4ddf13e945710f1944c\transformed\expo.modules.keepawake-15.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:2:1-43:12
MERGED from [host.exp.exponent:expo.modules.screenorientation:9.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b09029df9b9fdf2fef002b8635560cef\transformed\expo.modules.screenorientation-9.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.securestore:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19e2d333207faf7bb3b972a2b59942f0\transformed\expo.modules.securestore-15.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f4487991d53ea2ffbe2e4604920703d6\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.apollographql.apollo:apollo-runtime-android:4.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\422e91ae3fee7377d30b3b992f112273\transformed\apollo-runtime-release\AndroidManifest.xml:2:1-8:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\93b576e42733cef862a954c928112e8c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7b66ccce72f4e6fb908076962b89f8c\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0a2a6591dced4295b60b85002f8c8409\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7791bdf654876e6ba13c0757ee9ffed2\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6873cdaa6bfa0f911242290759fcfae\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a029fe8b8605891436e34332cff561\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\948281eefeb2ac423daef145cd8a3d63\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\deb9b1b93c9197dbf065a8c2a0c4b6f8\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\630e002fb78825169bcbedfd3becd945\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2617a59ab69d3cbb4d67d12e7172f9c8\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c45874b1b2dd68247585d37c44b265c5\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a62a50e9904e8d9d87c500591ee64cec\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11b30660fec58d2511abd54b1f5bfa02\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1be95b060ff0de27c9abd6913a2ec533\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2a8fb07010759952bf90ce0e80cd124\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ba46862941f89ac7149e9bbf2afa70ea\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32d3663f7533af91348424e01ec0d719\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18e88ed8c92f5699bc86941876f9b82\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3be4fb4599aa69685df676e23c1d435e\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7f734b4776f656b30f9f564abafee82a\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e502c92cd4162352bba0eb4051bb287a\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cec4ec87a0c48a94d459216f8dda060d\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e73162b71c728692a0e73647a17ce97d\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bd79fe3d971c00a9c69a6fc30124a89a\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\497b53a2103d9dab55d7751334c2a0b7\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcb549dbc855619e30027cf9153600f3\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c011fb53f7a2aa28824c31e90f656387\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7749b0e07ced936880eb5ff4a52d87c6\transformed\material-ripple-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.composables:core-android:1.37.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df8bcecc49fccce54e9e67dd4295a710\transformed\core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac9f14a47da32b84e694d05981f0518f\transformed\foundation-layout\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2359395d430481f036982ea0f5c8dae4\transformed\animation-core\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92cb98e81fed3d3b50619a35279019d0\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bb83775a718fa1878d483da48b985d9\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df45c221466a4106dc9374b4735bc533\transformed\ui-tooling-data\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5de9532f0a4fae909792cf997622c2\transformed\ui-unit\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11fd102c6c432d0a7e1f8a37852e02b8\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\499ccb90ba39bfb04188e42fb6dde75b\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8575d88d0c1fcbcbf2d0788d3102a584\transformed\ui-util\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b513094464c80e93b7db2e5bc221f53e\transformed\ui-text\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ea15573963922ccc058638b436218bd\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30c9c3522d01250988a84182945182d7\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24f75fe43a1dfc2cd0e4ee1767fb7a34\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d1bc41ea89e21615eddf9863861a32d\transformed\runtime-annotation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8195b6be121f81eccab7be9c7ac45f83\transformed\runtime-saveable\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\861839787bd11f700d8ba4a2bc58d182\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ec819bb4f8b327e6e4eae3361ec6e50\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10558616e97b62882262ebd437660138\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7eac873f9e9e9d0ef17272d1cafd1664\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb6f6e5d4b0be6ab8ca06969f4ad2e47\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfd1ac0f786923d01f25f12719618681\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7ae61c3b313c8db9701f97884214dd26\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c8b3ac0d0a1c4de61c4a8165bf1b104\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4f4761fcd58deeb85cf76c2e6dff1ca\transformed\documentfile-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7824f5704a8615d900e1bbc06f70d683\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a07e4dd9491bb3b255df6a59e2483943\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3925f987960bcdf2b8a2477ce730ea1b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d97cddcb237db80b430148e2000c049e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35c10ae8011164342dcf8561fd64efea\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a02035e21744ea944cae4c099374c9d0\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b0df1f54e47f937b3a436e7eaa6be195\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ef1d6332463d11dacfeb288b03c0bf38\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\17a21103ff574ee4212c28297f5d53b4\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da1abaface34e688008971d372cf60e\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6dd6e18c95dcc846d8cd2cc4971042d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aab617897d5efee02f2a2b67997cb35e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5165c706d0552969a898bd541f832e5\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\348019051576c774dcdb2baa24a765ff\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dad18988ecdcbd5cc410865e9eb67f40\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e17dac39dfada490e31bdb8abe4c3c1\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bba1d21875594204cebffa52ee9faa9a\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87c62ead478518fad06a6c791292b9fc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3a49a4c8a73dfd19eb58dfd431d233d0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c764c1d3666dc7fb74b57b77f5d8a34\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9900611c7f0496db5ba0e24c27e34642\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f3d7d25699208affda5e3d4c44d54862\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5a94f85a85d6bc95199dd305b8be44c4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a2fd120a9584085d2612537f4b414003\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f30fdc996636fc4d1e5f4da5f787459\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\341d65447503e294f53c11a98be04379\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c46ca3c85a79013ee8f827a1bfd1c979\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\326cd173aa233264a91dd5bbf2598610\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab4e70f01f1243dec7d1ff49d1e2575b\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c3976769562be0e4763bbc0f21712e6\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85a1f7086b7960f77f97b4af68a04f6d\transformed\runtime\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\762f4dc9aebf37bfc66de375dc543aba\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b4f01381b71eb2d1673f09999325396c\transformed\activity-1.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3db2f0b3509c4b3d08a81d0a67364ce\transformed\activity-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ada150d16c90f6fd21ac4f985429d84f\transformed\activity-compose-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ab80b900a609c386bd54943bf883268\transformed\material-icons-core-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c4d48eeb7f4b99a4a4e7d71de689573a\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182c514307c9cd7fb6130c217b650409\transformed\material-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f868bcdea222abfc5b941e0bc81954be\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9762a49ed3bf5dde0fb03455ec9600db\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8b23e8b30b401c4dd9168814489a0810\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2b33c66a03d9da1c6efc549e5037b53\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2951e3f7b53ce4974f38201822e09993\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.lukmccall:radix-ui-colors-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82cb00a830756bccb4f7e540c06f3138\transformed\library-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2fcc8f3df6680f0cc0691d14a8b859ae\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.facebook.react:hermes-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5c6f2f9e4b267c66b1b7d248bb2806\transformed\hermes-android-0.81.4-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b675be355ba59956683d034f19582563\transformed\viewbinding-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\07a6b3cdcdd40ead0f7a3f729962584b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3b97eec65a39280db013b98577adad14\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f33a32db8d06dc21097da2718df72823\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\56e7383d55b65ca83dd3ec0f83f1270c\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfc839453757451d65f1d7d2b231aeb7\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f532a9ff664b5c763585b87d177a407a\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d4e0b17f83e0c03ee07b999bc13b478\transformed\exifinterface-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfa7797f2c61aec7af3c5e9231aaf369\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\78ed5ce977e413a298fbda2b68b37ae3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f11fdf5ee99868dcbb95bb7170064472\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cd54390103da05e9f0b933f516c6d0cd\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19f4c919a8c81ff433987fd7c8d9709b\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.NOTIFICATIONS
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:3:3-69
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:3:20-67
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:4:3-77
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:4:20-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:5:3-78
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:7:5-81
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:5:20-76
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:6:3-75
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:6:20-73
uses-permission#android.permission.USE_BIOMETRIC
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:7:3-69
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:8:5-72
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:8:5-72
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:24:5-72
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:7:20-67
uses-permission#android.permission.USE_FINGERPRINT
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:8:3-71
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:7:5-74
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:7:5-74
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:27:5-74
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:8:20-69
uses-permission#android.permission.VIBRATE
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:9:3-63
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:9:20-61
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:10:3-65
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:10:20-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:11:3-78
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:11:20-76
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:12:3-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:12:20-77
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:13:3-76
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:13:20-74
queries
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:14:3-20:13
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:15:5-19:14
action#android.intent.action.VIEW
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:16:7-58
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:16:15-56
category#android.intent.category.BROWSABLE
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:17:7-67
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:17:17-65
data
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:7-37
	android:scheme
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:13-35
application
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:3-43:17
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:3-43:17
MERGED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-11:19
MERGED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:10:5-41:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:8:5-24:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:116-161
	android:largeHeap
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:292-317
	android:icon
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:81-115
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:221-247
	android:label
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:48-80
	android:hardwareAccelerated
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:318-352
	android:fullBackupContent
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:353-411
	android:allowBackup
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:162-188
	android:theme
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:189-220
	android:enableOnBackInvokedCallback
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:248-291
	android:dataExtractionRules
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:412-481
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:16-47
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:22:5-118
	android:value
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:22:93-116
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:22:16-92
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:23:5-139
	android:resource
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:23:88-137
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:23:16-87
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:24:5-135
	android:resource
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:24:87-133
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:24:16-86
meta-data#expo.modules.notifications.default_notification_color
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:25:5-136
	android:resource
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:25:85-134
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:25:16-84
meta-data#expo.modules.notifications.default_notification_icon
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:26:5-132
	android:resource
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:26:84-130
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:26:16-83
meta-data#expo.modules.updates.ENABLED
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:27:5-83
	android:value
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:27:60-81
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:27:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:28:5-105
	android:value
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:28:81-103
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:28:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:29:5-99
	android:value
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:29:80-97
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:29:16-79
activity#com.tecbiz.tecbizassociadospush.MainActivity
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:5-42:16
	android:maxAspectRatio
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:357-385
	android:screenOrientation
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:245-281
	android:launchMode
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:143-174
	android:windowSoftInputMode
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:386-428
	android:exported
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:221-244
	android:supportsPictureInPicture
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:316-356
	android:resizeableActivity
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:282-315
	android:configChanges
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:44-142
	android:theme
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:175-220
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:31:7-34:23
action#android.intent.action.MAIN
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:32:9-60
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:32:17-58
category#android.intent.category.LAUNCHER
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:33:9-68
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:33:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+tecbizexpoapp+data:scheme:tecbizapp
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:35:7-41:23
category#android.intent.category.DEFAULT
ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:37:9-67
	android:name
		ADDED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:37:19-65
uses-sdk
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
MERGED from [:expo] D:\Projetos\TecBizExpoApp\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\Projetos\TecBizExpoApp\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.font:14.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0199161e8ab7230eb657afbe5cc08a3b\transformed\expo.modules.font-14.0.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:14.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0199161e8ab7230eb657afbe5cc08a3b\transformed\expo.modules.font-14.0.8\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6997bfbb77c616381810af9ce1fe1f5\transformed\react-android-0.81.4-release\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d6997bfbb77c616381810af9ce1fe1f5\transformed\react-android-0.81.4-release\AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] D:\Projetos\TecBizExpoApp\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\Projetos\TecBizExpoApp\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\Projetos\TecBizExpoApp\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\Projetos\TecBizExpoApp\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\Projetos\TecBizExpoApp\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\Projetos\TecBizExpoApp\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\Projetos\TecBizExpoApp\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\Projetos\TecBizExpoApp\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:7.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a7cfe2550a19549038dcf98bd3d86b4\transformed\expo.modules.application-7.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:7.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1a7cfe2550a19549038dcf98bd3d86b4\transformed\expo.modules.application-7.0.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:12.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99ffc4e96bf1e2f63cba4f352fb21434\transformed\expo.modules.asset-12.0.8\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:12.0.8] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\99ffc4e96bf1e2f63cba4f352fb21434\transformed\expo.modules.asset-12.0.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:8.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac72a032a8878ebad40e33f6147ffdb8\transformed\expo.modules.device-8.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:8.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac72a032a8878ebad40e33f6147ffdb8\transformed\expo.modules.device-8.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:19.0.12] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0e053a568b0c2a0f58626bf908c71f52\transformed\expo.modules.filesystem-19.0.12\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:19.0.12] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0e053a568b0c2a0f58626bf908c71f52\transformed\expo.modules.filesystem-19.0.12\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\38d14400a2d5e4ddf13e945710f1944c\transformed\expo.modules.keepawake-15.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\38d14400a2d5e4ddf13e945710f1944c\transformed\expo.modules.keepawake-15.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.localauthentication:17.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c06e4086f4ddd2e805d86619e50f401d\transformed\expo.modules.localauthentication-17.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.screenorientation:9.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b09029df9b9fdf2fef002b8635560cef\transformed\expo.modules.screenorientation-9.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.screenorientation:9.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b09029df9b9fdf2fef002b8635560cef\transformed\expo.modules.screenorientation-9.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19e2d333207faf7bb3b972a2b59942f0\transformed\expo.modules.securestore-15.0.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:15.0.7] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19e2d333207faf7bb3b972a2b59942f0\transformed\expo.modules.securestore-15.0.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f4487991d53ea2ffbe2e4604920703d6\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f4487991d53ea2ffbe2e4604920703d6\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.apollographql.apollo:apollo-runtime-android:4.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\422e91ae3fee7377d30b3b992f112273\transformed\apollo-runtime-release\AndroidManifest.xml:6:5-44
MERGED from [com.apollographql.apollo:apollo-runtime-android:4.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\422e91ae3fee7377d30b3b992f112273\transformed\apollo-runtime-release\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\93b576e42733cef862a954c928112e8c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\93b576e42733cef862a954c928112e8c\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8016bd49cd6c51dfc99846b2ac930aa7\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.2.0-alpha04] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bf76bafeaa4e4c4b3789cb70960f60cb\transformed\biometric-1.2.0-alpha04\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e26a0718bb5a38e5d67edb6b5b8796bd\transformed\barcode-scanning-17.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7b66ccce72f4e6fb908076962b89f8c\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c7b66ccce72f4e6fb908076962b89f8c\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0a2a6591dced4295b60b85002f8c8409\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0a2a6591dced4295b60b85002f8c8409\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7791bdf654876e6ba13c0757ee9ffed2\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7791bdf654876e6ba13c0757ee9ffed2\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6873cdaa6bfa0f911242290759fcfae\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6873cdaa6bfa0f911242290759fcfae\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a029fe8b8605891436e34332cff561\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a6a029fe8b8605891436e34332cff561\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\948281eefeb2ac423daef145cd8a3d63\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\948281eefeb2ac423daef145cd8a3d63\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\deb9b1b93c9197dbf065a8c2a0c4b6f8\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\deb9b1b93c9197dbf065a8c2a0c4b6f8\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\630e002fb78825169bcbedfd3becd945\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\630e002fb78825169bcbedfd3becd945\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2617a59ab69d3cbb4d67d12e7172f9c8\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2617a59ab69d3cbb4d67d12e7172f9c8\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c45874b1b2dd68247585d37c44b265c5\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c45874b1b2dd68247585d37c44b265c5\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a62a50e9904e8d9d87c500591ee64cec\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a62a50e9904e8d9d87c500591ee64cec\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11b30660fec58d2511abd54b1f5bfa02\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11b30660fec58d2511abd54b1f5bfa02\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1be95b060ff0de27c9abd6913a2ec533\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1be95b060ff0de27c9abd6913a2ec533\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2a8fb07010759952bf90ce0e80cd124\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c2a8fb07010759952bf90ce0e80cd124\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ba46862941f89ac7149e9bbf2afa70ea\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ba46862941f89ac7149e9bbf2afa70ea\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32d3663f7533af91348424e01ec0d719\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\32d3663f7533af91348424e01ec0d719\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18e88ed8c92f5699bc86941876f9b82\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a18e88ed8c92f5699bc86941876f9b82\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3be4fb4599aa69685df676e23c1d435e\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3be4fb4599aa69685df676e23c1d435e\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7f734b4776f656b30f9f564abafee82a\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7f734b4776f656b30f9f564abafee82a\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e502c92cd4162352bba0eb4051bb287a\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e502c92cd4162352bba0eb4051bb287a\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cec4ec87a0c48a94d459216f8dda060d\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cec4ec87a0c48a94d459216f8dda060d\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e73162b71c728692a0e73647a17ce97d\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e73162b71c728692a0e73647a17ce97d\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bd79fe3d971c00a9c69a6fc30124a89a\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bd79fe3d971c00a9c69a6fc30124a89a\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\497b53a2103d9dab55d7751334c2a0b7\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\497b53a2103d9dab55d7751334c2a0b7\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcb549dbc855619e30027cf9153600f3\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bcb549dbc855619e30027cf9153600f3\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c011fb53f7a2aa28824c31e90f656387\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c011fb53f7a2aa28824c31e90f656387\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7749b0e07ced936880eb5ff4a52d87c6\transformed\material-ripple-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-ripple:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7749b0e07ced936880eb5ff4a52d87c6\transformed\material-ripple-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.composables:core-android:1.37.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df8bcecc49fccce54e9e67dd4295a710\transformed\core-release\AndroidManifest.xml:5:5-44
MERGED from [com.composables:core-android:1.37.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df8bcecc49fccce54e9e67dd4295a710\transformed\core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac9f14a47da32b84e694d05981f0518f\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ac9f14a47da32b84e694d05981f0518f\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2359395d430481f036982ea0f5c8dae4\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2359395d430481f036982ea0f5c8dae4\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92cb98e81fed3d3b50619a35279019d0\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\92cb98e81fed3d3b50619a35279019d0\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bb83775a718fa1878d483da48b985d9\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4bb83775a718fa1878d483da48b985d9\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df45c221466a4106dc9374b4735bc533\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\df45c221466a4106dc9374b4735bc533\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5de9532f0a4fae909792cf997622c2\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5de9532f0a4fae909792cf997622c2\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11fd102c6c432d0a7e1f8a37852e02b8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\11fd102c6c432d0a7e1f8a37852e02b8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\499ccb90ba39bfb04188e42fb6dde75b\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\499ccb90ba39bfb04188e42fb6dde75b\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8575d88d0c1fcbcbf2d0788d3102a584\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8575d88d0c1fcbcbf2d0788d3102a584\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b513094464c80e93b7db2e5bc221f53e\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b513094464c80e93b7db2e5bc221f53e\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\feee985a05e915f7c2cff0b90ae91794\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ea15573963922ccc058638b436218bd\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2ea15573963922ccc058638b436218bd\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e601c84bac2c1f19c9b2699a2ce4ad64\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d2e7731d70bee663ad697380578a9704\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d999eef7e435d9e248b502db8876bf2\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30c9c3522d01250988a84182945182d7\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\30c9c3522d01250988a84182945182d7\transformed\vision-interfaces-16.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24f75fe43a1dfc2cd0e4ee1767fb7a34\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\24f75fe43a1dfc2cd0e4ee1767fb7a34\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d1bc41ea89e21615eddf9863861a32d\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5d1bc41ea89e21615eddf9863861a32d\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8195b6be121f81eccab7be9c7ac45f83\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8195b6be121f81eccab7be9c7ac45f83\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\861839787bd11f700d8ba4a2bc58d182\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\861839787bd11f700d8ba4a2bc58d182\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ec819bb4f8b327e6e4eae3361ec6e50\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9ec819bb4f8b327e6e4eae3361ec6e50\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10558616e97b62882262ebd437660138\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10558616e97b62882262ebd437660138\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7eac873f9e9e9d0ef17272d1cafd1664\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7eac873f9e9e9d0ef17272d1cafd1664\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb6f6e5d4b0be6ab8ca06969f4ad2e47\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\eb6f6e5d4b0be6ab8ca06969f4ad2e47\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfd1ac0f786923d01f25f12719618681\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfd1ac0f786923d01f25f12719618681\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7ae61c3b313c8db9701f97884214dd26\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7ae61c3b313c8db9701f97884214dd26\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c8b3ac0d0a1c4de61c4a8165bf1b104\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1c8b3ac0d0a1c4de61c4a8165bf1b104\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4f4761fcd58deeb85cf76c2e6dff1ca\transformed\documentfile-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e4f4761fcd58deeb85cf76c2e6dff1ca\transformed\documentfile-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7824f5704a8615d900e1bbc06f70d683\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7824f5704a8615d900e1bbc06f70d683\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a07e4dd9491bb3b255df6a59e2483943\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a07e4dd9491bb3b255df6a59e2483943\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3925f987960bcdf2b8a2477ce730ea1b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3925f987960bcdf2b8a2477ce730ea1b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d97cddcb237db80b430148e2000c049e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d97cddcb237db80b430148e2000c049e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35c10ae8011164342dcf8561fd64efea\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\35c10ae8011164342dcf8561fd64efea\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a02035e21744ea944cae4c099374c9d0\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a02035e21744ea944cae4c099374c9d0\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b0df1f54e47f937b3a436e7eaa6be195\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b0df1f54e47f937b3a436e7eaa6be195\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ef1d6332463d11dacfeb288b03c0bf38\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ef1d6332463d11dacfeb288b03c0bf38\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\17a21103ff574ee4212c28297f5d53b4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\17a21103ff574ee4212c28297f5d53b4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da1abaface34e688008971d372cf60e\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da1abaface34e688008971d372cf60e\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6dd6e18c95dcc846d8cd2cc4971042d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c6dd6e18c95dcc846d8cd2cc4971042d\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aab617897d5efee02f2a2b67997cb35e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\aab617897d5efee02f2a2b67997cb35e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5165c706d0552969a898bd541f832e5\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b5165c706d0552969a898bd541f832e5\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\348019051576c774dcdb2baa24a765ff\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\348019051576c774dcdb2baa24a765ff\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dad18988ecdcbd5cc410865e9eb67f40\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dad18988ecdcbd5cc410865e9eb67f40\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e17dac39dfada490e31bdb8abe4c3c1\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e17dac39dfada490e31bdb8abe4c3c1\transformed\lifecycle-runtime-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bba1d21875594204cebffa52ee9faa9a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bba1d21875594204cebffa52ee9faa9a\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87c62ead478518fad06a6c791292b9fc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\87c62ead478518fad06a6c791292b9fc\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3a49a4c8a73dfd19eb58dfd431d233d0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3a49a4c8a73dfd19eb58dfd431d233d0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c764c1d3666dc7fb74b57b77f5d8a34\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c764c1d3666dc7fb74b57b77f5d8a34\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9900611c7f0496db5ba0e24c27e34642\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9900611c7f0496db5ba0e24c27e34642\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f3d7d25699208affda5e3d4c44d54862\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f3d7d25699208affda5e3d4c44d54862\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5a94f85a85d6bc95199dd305b8be44c4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5a94f85a85d6bc95199dd305b8be44c4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a2fd120a9584085d2612537f4b414003\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a2fd120a9584085d2612537f4b414003\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f30fdc996636fc4d1e5f4da5f787459\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8f30fdc996636fc4d1e5f4da5f787459\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\341d65447503e294f53c11a98be04379\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\341d65447503e294f53c11a98be04379\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c46ca3c85a79013ee8f827a1bfd1c979\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c46ca3c85a79013ee8f827a1bfd1c979\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\326cd173aa233264a91dd5bbf2598610\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\326cd173aa233264a91dd5bbf2598610\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab4e70f01f1243dec7d1ff49d1e2575b\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ab4e70f01f1243dec7d1ff49d1e2575b\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c3976769562be0e4763bbc0f21712e6\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9c3976769562be0e4763bbc0f21712e6\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85a1f7086b7960f77f97b4af68a04f6d\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\85a1f7086b7960f77f97b4af68a04f6d\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfbd4a64d2946efaca20bbc06b171311\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\762f4dc9aebf37bfc66de375dc543aba\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\762f4dc9aebf37bfc66de375dc543aba\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b4f01381b71eb2d1673f09999325396c\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b4f01381b71eb2d1673f09999325396c\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3db2f0b3509c4b3d08a81d0a67364ce\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e3db2f0b3509c4b3d08a81d0a67364ce\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ada150d16c90f6fd21ac4f985429d84f\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ada150d16c90f6fd21ac4f985429d84f\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ab80b900a609c386bd54943bf883268\transformed\material-icons-core-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-icons-core:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1ab80b900a609c386bd54943bf883268\transformed\material-icons-core-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c4d48eeb7f4b99a4a4e7d71de689573a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c4d48eeb7f4b99a4a4e7d71de689573a\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182c514307c9cd7fb6130c217b650409\transformed\material-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\182c514307c9cd7fb6130c217b650409\transformed\material-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f868bcdea222abfc5b941e0bc81954be\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f868bcdea222abfc5b941e0bc81954be\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9762a49ed3bf5dde0fb03455ec9600db\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9762a49ed3bf5dde0fb03455ec9600db\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8b23e8b30b401c4dd9168814489a0810\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8b23e8b30b401c4dd9168814489a0810\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2b33c66a03d9da1c6efc549e5037b53\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b2b33c66a03d9da1c6efc549e5037b53\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2951e3f7b53ce4974f38201822e09993\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2951e3f7b53ce4974f38201822e09993\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.lukmccall:radix-ui-colors-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82cb00a830756bccb4f7e540c06f3138\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [io.github.lukmccall:radix-ui-colors-android:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\82cb00a830756bccb4f7e540c06f3138\transformed\library-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2fcc8f3df6680f0cc0691d14a8b859ae\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2fcc8f3df6680f0cc0691d14a8b859ae\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5172ada4a02124d6f80a75df3844c57a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.react:hermes-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5c6f2f9e4b267c66b1b7d248bb2806\transformed\hermes-android-0.81.4-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.81.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5b5c6f2f9e4b267c66b1b7d248bb2806\transformed\hermes-android-0.81.4-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b675be355ba59956683d034f19582563\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b675be355ba59956683d034f19582563\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\07a6b3cdcdd40ead0f7a3f729962584b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\07a6b3cdcdd40ead0f7a3f729962584b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3b97eec65a39280db013b98577adad14\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3b97eec65a39280db013b98577adad14\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f33a32db8d06dc21097da2718df72823\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f33a32db8d06dc21097da2718df72823\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\56e7383d55b65ca83dd3ec0f83f1270c\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\56e7383d55b65ca83dd3ec0f83f1270c\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfc839453757451d65f1d7d2b231aeb7\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfc839453757451d65f1d7d2b231aeb7\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f532a9ff664b5c763585b87d177a407a\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f532a9ff664b5c763585b87d177a407a\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d4e0b17f83e0c03ee07b999bc13b478\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1d4e0b17f83e0c03ee07b999bc13b478\transformed\exifinterface-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfa7797f2c61aec7af3c5e9231aaf369\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\dfa7797f2c61aec7af3c5e9231aaf369\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\78ed5ce977e413a298fbda2b68b37ae3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\78ed5ce977e413a298fbda2b68b37ae3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f11fdf5ee99868dcbb95bb7170064472\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\f11fdf5ee99868dcbb95bb7170064472\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cd54390103da05e9f0b933f516c6d0cd\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cd54390103da05e9f0b933f516c6d0cd\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19f4c919a8c81ff433987fd7c8d9709b\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\19f4c919a8c81ff433987fd7c8d9709b\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\162cec6cdfe5f3752260b837f7019764\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:42
	android:value
		ADDED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-39
	android:name
		ADDED from [:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-64
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5d41abfd4ce8699901a1ca5cfb70a5b\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:34:13-92
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\37541f101477062e92c2ac26578114cb\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:22-76
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\18cf84c98fd82f7dbabfb0f27f981a70\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.tecbiz.tecbizassociadospush.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.tecbiz.tecbizassociadospush.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:24:13-71
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
