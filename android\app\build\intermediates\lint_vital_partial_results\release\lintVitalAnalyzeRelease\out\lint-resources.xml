http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/notification_icon.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/notification_icon.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/notification_icon.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/notification_icon.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/notification_icon.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rn_edit_text_material.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-hdpi/splashscreen_logo.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-mdpi/splashscreen_logo.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xhdpi/splashscreen_logo.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxhdpi/splashscreen_logo.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-xxxhdpi/splashscreen_logo.png,${\:app*buildDir}/generated/res/resValues/release/values/gradleResValues.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,+color:colorPrimary,0,V200030075,2c0003009f,;"#023c69";splashscreen_background,0,V20001000e,**********,;"#ffffff";colorPrimaryDark,0,V2000400a2,30000400d0,;"#ffffff";notification_icon_color,0,V2000500d3,**********,;"#ffffff";iconBackground,0,V200020046,2e00020072,;"#FFFFFF";+drawable:notification_icon,1,F;notification_icon,2,F;notification_icon,3,F;notification_icon,4,F;notification_icon,5,F;ic_launcher_background,6,F;rn_edit_text_material,7,F;splashscreen_logo,8,F;splashscreen_logo,9,F;splashscreen_logo,10,F;splashscreen_logo,11,F;splashscreen_logo,12,F;+integer:react_native_dev_server_port,13,V40006009f,3f000600da,;"8081";+mipmap:ic_launcher_round,14,F;ic_launcher_round,15,F;ic_launcher_round,16,F;ic_launcher_round,17,F;ic_launcher_round,18,F;ic_launcher_round,19,F;ic_launcher_foreground,20,F;ic_launcher_foreground,21,F;ic_launcher_foreground,22,F;ic_launcher_foreground,23,F;ic_launcher_foreground,24,F;ic_launcher,25,F;ic_launcher,26,F;ic_launcher,27,F;ic_launcher,28,F;ic_launcher,29,F;ic_launcher,30,F;+string:app_name,31,V20001000e,330001003f,;"TecBiz Associado";google_app_id,32,V4000400f1,6c00040159,;"1\:608372388905\:android\:25e23e2d6867cb314ad838";gcm_defaultSenderId,32,V400020037,5100020084,;"608372388905";google_api_key,32,V400030089,67000300ec,;"AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk";project_id,32,V40007023e,4800070282,;"tecbizappass";expo_splash_screen_resize_mode,31,V200020042,5500020095,;"contain";expo_splash_screen_status_bar_translucent,31,V200030098,5e000300f4,;"false";google_crash_reporting_api_key,32,V40005015e,77000501d1,;"AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk";google_storage_bucket,32,V4000601d6,6700060239,;"tecbizappass.firebasestorage.app";react_native_dev_server_ip,13,V400080109,590008015e,;"*************";+style:Theme.App.SplashScreen,33,V2000701aa,a0009023e,;DAppTheme,android\:windowBackground:@drawable/ic_launcher_background,;AppTheme,33,V20001003d,a000601a7,;DTheme.AppCompat.DayNight.NoActionBar,android\:enforceNavigationBarContrast:true,android\:editTextBackground:@drawable/rn_edit_text_material,android\:statusBarColor:#ffffff,colorPrimary:@color/colorPrimary,;