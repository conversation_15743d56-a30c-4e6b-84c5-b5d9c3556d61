{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5303", "endColumns": "139", "endOffsets": "5438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,212,283,352,433,504,571,641,724,807,889,961,1035,1117,1194,1276,1358,1434,1512,1589,1673,1747,1829,1901", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "123,207,278,347,428,499,566,636,719,802,884,956,1030,1112,1189,1271,1353,1429,1507,1584,1668,1742,1824,1896,1978"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2845,4217,9401,9472,9541,9786,9857,9924,9994,10251,10334,10584,10796,11114,11196,11273,11432,11514,11670,11826,11903,12088,12310,12513,12585", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "2913,4296,9467,9536,9617,9852,9919,9989,10072,10329,10411,10651,10865,11191,11268,11350,11509,11585,11743,11898,11982,12157,12387,12580,12662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,10870", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,10949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,364,471,562,722,833,949,1070,1208,1338,1464,1589,1729,1828,2002,2127,2270,2404,2537,2677,2774,2920,3007,3143,3244,3394", "endColumns": "178,129,106,90,159,110,115,120,137,129,125,124,139,98,173,124,142,133,132,139,96,145,86,135,100,149,117", "endOffsets": "229,359,466,557,717,828,944,1065,1203,1333,1459,1584,1724,1823,1997,2122,2265,2399,2532,2672,2769,2915,3002,3138,3239,3389,3507"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3009,3188,6525,6834,7108,7268,7697,7813,7934,8072,8202,8328,8453,8593,8692,8866,8991,9134,9268,10656,12848,12945,13091,13178,13314,13415,13565", "endColumns": "178,129,106,90,159,110,115,120,137,129,125,124,139,98,173,124,142,133,132,139,96,145,86,135,100,149,117", "endOffsets": "3183,3313,6627,6920,7263,7374,7808,7929,8067,8197,8323,8448,8588,8687,8861,8986,9129,9263,9396,10791,12940,13086,13173,13309,13410,13560,13678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "145,235", "endColumns": "89,90", "endOffsets": "230,321"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "12667,12757", "endColumns": "89,90", "endOffsets": "12752,12843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3318,3415,3517,3615,3719,3822,3924,11987", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3410,3512,3610,3714,3817,3919,4036,12083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4301,4407,4572,4701,4808,4955,5085,5199,5443,5601,5708,5875,6005,6157,6309,6379,6441", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "4402,4567,4696,4803,4950,5080,5194,5298,5596,5703,5870,6000,6152,6304,6374,6436,6520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6632,7379,7483,7595", "endColumns": "105,103,111,101", "endOffsets": "6733,7478,7590,7692"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,291,372,468,566,651,728,815,907,989,1071,1157,1229,1317,1394,1474,1552,1630,1700", "endColumns": "90,94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "191,286,367,463,561,646,723,810,902,984,1066,1152,1224,1312,1389,1469,1547,1625,1695,1816"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2918,4041,4136,6738,6925,7023,9622,9699,10077,10169,10416,10498,10954,11026,11355,11590,11748,12162,12240,12392", "endColumns": "90,94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "3004,4131,4212,6829,7018,7103,9694,9781,10164,10246,10493,10579,11021,11109,11427,11665,11821,12235,12305,12508"}}]}]}