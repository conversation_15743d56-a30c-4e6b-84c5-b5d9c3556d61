<dependencies>
  <compile
      roots="com.facebook.react:react-android:0.81.4:release@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,:@@:expo::release,:@@:expo-modules-core::release,:@@:expo-dev-launcher::release,:@@:expo-dev-menu::release,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,host.exp.exponent:expo.modules.device:8.0.7@aar,host.exp.exponent:expo.modules.filesystem:19.0.12@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,host.exp.exponent:expo.modules.securestore:15.0.7@aar,androidx.biometric:biometric:1.2.0-alpha04@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity:1.9.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.browser:browser:1.6.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.core:core-ktx:1.15.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.media:media:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.15.0@aar,androidx.core:core:1.15.0@aar,androidx.lifecycle:lifecycle-runtime:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar,com.facebook.fresco:fbcore:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,io.insert-koin:koin-core-jvm:3.5.6@jar,co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar,co.touchlab:stately-concurrency-jvm:2.0.6@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,com.squareup.okio:okio-jvm:3.9.0@jar,:@@:expo-constants::release,:@@:expo-dev-client::release,androidx.databinding:viewbinding:8.11.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.collection:collection-jvm:1.5.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,io.github.lukmccall:radix-ui-colors-android:1.0.1@aar,androidx.annotation:annotation-experimental:1.4.1@aar,com.facebook.fresco:ui-core:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar,com.facebook.fresco:animated-gif:3.6.0@aar,com.facebook.fresco:webpsupport:3.6.0@aar,com.facebook.react:hermes-android:0.81.4:release@aar,org.jetbrains:annotations:23.0.0@jar,commons-io:commons-io:2.6@jar,com.google.guava:listenablefuture:1.0@jar,androidx.documentfile:documentfile:1.1.0@aar,androidx.documentfile:documentfile:1.1.0@aar,org.jspecify:jspecify:1.0.0@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.soloader:nativeloader:0.12.1@jar,com.facebook.soloader:annotation:0.12.1@jar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,javax.inject:javax.inject:1@jar,host.exp.exponent:expo.modules.application:7.0.7@aar,expo.modules.asset:expo.modules.asset:12.0.8@aar,com.facebook.device.yearclass:yearclass:2.1.0@jar,commons-codec:commons-codec:1.10@jar,host.exp.exponent:expo.modules.font:14.0.8@aar,host.exp.exponent:expo.modules.keepawake:15.0.7@aar,host.exp.exponent:expo.modules.localauthentication:17.0.7@aar,host.exp.exponent:expo.modules.notifications:0.32.11@aar,host.exp.exponent:expo.modules.screenorientation:9.0.7@aar,:@@:expo-dev-menu-interface::release,:@@:expo-json-utils::release,:@@:expo-manifests::release,:@@:expo-updates-interface::release">
    <dependency
        name="com.facebook.react:react-android:0.81.4:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name=":@@:expo-dev-launcher::release"
        simpleName="host.exp.exponent:expo-dev-launcher"/>
    <dependency
        name=":@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="host.exp.exponent:expo.modules.device:8.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.device"/>
    <dependency
        name="host.exp.exponent:expo.modules.filesystem:19.0.12@aar"
        simpleName="host.exp.exponent:expo.modules.filesystem"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="host.exp.exponent:expo.modules.securestore:15.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.securestore"/>
    <dependency
        name="androidx.biometric:biometric:1.2.0-alpha04@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.core:core-ktx:1.15.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.15.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.5.6@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.9.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-dev-client::release"
        simpleName="host.exp.exponent:expo-dev-client"/>
    <dependency
        name="androidx.databinding:viewbinding:8.11.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="io.github.lukmccall:radix-ui-colors-android:1.0.1@aar"
        simpleName="io.github.lukmccall:radix-ui-colors-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.6.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.react:hermes-android:0.81.4:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.documentfile:documentfile:1.1.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="host.exp.exponent:expo.modules.application:7.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.application"/>
    <dependency
        name="expo.modules.asset:expo.modules.asset:12.0.8@aar"
        simpleName="expo.modules.asset:expo.modules.asset"/>
    <dependency
        name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
        simpleName="com.facebook.device.yearclass:yearclass"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="host.exp.exponent:expo.modules.font:14.0.8@aar"
        simpleName="host.exp.exponent:expo.modules.font"/>
    <dependency
        name="host.exp.exponent:expo.modules.keepawake:15.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.keepawake"/>
    <dependency
        name="host.exp.exponent:expo.modules.localauthentication:17.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.localauthentication"/>
    <dependency
        name="host.exp.exponent:expo.modules.notifications:0.32.11@aar"
        simpleName="host.exp.exponent:expo.modules.notifications"/>
    <dependency
        name="host.exp.exponent:expo.modules.screenorientation:9.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.screenorientation"/>
    <dependency
        name=":@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name=":@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name=":@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name=":@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
  </compile>
  <package
      roots=":@@:expo::release,:@@:expo-dev-launcher::release,:@@:expo-dev-menu::release,:@@:expo-dev-menu-interface::release,:@@:expo-modules-core::release,host.exp.exponent:expo.modules.font:14.0.8@aar,com.facebook.react:react-android:0.81.4:release@aar,:@@:expo-constants::release,:@@:expo-dev-client::release,:@@:expo-manifests::release,:@@:expo-json-utils::release,:@@:expo-updates-interface::release,host.exp.exponent:expo.modules.application:7.0.7@aar,expo.modules.asset:expo.modules.asset:12.0.8@aar,host.exp.exponent:expo.modules.device:8.0.7@aar,host.exp.exponent:expo.modules.filesystem:19.0.12@aar,host.exp.exponent:expo.modules.keepawake:15.0.7@aar,host.exp.exponent:expo.modules.localauthentication:17.0.7@aar,host.exp.exponent:expo.modules.notifications:0.32.11@aar,host.exp.exponent:expo.modules.screenorientation:9.0.7@aar,host.exp.exponent:expo.modules.securestore:15.0.7@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.google.firebase:firebase-messaging:24.0.1@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.apollographql.apollo:apollo-runtime-android:4.3.1@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,io.insert-koin:koin-core-jvm:3.5.6@jar,co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar,co.touchlab:stately-concurrency-jvm:2.0.6@jar,co.touchlab:stately-strict-jvm:2.0.6@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20@jar,androidx.appcompat:appcompat-resources:1.7.0@aar,com.google.android.material:material:1.2.1@aar,androidx.biometric:biometric:1.2.0-alpha04@aar,com.google.android.gms:play-services-code-scanner:16.1.0@aar,com.google.mlkit:barcode-scanning:17.3.0@aar,com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar,com.google.mlkit:barcode-scanning-common:17.0.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.11.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:webpsupport:3.6.0@aar,com.facebook.fresco:animated-gif:3.6.0@aar,com.facebook.fresco:animated-base:3.6.0@aar,com.facebook.fresco:animated-drawable:3.6.0@aar,com.facebook.fresco:vito-options:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,com.facebook.fresco:urimod:3.6.0@aar,com.facebook.fresco:vito-source:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,com.facebook.fresco:soloader:3.6.0@aar,com.facebook.fresco:fbcore:3.6.0@aar,org.jetbrains.kotlin:kotlin-reflect:2.1.20@jar,androidx.navigation:navigation-runtime-android:2.9.0@aar,androidx.navigation:navigation-common-android:2.9.0@aar,androidx.navigation:navigation-compose-android:2.9.0@aar,androidx.compose.material:material-ripple:1.0.0@aar,com.composables:core-android:1.37.0@aar,androidx.compose.foundation:foundation-layout-android:1.9.0@aar,androidx.compose.animation:animation-core-android:1.9.0@aar,androidx.compose.animation:animation-android:1.9.0@aar,androidx.compose.ui:ui-tooling-preview-android:1.9.0@aar,androidx.compose.ui:ui-tooling-data-android:1.9.0@aar,androidx.compose.ui:ui-unit-android:1.9.0@aar,androidx.compose.ui:ui-graphics-android:1.9.0@aar,androidx.compose.ui:ui-geometry-android:1.9.0@aar,androidx.compose.ui:ui-util-android:1.9.0@aar,androidx.compose.ui:ui-text-android:1.9.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.mlkit:vision-interfaces:16.3.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,androidx.compose.runtime:runtime-annotation-android:1.9.0@aar,androidx.compose.runtime:runtime-saveable-android:1.9.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.emoji2:emoji2-views-helper:1.4.0@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.autofill:autofill:1.1.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.browser:browser:1.6.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.documentfile:documentfile:1.1.0@aar,androidx.documentfile:documentfile:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.transition:transition:1.2.0@aar,androidx.media:media:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.15.0@aar,androidx.graphics:graphics-path:1.0.1@aar,androidx.core:core:1.15.0@aar,androidx.core:core:1.15.0@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar,androidx.lifecycle:lifecycle-runtime:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-process:2.9.0@aar,androidx.lifecycle:lifecycle-livedata:2.9.0@aar,androidx.lifecycle:lifecycle-common-java8:2.9.0@jar,androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar,androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar,androidx.savedstate:savedstate-ktx:1.3.0@aar,androidx.savedstate:savedstate-android:1.3.0@aar,androidx.savedstate:savedstate-compose-android:1.3.0@aar,androidx.compose.runtime:runtime-android:1.9.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity:1.9.0@aar,androidx.activity:activity-ktx:1.9.0@aar,androidx.activity:activity-compose:1.9.0@aar,androidx.compose.material:material-icons-core:1.0.0@aar,androidx.compose.ui:ui-android:1.9.0@aar,androidx.compose.ui:ui-tooling-android:1.9.0@aar,androidx.compose.material:material:1.0.0@aar,androidx.compose.foundation:foundation-android:1.9.0@aar,androidx.profileinstaller:profileinstaller:1.4.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.1.20@jar,com.facebook.fresco:ui-core:3.6.0@aar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.7.1@jar,io.github.lukmccall:radix-ui-colors-android:1.0.1@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.1.20@jar,com.facebook.fresco:vito-renderer:3.6.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.5.0@jar,androidx.collection:collection-jvm:1.5.0@jar,org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar,com.apollographql.apollo:apollo-api-jvm:4.3.1@jar,com.squareup.okio:okio-jvm:3.9.0@jar,com.benasher44:uuid-jvm:0.8.2@jar,org.jetbrains.kotlinx:atomicfu-jvm:0.26.0@jar,com.facebook.react:hermes-android:0.81.4:release@aar,androidx.databinding:viewbinding:8.11.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.exifinterface:exifinterface:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.9.1@jar,androidx.core:core-viewtree:1.0.0@aar,com.apollographql.apollo:apollo-mpp-utils-jvm:4.3.1@jar,com.apollographql.apollo:apollo-annotations-jvm:4.3.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar,org.jetbrains:annotations:23.0.0@jar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.facebook.soloader:nativeloader:0.12.1@jar,commons-io:commons-io:2.6@jar,com.google.code.gson:gson:2.8.6@jar,com.android.installreferrer:installreferrer:2.2@aar,com.facebook.device.yearclass:yearclass:2.1.0@jar,commons-codec:commons-codec:1.10@jar,me.leolin:ShortcutBadger:1.1.22@aar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.12.1@jar,com.google.guava:listenablefuture:1.0@jar,org.jspecify:jspecify:1.0.0@jar,com.google.errorprone:error_prone_annotations:2.26.0@jar,com.google.android.odml:image:1.0.0-beta1@aar">
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:expo-dev-launcher::release"
        simpleName="host.exp.exponent:expo-dev-launcher"/>
    <dependency
        name=":@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name=":@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name="host.exp.exponent:expo.modules.font:14.0.8@aar"
        simpleName="host.exp.exponent:expo.modules.font"/>
    <dependency
        name="com.facebook.react:react-android:0.81.4:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-dev-client::release"
        simpleName="host.exp.exponent:expo-dev-client"/>
    <dependency
        name=":@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name=":@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name=":@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
    <dependency
        name="host.exp.exponent:expo.modules.application:7.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.application"/>
    <dependency
        name="expo.modules.asset:expo.modules.asset:12.0.8@aar"
        simpleName="expo.modules.asset:expo.modules.asset"/>
    <dependency
        name="host.exp.exponent:expo.modules.device:8.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.device"/>
    <dependency
        name="host.exp.exponent:expo.modules.filesystem:19.0.12@aar"
        simpleName="host.exp.exponent:expo.modules.filesystem"/>
    <dependency
        name="host.exp.exponent:expo.modules.keepawake:15.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.keepawake"/>
    <dependency
        name="host.exp.exponent:expo.modules.localauthentication:17.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.localauthentication"/>
    <dependency
        name="host.exp.exponent:expo.modules.notifications:0.32.11@aar"
        simpleName="host.exp.exponent:expo.modules.notifications"/>
    <dependency
        name="host.exp.exponent:expo.modules.screenorientation:9.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.screenorientation"/>
    <dependency
        name="host.exp.exponent:expo.modules.securestore:15.0.7@aar"
        simpleName="host.exp.exponent:expo.modules.securestore"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.0.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.apollographql.apollo:apollo-runtime-android:4.3.1@aar"
        simpleName="com.apollographql.apollo:apollo-runtime-android"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.5.6@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="co.touchlab:stately-strict-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-strict-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="com.google.android.material:material:1.2.1@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.biometric:biometric:1.2.0-alpha04@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="com.google.android.gms:play-services-code-scanner:16.1.0@aar"
        simpleName="com.google.android.gms:play-services-code-scanner"/>
    <dependency
        name="com.google.mlkit:barcode-scanning:17.3.0@aar"
        simpleName="com.google.mlkit:barcode-scanning"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
        simpleName="com.google.android.gms:play-services-mlkit-barcode-scanning"/>
    <dependency
        name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
        simpleName="com.google.mlkit:barcode-scanning-common"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.11.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.6.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:animated-base:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-base"/>
    <dependency
        name="com.facebook.fresco:animated-drawable:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-drawable"/>
    <dependency
        name="com.facebook.fresco:vito-options:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-options"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:urimod:3.6.0@aar"
        simpleName="com.facebook.fresco:urimod"/>
    <dependency
        name="com.facebook.fresco:vito-source:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-source"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.6.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-runtime-android"/>
    <dependency
        name="androidx.navigation:navigation-common-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-common-android"/>
    <dependency
        name="androidx.navigation:navigation-compose-android:2.9.0@aar"
        simpleName="androidx.navigation:navigation-compose-android"/>
    <dependency
        name="androidx.compose.material:material-ripple:1.0.0@aar"
        simpleName="androidx.compose.material:material-ripple"/>
    <dependency
        name="com.composables:core-android:1.37.0@aar"
        simpleName="com.composables:core-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.9.0@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.9.0@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.9.0@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.3.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="androidx.compose.runtime:runtime-annotation-android:1.9.0@aar"
        simpleName="androidx.compose.runtime:runtime-annotation-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.9.0@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.documentfile:documentfile:1.1.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.media:media:1.0.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.15.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.graphics:graphics-path:1.0.1@aar"
        simpleName="androidx.graphics:graphics-path"/>
    <dependency
        name="androidx.core:core:1.15.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate-android"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-android"/>
    <dependency
        name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
        simpleName="androidx.savedstate:savedstate-compose-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.9.0@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.9.0@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.9.0@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.material:material-icons-core:1.0.0@aar"
        simpleName="androidx.compose.material:material-icons-core"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.9.0@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.material:material:1.0.0@aar"
        simpleName="androidx.compose.material:material"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.9.0@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-datetime-jvm"/>
    <dependency
        name="io.github.lukmccall:radix-ui-colors-android:1.0.1@aar"
        simpleName="io.github.lukmccall:radix-ui-colors-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.1.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="com.facebook.fresco:vito-renderer:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-renderer"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.5.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.5.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm"/>
    <dependency
        name="com.apollographql.apollo:apollo-api-jvm:4.3.1@jar"
        simpleName="com.apollographql.apollo:apollo-api-jvm"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.9.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="com.benasher44:uuid-jvm:0.8.2@jar"
        simpleName="com.benasher44:uuid-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:atomicfu-jvm:0.26.0@jar"
        simpleName="org.jetbrains.kotlinx:atomicfu-jvm"/>
    <dependency
        name="com.facebook.react:hermes-android:0.81.4:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="androidx.databinding:viewbinding:8.11.0@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.0.0@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.9.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="com.apollographql.apollo:apollo-mpp-utils-jvm:4.3.1@jar"
        simpleName="com.apollographql.apollo:apollo-mpp-utils-jvm"/>
    <dependency
        name="com.apollographql.apollo:apollo-annotations-jvm:4.3.1@jar"
        simpleName="com.apollographql.apollo:apollo-annotations-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.code.gson:gson:2.8.6@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.android.installreferrer:installreferrer:2.2@aar"
        simpleName="com.android.installreferrer:installreferrer"/>
    <dependency
        name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
        simpleName="com.facebook.device.yearclass:yearclass"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="me.leolin:ShortcutBadger:1.1.22@aar"
        simpleName="me.leolin:ShortcutBadger"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.jspecify:jspecify:1.0.0@jar"
        simpleName="org.jspecify:jspecify"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
  </package>
</dependencies>
