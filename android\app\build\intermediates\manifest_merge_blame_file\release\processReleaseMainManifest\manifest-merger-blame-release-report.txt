1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tecbiz.tecbizassociadospush"
4    android:versionCode="3570017"
5    android:versionName="5.0.9" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:2:3-64
11-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.NOTIFICATIONS" />
12-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:3:3-69
12-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:3:20-67
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:4:3-77
13-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:5:3-78
14-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:5:20-76
15    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
15-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:6:3-75
15-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:6:20-73
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
16-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:7:3-69
16-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:7:20-67
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
17-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:8:3-71
17-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:8:20-69
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:9:3-63
18-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:9:20-61
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:10:3-65
19-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:10:20-63
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:11:3-78
20-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:11:20-76
21    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
21-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:12:3-79
21-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:12:20-77
22    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
22-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:13:3-76
22-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:13:20-74
23
24    <queries>
24-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:14:3-20:13
25        <intent>
25-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:15:5-19:14
26            <action android:name="android.intent.action.VIEW" />
26-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:16:7-58
26-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:16:15-56
27
28            <category android:name="android.intent.category.BROWSABLE" />
28-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:17:7-67
28-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:17:17-65
29
30            <data android:scheme="https" />
30-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:7-37
30-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:13-35
31        </intent>
32    </queries>
33
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:8:5-77
34-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:8:22-74
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
35-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:22-76
36
37    <permission
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.tecbiz.tecbizassociadospush.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.tecbiz.tecbizassociadospush.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
42    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
42-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
42-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4278e3bbba81055c320f55c80c6b3bb7\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
43    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
44    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
45    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
46    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
47    <!-- for Samsung -->
48    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
48-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
49    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
49-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
50    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
50-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
50-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
51    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
51-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
51-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
52    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
52-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
52-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
53    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
53-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
53-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
54    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
54-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
54-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
55    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
55-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
55-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
56    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
56-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
56-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
57    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
57-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
57-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
58    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
58-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
58-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
59    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
59-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
59-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
60    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
60-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
60-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
61    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
61-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
61-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
62    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
63    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c05fbcf2880144c8bdbed39e1323b7de\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
64
65    <application
65-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:3-43:17
66        android:name="com.tecbiz.tecbizassociadospush.MainApplication"
66-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:16-47
67        android:allowBackup="true"
67-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:162-188
68        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
68-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7a80f6d275b4d3310d028e93bbd2adb7\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
69        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
69-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:412-481
70        android:enableOnBackInvokedCallback="false"
70-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:248-291
71        android:extractNativeLibs="false"
72        android:fullBackupContent="@xml/secure_store_backup_rules"
72-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:353-411
73        android:hardwareAccelerated="true"
73-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:318-352
74        android:icon="@mipmap/ic_launcher"
74-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:81-115
75        android:label="@string/app_name"
75-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:48-80
76        android:largeHeap="false"
76-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:292-317
77        android:roundIcon="@mipmap/ic_launcher_round"
77-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:116-161
78        android:supportsRtl="true"
78-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:221-247
79        android:theme="@style/AppTheme" >
79-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:21:189-220
80        <meta-data
80-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:22:5-118
81            android:name="com.google.firebase.messaging.default_notification_channel_id"
81-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:22:16-92
82            android:value="default" />
82-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:22:93-116
83        <meta-data
83-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:23:5-139
84            android:name="com.google.firebase.messaging.default_notification_color"
84-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:23:16-87
85            android:resource="@color/notification_icon_color" />
85-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:23:88-137
86        <meta-data
86-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:24:5-135
87            android:name="com.google.firebase.messaging.default_notification_icon"
87-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:24:16-86
88            android:resource="@drawable/notification_icon" />
88-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:24:87-133
89        <meta-data
89-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:25:5-136
90            android:name="expo.modules.notifications.default_notification_color"
90-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:25:16-84
91            android:resource="@color/notification_icon_color" />
91-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:25:85-134
92        <meta-data
92-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:26:5-132
93            android:name="expo.modules.notifications.default_notification_icon"
93-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:26:16-83
94            android:resource="@drawable/notification_icon" />
94-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:26:84-130
95        <meta-data
95-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:27:5-83
96            android:name="expo.modules.updates.ENABLED"
96-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:27:16-59
97            android:value="false" />
97-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:27:60-81
98        <meta-data
98-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:28:5-105
99            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
99-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:28:16-80
100            android:value="ALWAYS" />
100-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:28:81-103
101        <meta-data
101-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:29:5-99
102            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
102-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:29:16-79
103            android:value="0" />
103-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:29:80-97
104
105        <activity
105-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:5-42:16
106            android:name="com.tecbiz.tecbizassociadospush.MainActivity"
106-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:15-43
107            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|density"
107-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:44-142
108            android:exported="true"
108-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:221-244
109            android:launchMode="singleTask"
109-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:143-174
110            android:maxAspectRatio="2.4"
110-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:357-385
111            android:resizeableActivity="true"
111-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:282-315
112            android:screenOrientation="portrait"
112-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:245-281
113            android:supportsPictureInPicture="false"
113-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:316-356
114            android:theme="@style/Theme.App.SplashScreen"
114-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:175-220
115            android:windowSoftInputMode="adjustResize" >
115-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:30:386-428
116            <intent-filter>
116-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:31:7-34:23
117                <action android:name="android.intent.action.MAIN" />
117-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:32:9-60
117-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:32:17-58
118
119                <category android:name="android.intent.category.LAUNCHER" />
119-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:33:9-68
119-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:33:19-66
120            </intent-filter>
121            <intent-filter>
121-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:35:7-41:23
122                <action android:name="android.intent.action.VIEW" />
122-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:16:7-58
122-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:16:15-56
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:37:9-67
124-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:37:19-65
125                <category android:name="android.intent.category.BROWSABLE" />
125-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:17:7-67
125-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:17:17-65
126
127                <data android:scheme="tecbizapp" />
127-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:7-37
127-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:13-35
128                <data android:scheme="exp+tecbizexpoapp" />
128-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:7-37
128-->D:\Projetos\TecBizExpoApp\android\app\src\main\AndroidManifest.xml:18:13-35
129            </intent-filter>
130        </activity>
131
132        <meta-data
132-->[:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:42
133            android:name="com.google.mlkit.vision.DEPENDENCIES"
133-->[:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-64
134            android:value="barcode_ui" />
134-->[:expo-dev-launcher] D:\Projetos\TecBizExpoApp\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-39
135        <meta-data
135-->[:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
136            android:name="org.unimodules.core.AppLoader#react-native-headless"
136-->[:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
137            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
137-->[:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
138        <meta-data
138-->[:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
139            android:name="com.facebook.soloader.enabled"
139-->[:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
140            android:value="true" />
140-->[:expo-modules-core] D:\Projetos\TecBizExpoApp\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
141
142        <service
142-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:11:9-17:19
143            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
143-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:12:13-91
144            android:exported="false" >
144-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:13:13-37
145            <intent-filter android:priority="-1" >
145-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:14:13-16:29
145-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:14:28-49
146                <action android:name="com.google.firebase.MESSAGING_EVENT" />
146-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:15:17-78
146-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:15:25-75
147            </intent-filter>
148        </service>
149
150        <receiver
150-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:19:9-31:20
151            android:name="expo.modules.notifications.service.NotificationsService"
151-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:20:13-83
152            android:enabled="true"
152-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:21:13-35
153            android:exported="false" >
153-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:22:13-37
154            <intent-filter android:priority="-1" >
154-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:23:13-30:29
154-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:23:28-49
155                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
155-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:24:17-88
155-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:24:25-85
156                <action android:name="android.intent.action.BOOT_COMPLETED" />
156-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:25:17-79
156-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:25:25-76
157                <action android:name="android.intent.action.REBOOT" />
157-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:26:17-71
157-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:26:25-68
158                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
158-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:27:17-82
158-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:27:25-79
159                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
159-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:28:17-82
159-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:28:25-79
160                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
160-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:29:17-84
160-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:29:25-81
161            </intent-filter>
162        </receiver>
163
164        <activity
164-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:33:9-40:75
165            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
165-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:34:13-92
166            android:excludeFromRecents="true"
166-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:35:13-46
167            android:exported="false"
167-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:36:13-37
168            android:launchMode="standard"
168-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:37:13-42
169            android:noHistory="true"
169-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:38:13-37
170            android:taskAffinity=""
170-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:39:13-36
171            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
171-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:40:13-72
172
173        <receiver
173-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
174            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
174-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
175            android:exported="true"
175-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
176            android:permission="com.google.android.c2dm.permission.SEND" >
176-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
177            <intent-filter>
177-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
178                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
178-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
178-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
179            </intent-filter>
180
181            <meta-data
181-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
182                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
182-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
183                android:value="true" />
183-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
184        </receiver>
185        <!--
186             FirebaseMessagingService performs security checks at runtime,
187             but set to not exported to explicitly avoid allowing another app to call it.
188        -->
189        <service
189-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
190            android:name="com.google.firebase.messaging.FirebaseMessagingService"
190-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
191            android:directBootAware="true"
191-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
192            android:exported="false" >
192-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
193            <intent-filter android:priority="-500" >
193-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:14:13-16:29
193-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:14:28-49
194                <action android:name="com.google.firebase.MESSAGING_EVENT" />
194-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:15:17-78
194-->[host.exp.exponent:expo.modules.notifications:0.32.11] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8e0aac242641ffe02139e17b1b8d81fe\transformed\expo.modules.notifications-0.32.11\AndroidManifest.xml:15:25-75
195            </intent-filter>
196        </service>
197        <service
197-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
198            android:name="com.google.firebase.components.ComponentDiscoveryService"
198-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
199            android:directBootAware="true"
199-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
200            android:exported="false" >
200-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
201            <meta-data
201-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
202                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
202-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
203                android:value="com.google.firebase.components.ComponentRegistrar" />
203-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
204            <meta-data
204-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
205                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
205-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
206                android:value="com.google.firebase.components.ComponentRegistrar" />
206-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cf671caf8aa903c4b3c265cc86a280a8\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
207            <meta-data
207-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
208                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
208-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
209                android:value="com.google.firebase.components.ComponentRegistrar" />
209-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
210            <meta-data
210-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
211                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
211-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
212                android:value="com.google.firebase.components.ComponentRegistrar" />
212-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c6a50320e0ebb89d4158171b9e52f3a\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
213            <meta-data
213-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
214                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
214-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
215                android:value="com.google.firebase.components.ComponentRegistrar" />
215-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d145228cfa100a51c7dcdb221fde4e3f\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
216            <meta-data
216-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
217                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
219            <meta-data
219-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
220                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
220-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a41f057b1266edd961cfefc98e23fd88\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
222        </service>
223        <!--
224        This activity is an invisible delegate activity to start scanner activity
225        and receive result, so it's unnecessary to support screen orientation and
226        we can avoid any side effect from activity recreation in any case.
227        -->
228        <activity
228-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
229            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
229-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
230            android:exported="false"
230-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
231            android:screenOrientation="portrait" >
231-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abb77e75d6bda0ae0dce529fdc975a0f\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
232        </activity>
233
234        <service
234-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
235            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
235-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
236            android:directBootAware="true"
236-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:17:13-43
237            android:exported="false" >
237-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
238            <meta-data
238-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
239                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
239-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\06f47b2eae57325f12390fce4e91c4b7\transformed\play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
241            <meta-data
241-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
242                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
242-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0f60c9b71e5bcbda8a1d01e7d785385d\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
244            <meta-data
244-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:20:13-22:85
245                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
245-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:21:17-120
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:22:17-82
247        </service>
248
249        <provider
249-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:9:9-13:38
250            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
250-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:10:13-78
251            android:authorities="com.tecbiz.tecbizassociadospush.mlkitinitprovider"
251-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:11:13-69
252            android:exported="false"
252-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:12:13-37
253            android:initOrder="99" />
253-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1307de8de09a24fd448f42377b415be8\transformed\common-18.11.0\AndroidManifest.xml:13:13-35
254        <provider
254-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
255            android:name="com.google.firebase.provider.FirebaseInitProvider"
255-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
256            android:authorities="com.tecbiz.tecbizassociadospush.firebaseinitprovider"
256-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
257            android:directBootAware="true"
257-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
258            android:exported="false"
258-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
259            android:initOrder="100" />
259-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4438cd19837c002dfa594c858c486f26\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
260
261        <activity
261-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
262            android:name="com.google.android.gms.common.api.GoogleApiActivity"
262-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
263            android:exported="false"
263-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
264            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
264-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\bc31118459fe5c176424d734f980c964\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
265
266        <provider
266-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
267            android:name="androidx.startup.InitializationProvider"
267-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
268            android:authorities="com.tecbiz.tecbizassociadospush.androidx-startup"
268-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
269            android:exported="false" >
269-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
270            <meta-data
270-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
271                android:name="androidx.emoji2.text.EmojiCompatInitializer"
271-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
272                android:value="androidx.startup" />
272-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ee2683fbe22de09172f629e64d7bf874\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
273            <meta-data
273-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
274                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
274-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
275                android:value="androidx.startup" />
275-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fa126b0422d6d3141a82b4a4eb9b9f9b\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
276            <meta-data
276-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
277                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
277-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
278                android:value="androidx.startup" />
278-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
279        </provider>
280
281        <meta-data
281-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
282            android:name="com.google.android.gms.version"
282-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
283            android:value="@integer/google_play_services_version" />
283-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\140c13111513dc6f8651ec35200cff0b\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
284
285        <activity
285-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
286            android:name="androidx.compose.ui.tooling.PreviewActivity"
286-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:24:13-71
287            android:exported="true" />
287-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\10b4c45651a87afe24779e1993867213\transformed\ui-tooling\AndroidManifest.xml:25:13-36
288
289        <receiver
289-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
290            android:name="androidx.profileinstaller.ProfileInstallReceiver"
290-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
291            android:directBootAware="false"
291-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
292            android:enabled="true"
292-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
293            android:exported="true"
293-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
294            android:permission="android.permission.DUMP" >
294-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
295            <intent-filter>
295-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
296                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
296-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
296-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
297            </intent-filter>
298            <intent-filter>
298-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
299                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
299-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
299-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
300            </intent-filter>
301            <intent-filter>
301-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
302                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
302-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
303            </intent-filter>
304            <intent-filter>
304-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
305                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
305-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
305-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\946aa06de75d14ea26f920ab227faf40\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
306            </intent-filter>
307        </receiver>
308
309        <service
309-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
310            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
310-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
311            android:exported="false" >
311-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
312            <meta-data
312-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
313                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
313-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
314                android:value="cct" />
314-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\50b096f82fd87f48a167b59ecf6ffa92\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
315        </service>
316        <service
316-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
317            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
317-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
318            android:exported="false"
318-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
319            android:permission="android.permission.BIND_JOB_SERVICE" >
319-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
320        </service>
321
322        <receiver
322-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
323            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
323-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
324            android:exported="false" />
324-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3942b60061f62b5cb1c0e51487abeae9\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
325    </application>
326
327</manifest>
