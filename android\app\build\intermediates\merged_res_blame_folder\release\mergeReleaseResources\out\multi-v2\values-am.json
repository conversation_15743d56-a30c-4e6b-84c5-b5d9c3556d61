{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,261,338,430,526,608,686,769,851,929,1007,1088,1158,1241,1314,1387,1459,1539,1604", "endColumns": "72,82,76,91,95,81,77,82,81,77,77,80,69,82,72,72,71,79,64,115", "endOffsets": "173,256,333,425,521,603,681,764,846,924,1002,1083,1153,1236,1309,1382,1454,1534,1599,1715"}, "to": {"startLines": "29,39,40,61,63,64,83,84,85,86,87,88,91,92,93,94,95,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2723,3732,3815,6100,6279,6375,8486,8564,8647,8729,8807,8885,9160,9230,9313,9386,9459,9632,9712,9777", "endColumns": "72,82,76,91,95,81,77,82,81,77,77,80,69,82,72,72,71,79,64,115", "endOffsets": "2791,3810,3887,6187,6370,6452,8559,8642,8724,8802,8880,8961,9225,9308,9381,9454,9526,9707,9772,9888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3892,3994,4133,4255,4357,4484,4607,4715,4949,5077,5180,5325,5448,5583,5710,5770,5827", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "3989,4128,4250,4352,4479,4602,4710,4812,5072,5175,5320,5443,5578,5705,5765,5822,5893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "127,214", "endColumns": "86,85", "endOffsets": "209,295"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "9893,9980", "endColumns": "86,85", "endOffsets": "9975,10061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,196,304,410,497,629,729,834,941,1053,1169,1289,1405,1520,1612,1755,1866,1998,2123,2228,2342,2434,2558,2641,2757,2851,2978", "endColumns": "140,107,105,86,131,99,104,106,111,115,119,115,114,91,142,110,131,124,104,113,91,123,82,115,93,126,98", "endOffsets": "191,299,405,492,624,724,829,936,1048,1164,1284,1400,1515,1607,1750,1861,1993,2118,2223,2337,2429,2553,2636,2752,2846,2973,3072"}, "to": {"startLines": "30,31,59,62,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,89,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2796,2937,5898,6192,6457,6589,6987,7092,7199,7311,7427,7547,7663,7778,7870,8013,8124,8256,8381,8966,10066,10158,10282,10365,10481,10575,10702", "endColumns": "140,107,105,86,131,99,104,106,111,115,119,115,114,91,142,110,131,124,104,113,91,123,82,115,93,126,98", "endOffsets": "2932,3040,5999,6274,6584,6684,7087,7194,7306,7422,7542,7658,7773,7865,8008,8119,8251,8376,8481,9075,10153,10277,10360,10476,10570,10697,10796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "32,33,34,35,36,37,38,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3045,3138,3238,3335,3434,3530,3632,9531", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3133,3233,3330,3429,3525,3627,3727,9627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,9080", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,9155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "60,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6004,6689,6784,6890", "endColumns": "95,94,105,96", "endOffsets": "6095,6779,6885,6982"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "4817", "endColumns": "131", "endOffsets": "4944"}}]}]}