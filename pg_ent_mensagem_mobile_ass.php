<?php

include_once "./interface/web/paginaPadrao.php";
include_once "./logica/log/registro_log_geral.php";
include_once "./logica/extras/string.php";
include_once "./bd/bd.php";
include_once "./bd/sql.php";
include_once "./bd/sqlLog.php";

/**
 * Enter description here...
 * Cria��o da constante que limite a quantidade de tokens por envio
 */

define('_LIMITE_ENVIOS_', 99);
define('_QTD_CARACTERES_', 500);
define('_QTD_CARACTERES_TITULO_', 50);
define('_METHOD_', 'POST');

class mensagemMobileAss extends PaginaPadrao {
	public $bd;
	public $sql;
	private $tituloMensagem = null;
	private $mensagem = null;
	private $codent = null;

	function __construct($VARS) {
		$this->inicializa_pagina("ent_mensagem_mobile_ass");
		$this->showLoading();
		$this->tituloMensagem = "ASS - Mensagem mobile (Android)";
		$this->confere_autenticacao(array('entidade'));
		$this->codent = $this->sessao->get('CodEnt');
		$this->bd = new BD();
		$this->sql = new SQL();

		if (isset($VARS['modo']) == '1') {
			$this->tituloMensagem   = isset($_REQUEST['titulo']) ? $_REQUEST['titulo'] : null;
			$this->mensagem = isset($_REQUEST['msgAss']) ? $_REQUEST['msgAss'] : null;
			$this->enviarMensagem();
		}

		$this->montaPagina();
		$this->finaliza_pagina();
		$this->imprime_pagina();
	}

	private function montaPagina() {
		$this->template->associa('**METHOD**', _METHOD_);
		$this->template->associa('**MODO**', 0);
		$this->template->associa('**QTD_CARACTERES**', _QTD_CARACTERES_);
		$this->template->associa('**QTD_CARACTERES_TITULO**', _QTD_CARACTERES_TITULO_);
		$this->template->associa('**C2**', '0/'._QTD_CARACTERES_);
		//$this->template->associa('**QTD_ASSOCIADOS_VERSAO**', $this->getAssociadosUltimaVersaoApp());
	}

	function enviarMensagem() {
		include_once('./logica/elementos/sendAndroidPushNotification.php');
		$this->bd->consulta($this->sql->getTokensMobileAssociado($this->codent));
		$tokens = array();

		while ($registro = $this->bd->proximo_registro()) {
			$tokens[] = $registro['token'];
		}

		if (count($tokens > _LIMITE_ENVIOS_)) {
			$tokensLimitados = array_chunk($tokens, _LIMITE_ENVIOS_);
		}


		foreach ($tokensLimitados as $tokens) {
			$push = new sendAndroidPushNotification($this->tituloMensagem, $this->mensagem, $tokens);
			$push->send();

			if ($push->result->success >= 1) {
				$log = new RegistroLogGeral();
				$log->registra_operacao('mensagem_ass', $this->codent, null, null, $this->sessao->get('CodUsr'), 'Tipo: Mobile, Titulo: '.str_replace("'", "�", $this->tituloMensagem).', Mensagem: ' .str_replace("'", "�", $this->mensagem));

			}
		}

		echo "<script>alert('**ATEN��O**\\n\\nMensagem enviada com sucesso!'); window.open('./tecbiz.php?a=553223', '_self');</script>";
	}

	function getAssociadosUltimaVersaoApp() {
		$this->bd->consulta('select count(cod_associado) as quantidade from tbz_token_app_ass where cod_entidade = '.$this->codent);
		$r = $this->bd->proximo_registro();
		return $r['quantidade'];
	}
}

new mensagemMobileAss($_REQUEST);