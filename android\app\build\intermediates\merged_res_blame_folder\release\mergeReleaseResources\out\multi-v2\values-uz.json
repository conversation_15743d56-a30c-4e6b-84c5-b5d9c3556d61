{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,336,450,540,698,809,922,1042,1173,1301,1448,1580,1725,1822,1983,2116,2255,2395,2536,2662,2761,2882,2974,3096,3197,3320", "endColumns": "158,121,113,89,157,110,112,119,130,127,146,131,144,96,160,132,138,139,140,125,98,120,91,121,100,122,91", "endOffsets": "209,331,445,535,693,804,917,1037,1168,1296,1443,1575,1720,1817,1978,2111,2250,2390,2531,2657,2756,2877,2969,3091,3192,3315,3407"}, "to": {"startLines": "30,31,59,62,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,89,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2862,3021,6304,6640,6933,7091,7527,7640,7760,7891,8019,8166,8298,8443,8540,8701,8834,8973,9113,9767,10941,11040,11161,11253,11375,11476,11599", "endColumns": "158,121,113,89,157,110,112,119,130,127,146,131,144,96,160,132,138,139,140,125,98,120,91,121,100,122,91", "endOffsets": "3016,3138,6413,6725,7086,7197,7635,7755,7886,8014,8161,8293,8438,8535,8696,8829,8968,9108,9249,9888,11035,11156,11248,11370,11471,11594,11686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4063,4168,4318,4447,4556,4701,4834,4954,5210,5382,5490,5649,5781,5935,6097,6163,6224", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "4163,4313,4442,4551,4696,4829,4949,5055,5377,5485,5644,5776,5930,6092,6158,6219,6299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "60,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6418,7202,7311,7421", "endColumns": "117,108,109,105", "endOffsets": "6531,7306,7416,7522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,290,375,479,586,682,765,855,948,1031,1112,1195,1269,1354,1430,1505,1578,1661,1729", "endColumns": "85,98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "186,285,370,474,581,677,760,850,943,1026,1107,1190,1264,1349,1425,1500,1573,1656,1724,1841"}, "to": {"startLines": "29,39,40,61,63,64,83,84,85,86,87,88,91,92,93,94,95,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2776,3879,3978,6536,6730,6837,9254,9337,9427,9520,9603,9684,9977,10051,10136,10212,10287,10461,10544,10612", "endColumns": "85,98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "2857,3973,4058,6635,6832,6928,9332,9422,9515,9598,9679,9762,10046,10131,10207,10282,10355,10539,10607,10724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,9893", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,9972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "140,251", "endColumns": "110,100", "endOffsets": "246,347"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "10729,10840", "endColumns": "110,100", "endOffsets": "10835,10936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "32,33,34,35,36,37,38,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3143,3245,3347,3448,3548,3656,3760,10360", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3240,3342,3443,3543,3651,3755,3874,10456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5060", "endColumns": "149", "endOffsets": "5205"}}]}]}