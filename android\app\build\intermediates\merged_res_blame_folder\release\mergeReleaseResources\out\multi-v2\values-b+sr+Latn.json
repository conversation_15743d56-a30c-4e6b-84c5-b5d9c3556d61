{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,9821", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,9903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "32,33,34,35,36,37,38,96", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3233,3331,3433,3530,3634,3738,3843,10304", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3326,3428,3525,3629,3733,3838,3954,10400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "60,67,68,69", "startColumns": "4,4,4,4", "startOffsets": "6429,7192,7292,7405", "endColumns": "110,99,112,97", "endOffsets": "6535,7287,7400,7498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,293,380,477,578,664,741,832,924,1009,1089,1174,1247,1337,1414,1493,1570,1649,1719", "endColumns": "90,96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "191,288,375,472,573,659,736,827,919,1004,1084,1169,1242,1332,1409,1488,1565,1644,1714,1832"}, "to": {"startLines": "29,39,40,61,63,64,83,84,85,86,87,88,91,92,93,94,95,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2836,3959,4056,6540,6730,6831,9175,9252,9343,9435,9520,9600,9908,9981,10071,10148,10227,10405,10484,10554", "endColumns": "90,96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "2922,4051,4138,6632,6826,6912,9247,9338,9430,9515,9595,9680,9976,10066,10143,10222,10299,10479,10549,10667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "145,235", "endColumns": "89,91", "endOffsets": "230,322"}, "to": {"startLines": "100,101", "startColumns": "4,4", "startOffsets": "10672,10762", "endColumns": "89,91", "endOffsets": "10757,10849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4143,4250,4408,4535,4645,4799,4926,5038,5270,5419,5526,5686,5813,5962,6105,6173,6238", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "4245,4403,4530,4640,4794,4921,5033,5135,5414,5521,5681,5808,5957,6100,6168,6233,6313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "49", "startColumns": "4", "startOffsets": "5140", "endColumns": "129", "endOffsets": "5265"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,361,472,565,717,840,953,1074,1208,1339,1466,1597,1732,1832,1999,2114,2253,2386,2512,2648,2747,2886,2974,3117,3220,3363", "endColumns": "172,132,110,92,151,122,112,120,133,130,126,130,134,99,166,114,138,132,125,135,98,138,87,142,102,142,108", "endOffsets": "223,356,467,560,712,835,948,1069,1203,1334,1461,1592,1727,1827,1994,2109,2248,2381,2507,2643,2742,2881,2969,3112,3215,3358,3467"}, "to": {"startLines": "30,31,59,62,65,66,70,71,72,73,74,75,76,77,78,79,80,81,82,89,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2927,3100,6318,6637,6917,7069,7503,7616,7737,7871,8002,8129,8260,8395,8495,8662,8777,8916,9049,9685,10854,10953,11092,11180,11323,11426,11569", "endColumns": "172,132,110,92,151,122,112,120,133,130,126,130,134,99,166,114,138,132,125,135,98,138,87,142,102,142,108", "endOffsets": "3095,3228,6424,6725,7064,7187,7611,7732,7866,7997,8124,8255,8390,8490,8657,8772,8911,9044,9170,9816,10948,11087,11175,11318,11421,11564,11673"}}]}]}