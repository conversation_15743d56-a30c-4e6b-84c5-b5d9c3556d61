{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,193,285,368,465,564,649,725,821,908,997,1078,1161,1238,1324,1399,1471,1542,1626,1696", "endColumns": "87,91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "188,280,363,460,559,644,720,816,903,992,1073,1156,1233,1319,1394,1466,1537,1621,1691,1811"}, "to": {"startLines": "30,40,41,63,65,66,88,89,92,93,96,97,102,103,107,110,112,117,118,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2903,4027,4119,6792,6980,7079,9669,9745,9991,10078,10330,10411,10859,10936,11261,11497,11640,12049,12133,12288", "endColumns": "87,91,82,96,98,84,75,95,86,88,80,82,76,85,74,71,70,83,69,119", "endOffsets": "2986,4114,4197,6884,7074,7159,9740,9836,10073,10162,10406,10489,10931,11017,11331,11564,11706,12128,12198,12403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,225,357,471,562,713,826,938,1059,1197,1331,1460,1588,1730,1828,1991,2116,2256,2402,2528,2660,2770,2918,3009,3135,3238,3379", "endColumns": "169,131,113,90,150,112,111,120,137,133,128,127,141,97,162,124,139,145,125,131,109,147,90,125,102,140,109", "endOffsets": "220,352,466,557,708,821,933,1054,1192,1326,1455,1583,1725,1823,1986,2111,2251,2397,2523,2655,2765,2913,3004,3130,3233,3374,3484"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,99,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,3161,6575,6889,7164,7315,7739,7851,7972,8110,8244,8373,8501,8643,8741,8904,9029,9169,9315,10566,12654,12764,12912,13003,13129,13232,13373", "endColumns": "169,131,113,90,150,112,111,120,137,133,128,127,141,97,162,124,139,145,125,131,109,147,90,125,102,140,109", "endOffsets": "3156,3288,6684,6975,7310,7423,7846,7967,8105,8239,8368,8496,8638,8736,8899,9024,9164,9310,9436,10693,12759,12907,12998,13124,13227,13368,13478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "142,228", "endColumns": "85,85", "endOffsets": "223,309"}, "to": {"startLines": "122,123", "startColumns": "4,4", "startOffsets": "12482,12568", "endColumns": "85,85", "endOffsets": "12563,12649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,274,348,434,508,584,668,747,819,897,975,1049,1136,1220,1297,1368,1438,1527,1605,1690", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "126,201,269,343,429,503,579,663,742,814,892,970,1044,1131,1215,1292,1363,1433,1522,1600,1685,1759"}, "to": {"startLines": "29,42,85,86,87,90,91,94,95,98,100,104,105,106,108,109,111,113,114,116,119,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2827,4202,9441,9509,9583,9841,9915,10167,10251,10494,10698,11022,11100,11174,11336,11420,11569,11711,11781,11971,12203,12408", "endColumns": "75,74,67,73,85,73,75,83,78,71,77,77,73,86,83,76,70,69,88,77,84,73", "endOffsets": "2898,4272,9504,9578,9664,9910,9986,10246,10325,10561,10771,11095,11169,11256,11415,11492,11635,11776,11865,12044,12283,12477"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4277,4385,4536,4664,4775,4942,5069,5192,5441,5619,5725,5894,6020,6183,6365,6433,6496", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "4380,4531,4659,4770,4937,5064,5187,5293,5614,5720,5889,6015,6178,6360,6428,6491,6570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "33,34,35,36,37,38,39,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3293,3395,3497,3597,3697,3804,3908,11870", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3390,3492,3592,3692,3799,3903,4022,11966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,10776", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,10854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5298", "endColumns": "142", "endOffsets": "5436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6689,7428,7529,7640", "endColumns": "102,100,110,98", "endOffsets": "6787,7524,7635,7734"}}]}]}