{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,291,377,478,583,675,756,850,939,1029,1110,1192,1267,1356,1431,1509,1584,1663,1733", "endColumns": "86,98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "187,286,372,473,578,670,751,845,934,1024,1105,1187,1262,1351,1426,1504,1579,1658,1728,1851"}, "to": {"startLines": "30,40,41,63,65,66,88,89,94,95,98,99,104,105,109,112,114,119,120,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2960,4061,4160,6925,7117,7222,9994,10075,10468,10557,10823,10904,11359,11434,11779,12017,12166,12572,12651,12801", "endColumns": "86,98,85,100,104,91,80,93,88,89,80,81,74,88,74,77,74,78,69,122", "endOffsets": "3042,4155,4241,7021,7217,7309,10070,10164,10552,10642,10899,10981,11429,11518,11849,12090,12236,12646,12716,12919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "141,239", "endColumns": "97,101", "endOffsets": "234,336"}, "to": {"startLines": "125,126", "startColumns": "4,4", "startOffsets": "13080,13178", "endColumns": "97,101", "endOffsets": "13173,13275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "51", "startColumns": "4", "startOffsets": "5401", "endColumns": "163", "endOffsets": "5560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,211,334,451,542,700,822,950,1070,1222,1375,1515,1653,1806,1909,2086,2227,2370,2517,2650,2783,2879,3011,3099,3223,3333,3479", "endColumns": "155,122,116,90,157,121,127,119,151,152,139,137,152,102,176,140,142,146,132,132,95,131,87,123,109,145,104", "endOffsets": "206,329,446,537,695,817,945,1065,1217,1370,1510,1648,1801,1904,2081,2222,2365,2512,2645,2778,2874,3006,3094,3218,3328,3474,3579"}, "to": {"startLines": "31,32,61,64,67,68,72,73,74,75,76,77,78,79,80,81,82,83,84,101,127,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3047,3203,6698,7026,7314,7472,7936,8064,8184,8336,8489,8629,8767,8920,9023,9200,9341,9484,9631,11058,13280,13376,13508,13596,13720,13830,13976", "endColumns": "155,122,116,90,157,121,127,119,151,152,139,137,152,102,176,140,142,146,132,132,95,131,87,123,109,145,104", "endOffsets": "3198,3321,6810,7112,7467,7589,8059,8179,8331,8484,8624,8762,8915,9018,9195,9336,9479,9626,9759,11186,13371,13503,13591,13715,13825,13971,14076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,11273", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,11354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "43,44,45,46,47,48,49,50,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4332,4443,4629,4761,4872,5053,5178,5296,5565,5754,5862,6032,6161,6339,6484,6553,6615", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "4438,4624,4756,4867,5048,5173,5291,5396,5749,5857,6027,6156,6334,6479,6548,6610,6693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,291,361,445,517,585,663,744,828,920,992,1074,1161,1245,1330,1413,1493,1564,1634,1722,1794,1874,1948", "endColumns": "73,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "124,210,286,356,440,512,580,658,739,823,915,987,1069,1156,1240,1325,1408,1488,1559,1629,1717,1789,1869,1943,2025"}, "to": {"startLines": "29,42,85,86,87,90,91,92,93,96,97,100,102,106,107,108,110,111,113,115,116,118,121,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2886,4246,9764,9840,9910,10169,10241,10309,10387,10647,10731,10986,11191,11523,11610,11694,11854,11937,12095,12241,12311,12500,12721,12924,12998", "endColumns": "73,85,75,69,83,71,67,77,80,83,91,71,81,86,83,84,82,79,70,69,87,71,79,73,81", "endOffsets": "2955,4327,9835,9905,9989,10236,10304,10382,10463,10726,10818,11053,11268,11605,11689,11774,11932,12012,12161,12306,12394,12567,12796,12993,13075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "33,34,35,36,37,38,39,117", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3326,3424,3527,3627,3730,3838,3944,12399", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "3419,3522,3622,3725,3833,3939,4056,12495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "62,69,70,71", "startColumns": "4,4,4,4", "startOffsets": "6815,7594,7701,7826", "endColumns": "109,106,124,109", "endOffsets": "6920,7696,7821,7931"}}]}]}