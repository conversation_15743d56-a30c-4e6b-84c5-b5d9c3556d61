{"logs": [{"outputFile": "com.tecbiz.tecbizassociadospush.app-mergeReleaseResources-68:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bf76bafeaa4e4c4b3789cb70960f60cb\\transformed\\biometric-1.2.0-alpha04\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,345,460,550,718,851,966,1090,1219,1352,1488,1607,1746,1841,2003,2130,2279,2421,2549,2674,2775,2911,3019,3162,3264,3401", "endColumns": "160,128,114,89,167,132,114,123,128,132,135,118,138,94,161,126,148,141,127,124,100,135,107,142,101,136,102", "endOffsets": "211,340,455,545,713,846,961,1085,1214,1347,1483,1602,1741,1836,1998,2125,2274,2416,2544,2669,2770,2906,3014,3157,3259,3396,3499"}, "to": {"startLines": "30,31,60,63,66,67,71,72,73,74,75,76,77,78,79,80,81,82,83,94,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2875,3036,6363,6683,6962,7130,7580,7695,7819,7948,8081,8217,8336,8475,8570,8732,8859,9008,9150,10083,11374,11475,11611,11719,11862,11964,12101", "endColumns": "160,128,114,89,167,132,114,123,128,132,135,118,138,94,161,126,148,141,127,124,100,135,107,142,101,136,102", "endOffsets": "3031,3160,6473,6768,7125,7258,7690,7814,7943,8076,8212,8331,8470,8565,8727,8854,9003,9145,9273,10203,11470,11606,11714,11857,11959,12096,12199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\bc31118459fe5c176424d734f980c964\\transformed\\play-services-base-18.5.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4146,4249,4402,4528,4634,4774,4900,5023,5296,5461,5567,5724,5853,6006,6163,6226,6285", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4244,4397,4523,4629,4769,4895,5018,5127,5456,5562,5719,5848,6001,6158,6221,6280,6358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\c4d48eeb7f4b99a4a4e7d71de689573a\\transformed\\ui-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,277,360,465,567,654,735,828,918,1000,1083,1168,1241,1327,1401,1477,1551,1627,1697", "endColumns": "78,92,82,104,101,86,80,92,89,81,82,84,72,85,73,75,73,75,69,117", "endOffsets": "179,272,355,460,562,649,730,823,913,995,1078,1163,1236,1322,1396,1472,1546,1622,1692,1810"}, "to": {"startLines": "29,39,40,62,64,65,86,87,90,91,92,93,96,97,98,99,101,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2796,3884,3977,6578,6773,6875,9431,9512,9743,9833,9915,9998,10290,10363,10449,10523,10674,10922,10998,11068", "endColumns": "78,92,82,104,101,86,80,92,89,81,82,84,72,85,73,75,73,75,69,117", "endOffsets": "2870,3972,4055,6678,6870,6957,9507,9600,9828,9910,9993,10078,10358,10444,10518,10594,10743,10993,11063,11181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7ae61c3b313c8db9701f97884214dd26\\transformed\\browser-1.6.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "61,68,69,70", "startColumns": "4,4,4,4", "startOffsets": "6478,7263,7367,7475", "endColumns": "99,103,107,104", "endOffsets": "6573,7362,7470,7575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\140c13111513dc6f8651ec35200cff0b\\transformed\\play-services-basement-18.4.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "5132", "endColumns": "163", "endOffsets": "5291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\0a2a6591dced4295b60b85002f8c8409\\transformed\\appcompat-1.7.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,10208", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,10285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\d6997bfbb77c616381810af9ce1fe1f5\\transformed\\react-android-0.81.4-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,211,294,364,432,507", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "136,206,289,359,427,502,575"}, "to": {"startLines": "41,84,85,88,89,100,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4060,9278,9348,9605,9675,10599,10748", "endColumns": "85,69,82,69,67,74,72", "endOffsets": "4141,9343,9426,9670,9738,10669,10816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\f868bcdea222abfc5b941e0bc81954be\\transformed\\foundation-release\\res\\values-kk\\values-kk.xml", "from": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "133,225", "endColumns": "91,95", "endOffsets": "220,316"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11186,11278", "endColumns": "91,95", "endOffsets": "11273,11369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7a80f6d275b4d3310d028e93bbd2adb7\\transformed\\core-1.15.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,359,462,566,663,774", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "150,252,354,457,561,658,769,870"}, "to": {"startLines": "32,33,34,35,36,37,38,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3165,3265,3367,3469,3572,3676,3773,10821", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "3260,3362,3464,3567,3671,3768,3879,10917"}}]}]}